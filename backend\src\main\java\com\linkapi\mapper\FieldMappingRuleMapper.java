package com.linkapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linkapi.entity.FieldMappingRule;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 字段映射规则Mapper接口
 * 
 * <AUTHOR> API Team
 */
@Mapper
public interface FieldMappingRuleMapper extends BaseMapper<FieldMappingRule> {

    /**
     * 根据配置ID查询规则列表
     */
    @Select("SELECT * FROM field_mapping_rule WHERE deleted = 0 AND config_id = #{configId} AND is_enabled = 1 ORDER BY sort_order ASC, created_time ASC")
    List<FieldMappingRule> selectByConfigId(@Param("configId") Long configId);

    /**
     * 根据配置ID查询所有规则（包括禁用的）
     */
    @Select("SELECT * FROM field_mapping_rule WHERE deleted = 0 AND config_id = #{configId} ORDER BY sort_order ASC, created_time ASC")
    List<FieldMappingRule> selectAllByConfigId(@Param("configId") Long configId);

    /**
     * 根据源字段名查询规则
     */
    @Select("SELECT * FROM field_mapping_rule WHERE deleted = 0 AND config_id = #{configId} AND source_field = #{sourceField} AND is_enabled = 1")
    FieldMappingRule selectBySourceField(@Param("configId") Long configId, @Param("sourceField") String sourceField);

    /**
     * 根据目标字段名查询规则
     */
    @Select("SELECT * FROM field_mapping_rule WHERE deleted = 0 AND config_id = #{configId} AND target_field = #{targetField} AND is_enabled = 1")
    FieldMappingRule selectByTargetField(@Param("configId") Long configId, @Param("targetField") String targetField);

    /**
     * 查询必填字段规则
     */
    @Select("SELECT * FROM field_mapping_rule WHERE deleted = 0 AND config_id = #{configId} AND is_required = 1 AND is_enabled = 1 ORDER BY sort_order ASC")
    List<FieldMappingRule> selectRequiredRules(@Param("configId") Long configId);

    /**
     * 批量插入规则
     */
    @Insert({
        "<script>",
        "INSERT INTO field_mapping_rule (config_id, source_field, source_field_label, target_field, target_field_label, ",
        "field_type, is_required, default_value, transform_type, transform_expression, validation_rules, ",
        "field_description, sort_order, is_enabled, created_time, updated_time) VALUES ",
        "<foreach collection='rules' item='rule' separator=','>",
        "(#{rule.configId}, #{rule.sourceField}, #{rule.sourceFieldLabel}, #{rule.targetField}, #{rule.targetFieldLabel}, ",
        "#{rule.fieldType}, #{rule.isRequired}, #{rule.defaultValue}, #{rule.transformType}, #{rule.transformExpression}, ",
        "#{rule.validationRules}, #{rule.fieldDescription}, #{rule.sortOrder}, #{rule.isEnabled}, NOW(), NOW())",
        "</foreach>",
        "</script>"
    })
    int batchInsert(@Param("rules") List<FieldMappingRule> rules);

    /**
     * 批量更新规则启用状态
     */
    @Update({
        "<script>",
        "UPDATE field_mapping_rule SET is_enabled = #{isEnabled}, updated_time = NOW() ",
        "WHERE deleted = 0 AND id IN ",
        "<foreach collection='ids' item='id' open='(' separator=',' close=')'>",
        "#{id}",
        "</foreach>",
        "</script>"
    })
    int batchUpdateEnabled(@Param("ids") List<Long> ids, @Param("isEnabled") Boolean isEnabled);

    /**
     * 根据配置ID删除所有规则
     */
    @Update("UPDATE field_mapping_rule SET deleted = 1, updated_time = NOW() WHERE config_id = #{configId}")
    int deleteByConfigId(@Param("configId") Long configId);

    /**
     * 更新规则排序
     */
    @Update({
        "<script>",
        "UPDATE field_mapping_rule SET sort_order = CASE id ",
        "<foreach collection='rules' item='rule'>",
        "WHEN #{rule.id} THEN #{rule.sortOrder} ",
        "</foreach>",
        "END, updated_time = NOW() ",
        "WHERE id IN ",
        "<foreach collection='rules' item='rule' open='(' separator=',' close=')'>",
        "#{rule.id}",
        "</foreach>",
        "</script>"
    })
    int batchUpdateSortOrder(@Param("rules") List<FieldMappingRule> rules);

    /**
     * 复制规则到新配置
     */
    @Insert({
        "INSERT INTO field_mapping_rule (config_id, source_field, source_field_label, target_field, target_field_label, ",
        "field_type, is_required, default_value, transform_type, transform_expression, validation_rules, ",
        "field_description, sort_order, is_enabled, created_time, updated_time) ",
        "SELECT #{newConfigId}, source_field, source_field_label, target_field, target_field_label, ",
        "field_type, is_required, default_value, transform_type, transform_expression, validation_rules, ",
        "field_description, sort_order, is_enabled, NOW(), NOW() ",
        "FROM field_mapping_rule WHERE deleted = 0 AND config_id = #{sourceConfigId}"
    })
    int copyRulesToNewConfig(@Param("sourceConfigId") Long sourceConfigId, @Param("newConfigId") Long newConfigId);

    /**
     * 统计配置的规则数量
     */
    @Select("SELECT COUNT(*) FROM field_mapping_rule WHERE deleted = 0 AND config_id = #{configId}")
    int countByConfigId(@Param("configId") Long configId);

    /**
     * 统计启用的规则数量
     */
    @Select("SELECT COUNT(*) FROM field_mapping_rule WHERE deleted = 0 AND config_id = #{configId} AND is_enabled = 1")
    int countEnabledByConfigId(@Param("configId") Long configId);

    /**
     * 检查字段名是否重复
     */
    @Select("SELECT COUNT(*) FROM field_mapping_rule WHERE deleted = 0 AND config_id = #{configId} AND source_field = #{sourceField} AND id != #{excludeId}")
    int countBySourceField(@Param("configId") Long configId, @Param("sourceField") String sourceField, @Param("excludeId") Long excludeId);

    /**
     * 检查目标字段名是否重复
     */
    @Select("SELECT COUNT(*) FROM field_mapping_rule WHERE deleted = 0 AND config_id = #{configId} AND target_field = #{targetField} AND id != #{excludeId}")
    int countByTargetField(@Param("configId") Long configId, @Param("targetField") String targetField, @Param("excludeId") Long excludeId);

    /**
     * 获取配置中的最大排序号
     */
    @Select("SELECT COALESCE(MAX(sort_order), 0) FROM field_mapping_rule WHERE deleted = 0 AND config_id = #{configId}")
    int getMaxSortOrder(@Param("configId") Long configId);

    /**
     * 根据字段类型统计规则数量
     */
    @Select({
        "SELECT field_type, COUNT(*) as count ",
        "FROM field_mapping_rule ",
        "WHERE deleted = 0 AND config_id = #{configId} ",
        "GROUP BY field_type"
    })
    List<FieldTypeStatistics> getFieldTypeStatistics(@Param("configId") Long configId);

    /**
     * 字段类型统计
     */
    class FieldTypeStatistics {
        private FieldMappingRule.FieldType fieldType;
        private Long count;

        // Getters and Setters
        public FieldMappingRule.FieldType getFieldType() { return fieldType; }
        public void setFieldType(FieldMappingRule.FieldType fieldType) { this.fieldType = fieldType; }

        public Long getCount() { return count; }
        public void setCount(Long count) { this.count = count; }
    }
}
