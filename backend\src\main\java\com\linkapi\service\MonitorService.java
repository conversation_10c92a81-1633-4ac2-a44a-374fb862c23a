package com.linkapi.service;

import java.util.Map;

/**
 * 系统监控服务接口
 * 提供系统性能监控、服务状态检查等功能
 */
public interface MonitorService {

    /**
     * 获取系统信息
     * @return 系统基本信息和性能指标
     */
    Map<String, Object> getSystemInfo();

    /**
     * 获取应用信息
     * @return 应用运行状态和统计信息
     */
    Map<String, Object> getApplicationInfo();

    /**
     * 获取服务状态列表
     * @return 各个服务的运行状态
     */
    Object getServiceStatus();

    /**
     * 检查指定服务状态
     * @param serviceName 服务名称
     * @return 服务检查结果
     */
    Map<String, Object> checkService(String serviceName);

    /**
     * 获取实时性能数据
     * @param type 数据类型 (cpu, memory, network)
     * @return 实时性能数据
     */
    Object getRealtimeData(String type);
}