<template>
  <div class="file-upload">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>Excel 文件上传</span>
          <el-button type="primary" @click="downloadTemplate">
            下载模板
          </el-button>
        </div>
      </template>
      
      <!-- 上传区域 -->
      <div class="upload-area">
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :action="uploadUrl"
          :headers="uploadHeaders"
          :before-upload="beforeUpload"
          :on-success="handleSuccess"
          :on-error="handleError"
          :on-progress="handleProgress"
          :file-list="fileList"
          :limit="1"
          :auto-upload="false"
          accept=".xlsx,.xls"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将 Excel 文件拖拽到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 .xlsx 和 .xls 格式，文件大小不超过 10MB
            </div>
          </template>
        </el-upload>
        
        <!-- 上传按钮 -->
        <div class="upload-actions" v-if="fileList.length > 0">
          <el-button type="primary" @click="submitUpload" :loading="uploading">
            {{ uploading ? '上传中...' : '开始上传' }}
          </el-button>
          <el-button @click="clearFiles">清空文件</el-button>
        </div>
      </div>
      
      <!-- 上传进度 -->
      <div class="progress-area" v-if="uploadProgress > 0">
        <el-progress :percentage="uploadProgress" :status="progressStatus" />
      </div>
      
      <!-- 解析结果 -->
      <div class="result-area" v-if="parseResult">
        <el-alert
          :title="parseResult.success ? '解析成功' : '解析失败'"
          :type="parseResult.success ? 'success' : 'error'"
          :description="parseResult.message"
          show-icon
          :closable="false"
        />
        
        <!-- 成功统计 -->
        <div class="success-stats" v-if="parseResult.success">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-statistic title="总行数" :value="parseResult.totalRows" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="成功解析" :value="parseResult.successRows" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="失败行数" :value="parseResult.errorRows" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="生成单据" :value="parseResult.documentCount" />
            </el-col>
          </el-row>
        </div>
        
        <!-- 错误详情 -->
        <div class="error-details" v-if="parseResult.errors && parseResult.errors.length > 0">
          <el-collapse>
            <el-collapse-item title="错误详情" name="errors">
              <el-table :data="parseResult.errors" style="width: 100%">
                <el-table-column prop="row" label="行号" width="80" />
                <el-table-column prop="column" label="列" width="100" />
                <el-table-column prop="message" label="错误信息" />
                <el-table-column prop="value" label="原始值" width="150" />
              </el-table>
            </el-collapse-item>
          </el-collapse>
        </div>
        
        <!-- 操作按钮 -->
        <div class="result-actions" v-if="parseResult.success && parseResult.documentCount > 0">
          <el-button type="primary" @click="goToDocumentList">
            查看生成的单据
          </el-button>
          <el-button type="success" @click="batchValidate" :loading="validating">
            批量校验单据
          </el-button>
        </div>
      </div>
    </el-card>
    
    <!-- 上传历史 -->
    <el-card class="history-card">
      <template #header>
        <span>上传历史</span>
      </template>
      
      <el-table :data="uploadHistory" v-loading="historyLoading">
        <el-table-column prop="fileName" label="文件名" />
        <el-table-column prop="fileSize" label="文件大小" width="120">
          <template #default="scope">
            {{ formatFileSize(scope.row.fileSize) }}
          </template>
        </el-table-column>
        <el-table-column prop="totalRows" label="总行数" width="100" />
        <el-table-column prop="successRows" label="成功行数" width="100" />
        <el-table-column prop="errorRows" label="失败行数" width="100" />
        <el-table-column prop="documentCount" label="生成单据" width="100" />
        <el-table-column prop="status" label="状态" width="120">
          <template #default="scope">
            <el-tag :type="getStatusTag(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="uploadTime" label="上传时间" width="180" />
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button size="small" @click="viewUploadDetail(scope.row)">
              查看详情
            </el-button>
            <el-button size="small" type="danger" @click="deleteUploadRecord(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <el-pagination
        v-model:current-page="historyPagination.page"
        v-model:page-size="historyPagination.size"
        :page-sizes="[10, 20, 50]"
        :total="historyPagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleHistorySizeChange"
        @current-change="handleHistoryCurrentChange"
        style="margin-top: 20px; text-align: right;"
      />
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import type { UploadInstance, UploadProps, UploadRawFile } from 'element-plus'
import { request } from '@/utils/request'

interface ParseResult {
  success: boolean
  message: string
  totalRows: number
  successRows: number
  errorRows: number
  documentCount: number
  errors?: ParseError[]
}

interface ParseError {
  row: number
  column: string
  message: string
  value: string
}

interface UploadRecord {
  id: number
  fileName: string
  fileSize: number
  totalRows: number
  successRows: number
  errorRows: number
  documentCount: number
  status: string
  uploadTime: string
}

const router = useRouter()
const uploadRef = ref<UploadInstance>()

const uploading = ref(false)
const validating = ref(false)
const uploadProgress = ref(0)
const progressStatus = ref<'success' | 'exception' | undefined>()
const fileList = ref([])
const parseResult = ref<ParseResult>()
const uploadHistory = ref<UploadRecord[]>([])
const historyLoading = ref(false)

const uploadUrl = '/api/upload/excel'
const uploadHeaders = {
  'Authorization': `Bearer ${localStorage.getItem('token')}`
}

const historyPagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

const beforeUpload: UploadProps['beforeUpload'] = (rawFile: UploadRawFile) => {
  const isExcel = rawFile.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  rawFile.type === 'application/vnd.ms-excel'
  const isLt10M = rawFile.size / 1024 / 1024 < 10

  if (!isExcel) {
    ElMessage.error('只能上传 Excel 文件！')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB！')
    return false
  }
  return true
}

const handleProgress: UploadProps['onProgress'] = (evt) => {
  uploadProgress.value = Math.round(evt.percent || 0)
}

const handleSuccess: UploadProps['onSuccess'] = (response) => {
  uploading.value = false
  uploadProgress.value = 100
  progressStatus.value = 'success'
  parseResult.value = response.data
  
  ElMessage.success('文件上传成功')
  loadUploadHistory()
  
  // 清空文件列表
  setTimeout(() => {
    clearFiles()
  }, 2000)
}

const handleError: UploadProps['onError'] = (error) => {
  uploading.value = false
  progressStatus.value = 'exception'
  
  try {
    const errorData = JSON.parse(error.message)
    ElMessage.error(errorData.message || '上传失败')
  } catch {
    ElMessage.error('上传失败')
  }
}

const submitUpload = () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请先选择文件')
    return
  }
  
  uploading.value = true
  uploadProgress.value = 0
  progressStatus.value = undefined
  parseResult.value = undefined
  
  uploadRef.value?.submit()
}

const clearFiles = () => {
  uploadRef.value?.clearFiles()
  fileList.value = []
  uploadProgress.value = 0
  progressStatus.value = undefined
  parseResult.value = undefined
}

const downloadTemplate = async () => {
  try {
    const response = await request.get('/api/files/template', {
      responseType: 'blob'
    })
    
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '单据导入模板.xlsx'
    link.click()
    window.URL.revokeObjectURL(url)
    
    ElMessage.success('模板下载成功')
  } catch (error) {
    ElMessage.error('模板下载失败')
  }
}

const goToDocumentList = () => {
  router.push('/documents')
}

const batchValidate = async () => {
  try {
    validating.value = true
    await request.post('/api/documents/batch-validate-recent')
    ElMessage.success('批量校验任务已启动')
  } catch (error: any) {
    ElMessage.error(error.response?.data?.message || '批量校验失败')
  } finally {
    validating.value = false
  }
}

const loadUploadHistory = async () => {
  historyLoading.value = true
  try {
    const response = await request.get('/api/files/upload-history', {
      params: {
        page: historyPagination.page,
        size: historyPagination.size
      }
    })
    uploadHistory.value = response.data.records
    historyPagination.total = response.data.total
  } catch (error) {
    ElMessage.error('加载上传历史失败')
  } finally {
    historyLoading.value = false
  }
}

const viewUploadDetail = (record: UploadRecord) => {
  // 可以打开详情对话框或跳转到详情页面
  ElMessage.info('查看上传详情功能待实现')
}

const deleteUploadRecord = async (record: UploadRecord) => {
  try {
    await ElMessageBox.confirm('确定要删除这条上传记录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await request.delete(`/api/files/upload-history/${record.id}`)
    ElMessage.success('删除成功')
    loadUploadHistory()
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.response?.data?.message || '删除失败')
    }
  }
}

const handleHistorySizeChange = (size: number) => {
  historyPagination.size = size
  historyPagination.page = 1
  loadUploadHistory()
}

const handleHistoryCurrentChange = (page: number) => {
  historyPagination.page = page
  loadUploadHistory()
}

const formatFileSize = (size: number) => {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(1) + ' KB'
  } else {
    return (size / 1024 / 1024).toFixed(1) + ' MB'
  }
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    SUCCESS: '成功',
    FAILED: '失败',
    PROCESSING: '处理中'
  }
  return statusMap[status] || status
}

const getStatusTag = (status: string) => {
  const tagMap: Record<string, string> = {
    SUCCESS: 'success',
    FAILED: 'danger',
    PROCESSING: 'warning'
  }
  return tagMap[status] || ''
}

onMounted(() => {
  loadUploadHistory()
})
</script>

<style scoped>
.file-upload {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.upload-area {
  margin-bottom: 20px;
}

.upload-actions {
  text-align: center;
  margin-top: 20px;
}

.progress-area {
  margin: 20px 0;
}

.result-area {
  margin-top: 20px;
}

.success-stats {
  margin: 20px 0;
  padding: 20px;
  background-color: #f0f9ff;
  border-radius: 4px;
}

.error-details {
  margin: 20px 0;
}

.result-actions {
  text-align: center;
  margin-top: 20px;
}

.history-card {
  margin-top: 20px;
}

.upload-demo {
  width: 100%;
}
</style>