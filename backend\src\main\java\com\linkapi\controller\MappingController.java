package com.linkapi.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.linkapi.common.Result;
import com.linkapi.entity.CustomerMapping;
import com.linkapi.entity.ProductMapping;
import com.linkapi.entity.WarehouseMapping;
import com.linkapi.service.MappingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 映射控制器
 * 
 * <AUTHOR> API Team
 */
@Slf4j
@RestController
@RequestMapping("/mapping")
@RequiredArgsConstructor
public class MappingController {

    private final MappingService mappingService;

    // ==================== 产品映射 ====================

    /**
     * 分页查询产品映射
     */
    @GetMapping("/product")
    public Result<Map<String, Object>> getProductMappingList(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String sourceCode,
            @RequestParam(required = false) String targetCode) {
        
        try {
            IPage<ProductMapping> page = mappingService.getProductMappingPage(pageNum, pageSize, sourceCode, targetCode);
            
            Map<String, Object> data = new HashMap<>();
            data.put("list", page.getRecords());
            data.put("total", page.getTotal());
            data.put("pageNum", page.getCurrent());
            data.put("pageSize", page.getSize());
            data.put("pages", page.getPages());
            
            return Result.success("查询成功", data);
        } catch (Exception e) {
            log.error("查询产品映射列表失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 创建产品映射
     */
    @PostMapping("/product")
    public Result<ProductMapping> createProductMapping(@RequestBody ProductMapping mapping) {
        try {
            ProductMapping createdMapping = mappingService.createProductMapping(mapping);
            return Result.success("创建成功", createdMapping);
        } catch (Exception e) {
            log.error("创建产品映射失败", e);
            return Result.error("创建失败：" + e.getMessage());
        }
    }

    /**
     * 更新产品映射
     */
    @PutMapping("/product/{id}")
    public Result<ProductMapping> updateProductMapping(@PathVariable Long id, @RequestBody ProductMapping mapping) {
        try {
            mapping.setId(id);
            ProductMapping updatedMapping = mappingService.updateProductMapping(mapping);
            return Result.success("更新成功", updatedMapping);
        } catch (Exception e) {
            log.error("更新产品映射失败", e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除产品映射
     */
    @DeleteMapping("/product/{id}")
    public Result<String> deleteProductMapping(@PathVariable Long id) {
        try {
            boolean success = mappingService.deleteProductMapping(id);
            if (success) {
                return Result.success("删除成功", "success");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除产品映射失败", e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    // ==================== 客户映射 ====================

    /**
     * 分页查询客户映射
     */
    @GetMapping("/customer")
    public Result<Map<String, Object>> getCustomerMappingList(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String sourceCode,
            @RequestParam(required = false) String targetCode) {
        
        try {
            IPage<CustomerMapping> page = mappingService.getCustomerMappingPage(pageNum, pageSize, sourceCode, targetCode);
            
            Map<String, Object> data = new HashMap<>();
            data.put("list", page.getRecords());
            data.put("total", page.getTotal());
            data.put("pageNum", page.getCurrent());
            data.put("pageSize", page.getSize());
            data.put("pages", page.getPages());
            
            return Result.success("查询成功", data);
        } catch (Exception e) {
            log.error("查询客户映射列表失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 创建客户映射
     */
    @PostMapping("/customer")
    public Result<CustomerMapping> createCustomerMapping(@RequestBody CustomerMapping mapping) {
        try {
            CustomerMapping createdMapping = mappingService.createCustomerMapping(mapping);
            return Result.success("创建成功", createdMapping);
        } catch (Exception e) {
            log.error("创建客户映射失败", e);
            return Result.error("创建失败：" + e.getMessage());
        }
    }

    /**
     * 更新客户映射
     */
    @PutMapping("/customer/{id}")
    public Result<CustomerMapping> updateCustomerMapping(@PathVariable Long id, @RequestBody CustomerMapping mapping) {
        try {
            mapping.setId(id);
            CustomerMapping updatedMapping = mappingService.updateCustomerMapping(mapping);
            return Result.success("更新成功", updatedMapping);
        } catch (Exception e) {
            log.error("更新客户映射失败", e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除客户映射
     */
    @DeleteMapping("/customer/{id}")
    public Result<String> deleteCustomerMapping(@PathVariable Long id) {
        try {
            boolean success = mappingService.deleteCustomerMapping(id);
            if (success) {
                return Result.success("删除成功", "success");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除客户映射失败", e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    // ==================== 仓库映射 ====================

    /**
     * 分页查询仓库映射
     */
    @GetMapping("/warehouse")
    public Result<Map<String, Object>> getWarehouseMappingList(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String sourceCode,
            @RequestParam(required = false) String targetCode) {
        
        try {
            IPage<WarehouseMapping> page = mappingService.getWarehouseMappingPage(pageNum, pageSize, sourceCode, targetCode);
            
            Map<String, Object> data = new HashMap<>();
            data.put("list", page.getRecords());
            data.put("total", page.getTotal());
            data.put("pageNum", page.getCurrent());
            data.put("pageSize", page.getSize());
            data.put("pages", page.getPages());
            
            return Result.success("查询成功", data);
        } catch (Exception e) {
            log.error("查询仓库映射列表失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 创建仓库映射
     */
    @PostMapping("/warehouse")
    public Result<WarehouseMapping> createWarehouseMapping(@RequestBody WarehouseMapping mapping) {
        try {
            WarehouseMapping createdMapping = mappingService.createWarehouseMapping(mapping);
            return Result.success("创建成功", createdMapping);
        } catch (Exception e) {
            log.error("创建仓库映射失败", e);
            return Result.error("创建失败：" + e.getMessage());
        }
    }

    /**
     * 更新仓库映射
     */
    @PutMapping("/warehouse/{id}")
    public Result<WarehouseMapping> updateWarehouseMapping(@PathVariable Long id, @RequestBody WarehouseMapping mapping) {
        try {
            mapping.setId(id);
            WarehouseMapping updatedMapping = mappingService.updateWarehouseMapping(mapping);
            return Result.success("更新成功", updatedMapping);
        } catch (Exception e) {
            log.error("更新仓库映射失败", e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除仓库映射
     */
    @DeleteMapping("/warehouse/{id}")
    public Result<String> deleteWarehouseMapping(@PathVariable Long id) {
        try {
            boolean success = mappingService.deleteWarehouseMapping(id);
            if (success) {
                return Result.success("删除成功", "success");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除仓库映射失败", e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    // ==================== 统计信息 ====================

    /**
     * 获取映射统计信息
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getMappingStatistics() {
        try {
            MappingService.MappingStatistics statistics = mappingService.getMappingStatistics();
            
            Map<String, Object> data = new HashMap<>();
            data.put("productCount", statistics.getProductCount());
            data.put("customerCount", statistics.getCustomerCount());
            data.put("warehouseCount", statistics.getWarehouseCount());
            data.put("totalCount", statistics.getProductCount() + statistics.getCustomerCount() + statistics.getWarehouseCount());
            
            return Result.success("查询成功", data);
        } catch (Exception e) {
            log.error("查询映射统计信息失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
}
