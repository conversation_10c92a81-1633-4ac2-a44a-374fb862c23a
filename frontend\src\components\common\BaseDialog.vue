<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    :width="width"
    :fullscreen="fullscreen"
    :top="top"
    :modal="modal"
    :modal-class="modalClass"
    :append-to-body="appendToBody"
    :lock-scroll="lockScroll"
    :custom-class="customClass"
    :open-delay="openDelay"
    :close-delay="closeDelay"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape"
    :show-close="showClose"
    :before-close="handleBeforeClose"
    :center="center"
    :align-center="alignCenter"
    :destroy-on-close="destroyOnClose"
    :draggable="draggable"
    :overflow="overflow"
    @open="handleOpen"
    @opened="handleOpened"
    @close="handleClose"
    @closed="handleClosed"
    @open-auto-focus="handleOpenAutoFocus"
    @close-auto-focus="handleCloseAutoFocus"
  >
    <!-- 自定义标题 -->
    <template v-if="$slots.header" #header="{ close, titleId, titleClass }">
      <slot name="header" :close="close" :title-id="titleId" :title-class="titleClass" />
    </template>
    
    <!-- 对话框内容 -->
    <div 
      v-loading="loading"
      :element-loading-text="loadingText"
      :element-loading-spinner="loadingSpinner"
      :element-loading-background="loadingBackground"
      class="dialog-content"
      :class="contentClass"
      :style="contentStyle"
    >
      <!-- 错误提示 -->
      <el-alert
        v-if="error"
        :title="error"
        type="error"
        :closable="false"
        show-icon
        class="dialog-error"
      />
      
      <!-- 主要内容 -->
      <div class="dialog-body">
        <slot />
      </div>
    </div>
    
    <!-- 自定义底部 -->
    <template v-if="$slots.footer || showDefaultFooter" #footer>
      <slot name="footer">
        <div v-if="showDefaultFooter" class="dialog-footer">
          <el-button
            v-if="showCancel"
            :size="buttonSize"
            :disabled="loading"
            @click="handleCancel"
          >
            {{ cancelText }}
          </el-button>
          
          <el-button
            v-if="showConfirm"
            type="primary"
            :size="buttonSize"
            :loading="loading"
            :disabled="confirmDisabled"
            @click="handleConfirm"
          >
            {{ confirmText }}
          </el-button>
        </div>
      </slot>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElDialog, ElButton, ElAlert, ElMessageBox } from 'element-plus'
import type { ComponentSize } from '@/types/component'

// Props定义
interface BaseDialogProps {
  // 显示控制
  modelValue: boolean
  
  // 基本属性
  title?: string
  width?: string | number
  fullscreen?: boolean
  top?: string
  modal?: boolean
  modalClass?: string
  appendToBody?: boolean
  lockScroll?: boolean
  customClass?: string
  openDelay?: number
  closeDelay?: number
  closeOnClickModal?: boolean
  closeOnPressEscape?: boolean
  showClose?: boolean
  center?: boolean
  alignCenter?: boolean
  destroyOnClose?: boolean
  draggable?: boolean
  overflow?: boolean
  
  // 内容样式
  contentClass?: string
  contentStyle?: any
  
  // 加载状态
  loading?: boolean
  loadingText?: string
  loadingSpinner?: string
  loadingBackground?: string
  
  // 错误状态
  error?: string
  
  // 底部按钮
  showDefaultFooter?: boolean
  showCancel?: boolean
  showConfirm?: boolean
  cancelText?: string
  confirmText?: string
  confirmDisabled?: boolean
  buttonSize?: ComponentSize
  
  // 关闭前确认
  beforeClose?: (done: () => void) => void
  confirmBeforeClose?: boolean
  confirmCloseMessage?: string
}

const props = withDefaults(defineProps<BaseDialogProps>(), {
  width: '50%',
  top: '15vh',
  modal: true,
  appendToBody: true,
  lockScroll: true,
  closeOnClickModal: true,
  closeOnPressEscape: true,
  showClose: true,
  center: false,
  alignCenter: false,
  destroyOnClose: false,
  draggable: false,
  overflow: false,
  loadingText: '加载中...',
  loadingBackground: 'rgba(0, 0, 0, 0.8)',
  showDefaultFooter: true,
  showCancel: true,
  showConfirm: true,
  cancelText: '取消',
  confirmText: '确定',
  confirmDisabled: false,
  buttonSize: 'default',
  confirmBeforeClose: false,
  confirmCloseMessage: '确定要关闭吗？'
})

// Emits定义
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  open: []
  opened: []
  close: []
  closed: []
  confirm: []
  cancel: []
  'before-close': [done: () => void]
  'open-auto-focus': []
  'close-auto-focus': []
}>()

// 对话框显示状态
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => {
    emit('update:modelValue', value)
  }
})

// 方法
const handleOpen = () => {
  emit('open')
}

const handleOpened = () => {
  emit('opened')
}

const handleClose = () => {
  emit('close')
}

const handleClosed = () => {
  emit('closed')
}

const handleOpenAutoFocus = () => {
  emit('open-auto-focus')
}

const handleCloseAutoFocus = () => {
  emit('close-auto-focus')
}

const handleBeforeClose = (done: () => void) => {
  if (props.beforeClose) {
    props.beforeClose(done)
    return
  }
  
  if (props.confirmBeforeClose) {
    ElMessageBox.confirm(
      props.confirmCloseMessage,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      done()
    }).catch(() => {
      // 用户取消关闭
    })
    return
  }
  
  emit('before-close', done)
  done()
}

const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
  dialogVisible.value = false
}

// 公开方法
const open = () => {
  dialogVisible.value = true
}

const close = () => {
  dialogVisible.value = false
}

// 暴露方法
defineExpose({
  open,
  close
})
</script>

<style lang="scss" scoped>
.dialog-content {
  .dialog-error {
    margin-bottom: 16px;
  }
  
  .dialog-body {
    // 对话框主体内容样式
  }
}

.dialog-footer {
  text-align: right;
  
  .el-button + .el-button {
    margin-left: 12px;
  }
}

// 全局样式（不使用scoped）
:deep(.el-dialog) {
  &.is-draggable {
    .el-dialog__header {
      cursor: move;
      user-select: none;
    }
  }
  
  .el-dialog__body {
    padding: 20px;
  }
  
  .el-dialog__footer {
    padding: 10px 20px 20px;
    border-top: 1px solid #e4e7ed;
  }
}

// 响应式设计
@media (max-width: 768px) {
  :deep(.el-dialog) {
    width: 95% !important;
    margin-top: 5vh !important;
  }
}
</style>
