package com.linkapi.service.interfaces;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.linkapi.common.BusinessException;
import com.linkapi.dto.FieldMappingConfigDTO;
import com.linkapi.entity.FieldMappingConfig;
import com.linkapi.entity.FieldMappingRule;
import com.linkapi.mapper.FieldMappingConfigMapper;
import com.linkapi.service.base.BaseService;

import java.util.List;
import java.util.Map;

/**
 * 字段映射配置服务接口
 * 
 * <AUTHOR> API Team
 */
public interface FieldMappingConfigServiceInterface extends BaseService<FieldMappingConfig, Long> {

    // ==================== 配置管理 ====================

    /**
     * 创建字段映射配置
     * 
     * @param configDTO 配置DTO
     * @return 创建后的配置
     * @throws BusinessException 业务异常
     */
    FieldMappingConfigDTO createConfig(FieldMappingConfigDTO configDTO) throws BusinessException;

    /**
     * 更新字段映射配置
     * 
     * @param configDTO 配置DTO
     * @return 更新后的配置
     * @throws BusinessException 业务异常
     */
    FieldMappingConfigDTO updateConfig(FieldMappingConfigDTO configDTO) throws BusinessException;

    /**
     * 删除字段映射配置
     * 
     * @param id 配置ID
     * @return 是否删除成功
     * @throws BusinessException 业务异常
     */
    boolean deleteConfig(Long id) throws BusinessException;

    /**
     * 获取配置详情（包含规则）
     * 
     * @param id 配置ID
     * @return 配置详情
     * @throws BusinessException 业务异常
     */
    FieldMappingConfigDTO getConfigDetail(Long id) throws BusinessException;

    /**
     * 分页查询配置列表
     * 
     * @param queryDTO 查询条件
     * @return 分页结果
     * @throws BusinessException 业务异常
     */
    IPage<FieldMappingConfigDTO.ConfigPreviewDTO> getConfigPage(FieldMappingConfigDTO.ConfigQueryDTO queryDTO) 
            throws BusinessException;

    // ==================== 配置查询 ====================

    /**
     * 根据类型和模块查询配置
     * 
     * @param configType 配置类型
     * @param moduleType 模块类型
     * @return 配置列表
     * @throws BusinessException 业务异常
     */
    List<FieldMappingConfig> getConfigsByTypeAndModule(FieldMappingConfig.ConfigType configType, String moduleType) 
            throws BusinessException;

    /**
     * 根据源系统和目标系统查询配置
     * 
     * @param sourceSystem 源系统
     * @param targetSystem 目标系统
     * @return 配置列表
     * @throws BusinessException 业务异常
     */
    List<FieldMappingConfig> getConfigsBySourceAndTarget(String sourceSystem, String targetSystem) 
            throws BusinessException;

    /**
     * 获取默认配置
     * 
     * @param configType 配置类型
     * @param moduleType 模块类型
     * @return 默认配置
     * @throws BusinessException 业务异常
     */
    FieldMappingConfig getDefaultConfig(FieldMappingConfig.ConfigType configType, String moduleType) 
            throws BusinessException;

    /**
     * 获取启用的配置列表
     * 
     * @return 启用的配置列表
     * @throws BusinessException 业务异常
     */
    List<FieldMappingConfig> getEnabledConfigs() throws BusinessException;

    // ==================== 配置操作 ====================

    /**
     * 发布配置
     * 
     * @param id 配置ID
     * @return 是否发布成功
     * @throws BusinessException 业务异常
     */
    boolean publishConfig(Long id) throws BusinessException;

    /**
     * 停用配置
     * 
     * @param id 配置ID
     * @return 是否停用成功
     * @throws BusinessException 业务异常
     */
    boolean disableConfig(Long id) throws BusinessException;

    /**
     * 启用配置
     * 
     * @param id 配置ID
     * @return 是否启用成功
     * @throws BusinessException 业务异常
     */
    boolean enableConfig(Long id) throws BusinessException;

    /**
     * 设置默认配置
     * 
     * @param id 配置ID
     * @return 是否设置成功
     * @throws BusinessException 业务异常
     */
    boolean setDefaultConfig(Long id) throws BusinessException;

    /**
     * 复制配置
     * 
     * @param sourceId 源配置ID
     * @param newConfigName 新配置名称
     * @return 复制后的配置
     * @throws BusinessException 业务异常
     */
    FieldMappingConfigDTO copyConfig(Long sourceId, String newConfigName) throws BusinessException;

    // ==================== 字段映射应用 ====================

    /**
     * 应用字段映射
     * 
     * @param configId 配置ID
     * @param sourceData 源数据
     * @return 映射后的数据
     * @throws BusinessException 业务异常
     */
    Map<String, Object> applyFieldMapping(Long configId, Map<String, Object> sourceData) throws BusinessException;

    /**
     * 批量应用字段映射
     * 
     * @param configId 配置ID
     * @param sourceDataList 源数据列表
     * @return 映射后的数据列表
     * @throws BusinessException 业务异常
     */
    List<Map<String, Object>> batchApplyFieldMapping(Long configId, List<Map<String, Object>> sourceDataList) 
            throws BusinessException;

    /**
     * 验证数据
     * 
     * @param configId 配置ID
     * @param data 数据
     * @return 验证结果
     * @throws BusinessException 业务异常
     */
    ValidationResult validateData(Long configId, Map<String, Object> data) throws BusinessException;

    // ==================== 配置导入导出 ====================

    /**
     * 导出配置
     * 
     * @param id 配置ID
     * @return 配置JSON字符串
     * @throws BusinessException 业务异常
     */
    String exportConfig(Long id) throws BusinessException;

    /**
     * 导入配置
     * 
     * @param configJson 配置JSON字符串
     * @return 导入后的配置
     * @throws BusinessException 业务异常
     */
    FieldMappingConfigDTO importConfig(String configJson) throws BusinessException;

    /**
     * 批量导出配置
     * 
     * @param ids 配置ID列表
     * @return 配置JSON字符串
     * @throws BusinessException 业务异常
     */
    String batchExportConfigs(List<Long> ids) throws BusinessException;

    /**
     * 批量导入配置
     * 
     * @param configsJson 配置JSON字符串
     * @return 导入结果
     * @throws BusinessException 业务异常
     */
    ImportResult batchImportConfigs(String configsJson) throws BusinessException;

    // ==================== 统计信息 ====================

    /**
     * 获取配置统计信息
     * 
     * @return 统计信息
     * @throws BusinessException 业务异常
     */
    ConfigStatistics getConfigStatistics() throws BusinessException;

    /**
     * 获取字段类型统计
     * 
     * @param configId 配置ID
     * @return 字段类型统计
     * @throws BusinessException 业务异常
     */
    List<FieldTypeStatistics> getFieldTypeStatistics(Long configId) throws BusinessException;

    // ==================== 内部类 ====================

    /**
     * 验证结果
     */
    class ValidationResult {
        private boolean valid;
        private List<ValidationError> errors;
        private Map<String, Object> validatedData;

        // Getters and Setters
        public boolean isValid() { return valid; }
        public void setValid(boolean valid) { this.valid = valid; }

        public List<ValidationError> getErrors() { return errors; }
        public void setErrors(List<ValidationError> errors) { this.errors = errors; }

        public Map<String, Object> getValidatedData() { return validatedData; }
        public void setValidatedData(Map<String, Object> validatedData) { this.validatedData = validatedData; }
    }

    /**
     * 验证错误
     */
    class ValidationError {
        private String field;
        private String message;
        private Object value;

        public ValidationError(String field, String message, Object value) {
            this.field = field;
            this.message = message;
            this.value = value;
        }

        // Getters and Setters
        public String getField() { return field; }
        public void setField(String field) { this.field = field; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }

        public Object getValue() { return value; }
        public void setValue(Object value) { this.value = value; }
    }

    /**
     * 导入结果
     */
    class ImportResult {
        private int totalCount;
        private int successCount;
        private int failedCount;
        private List<String> errorMessages;

        // Getters and Setters
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }

        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }

        public int getFailedCount() { return failedCount; }
        public void setFailedCount(int failedCount) { this.failedCount = failedCount; }

        public List<String> getErrorMessages() { return errorMessages; }
        public void setErrorMessages(List<String> errorMessages) { this.errorMessages = errorMessages; }
    }

    /**
     * 配置统计
     */
    class ConfigStatistics {
        private long totalCount;
        private long enabledCount;
        private long publishedCount;
        private long defaultCount;

        // Getters and Setters
        public long getTotalCount() { return totalCount; }
        public void setTotalCount(long totalCount) { this.totalCount = totalCount; }

        public long getEnabledCount() { return enabledCount; }
        public void setEnabledCount(long enabledCount) { this.enabledCount = enabledCount; }

        public long getPublishedCount() { return publishedCount; }
        public void setPublishedCount(long publishedCount) { this.publishedCount = publishedCount; }

        public long getDefaultCount() { return defaultCount; }
        public void setDefaultCount(long defaultCount) { this.defaultCount = defaultCount; }
    }

    /**
     * 字段类型统计
     */
    class FieldTypeStatistics {
        private FieldMappingRule.FieldType fieldType;
        private long count;

        // Getters and Setters
        public FieldMappingRule.FieldType getFieldType() { return fieldType; }
        public void setFieldType(FieldMappingRule.FieldType fieldType) { this.fieldType = fieldType; }

        public long getCount() { return count; }
        public void setCount(long count) { this.count = count; }
    }
}
