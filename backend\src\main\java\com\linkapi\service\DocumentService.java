package com.linkapi.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkapi.entity.Document;
import com.linkapi.entity.Document.DocumentStatus;
import com.linkapi.mapper.DocumentMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文档服务类
 * 
 * <AUTHOR> API Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentService {

    private final DocumentMapper documentMapper;

    /**
     * 分页查询文档
     */
    public IPage<Document> getDocumentPage(int pageNum, int pageSize, String documentType, String status) {
        Page<Document> page = new Page<>(pageNum, pageSize);
        QueryWrapper<Document> queryWrapper = new QueryWrapper<>();
        
        if (documentType != null && !documentType.trim().isEmpty()) {
            queryWrapper.eq("document_type", documentType);
        }
        
        if (status != null && !status.trim().isEmpty()) {
            queryWrapper.eq("status", status);
        }
        
        queryWrapper.orderByDesc("created_time");
        
        return documentMapper.selectPage(page, queryWrapper);
    }

    /**
     * 根据ID获取文档
     */
    public Document getDocumentById(Long id) {
        return documentMapper.selectById(id);
    }

    /**
     * 根据单据编号获取文档
     */
    public Document getDocumentByNo(String documentNo) {
        QueryWrapper<Document> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("document_no", documentNo);
        return documentMapper.selectOne(queryWrapper);
    }

    /**
     * 创建文档
     */
    @Transactional
    public Document createDocument(Document document) {
        document.setCreatedTime(LocalDateTime.now());
        document.setUpdatedTime(LocalDateTime.now());
        document.setStatus(DocumentStatus.PENDING);
        document.setRetryCount(0);
        
        documentMapper.insert(document);
        return document;
    }

    /**
     * 更新文档
     */
    @Transactional
    public Document updateDocument(Document document) {
        document.setUpdatedTime(LocalDateTime.now());
        documentMapper.updateById(document);
        return document;
    }

    /**
     * 删除文档
     */
    @Transactional
    public boolean deleteDocument(Long id) {
        return documentMapper.deleteById(id) > 0;
    }

    /**
     * 获取待处理文档列表
     */
    public List<Document> getPendingDocuments() {
        QueryWrapper<Document> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", "PENDING");
        queryWrapper.orderByAsc("created_time");
        return documentMapper.selectList(queryWrapper);
    }

    /**
     * 更新文档状态
     */
    @Transactional
    public boolean updateDocumentStatus(Long id, DocumentStatus status, String errorMessage) {
        Document document = new Document();
        document.setId(id);
        document.setStatus(status);
        document.setUpdatedTime(LocalDateTime.now());

        if (errorMessage != null) {
            document.setErrorMessage(errorMessage);
        }

        if (DocumentStatus.SUCCESS.equals(status)) {
            document.setLastPushTime(LocalDateTime.now());
        }

        return documentMapper.updateById(document) > 0;
    }

    /**
     * 增加重试次数
     */
    @Transactional
    public boolean incrementRetryCount(Long id) {
        Document document = documentMapper.selectById(id);
        if (document != null) {
            document.setRetryCount(document.getRetryCount() + 1);
            document.setUpdatedTime(LocalDateTime.now());
            return documentMapper.updateById(document) > 0;
        }
        return false;
    }

    /**
     * 获取文档统计信息
     */
    public DocumentStatistics getDocumentStatistics() {
        // 总文档数
        Long totalCount = documentMapper.selectCount(new QueryWrapper<>());
        
        // 今日文档数
        QueryWrapper<Document> todayWrapper = new QueryWrapper<>();
        todayWrapper.ge("created_time", LocalDateTime.now().toLocalDate().atStartOfDay());
        Long todayCount = documentMapper.selectCount(todayWrapper);
        
        // 成功文档数
        QueryWrapper<Document> successWrapper = new QueryWrapper<>();
        successWrapper.eq("status", "SUCCESS");
        Long successCount = documentMapper.selectCount(successWrapper);
        
        // 失败文档数
        QueryWrapper<Document> failedWrapper = new QueryWrapper<>();
        failedWrapper.eq("status", "FAILED");
        Long failedCount = documentMapper.selectCount(failedWrapper);
        
        // 计算成功率
        double successRate = totalCount > 0 ? (double) successCount / totalCount * 100 : 0.0;
        
        return new DocumentStatistics(totalCount, todayCount, successCount, failedCount, successRate);
    }

    /**
     * 文档统计信息内部类
     */
    public static class DocumentStatistics {
        private final Long totalCount;
        private final Long todayCount;
        private final Long successCount;
        private final Long failedCount;
        private final Double successRate;

        public DocumentStatistics(Long totalCount, Long todayCount, Long successCount, Long failedCount, Double successRate) {
            this.totalCount = totalCount;
            this.todayCount = todayCount;
            this.successCount = successCount;
            this.failedCount = failedCount;
            this.successRate = successRate;
        }

        // Getters
        public Long getTotalCount() { return totalCount; }
        public Long getTodayCount() { return todayCount; }
        public Long getSuccessCount() { return successCount; }
        public Long getFailedCount() { return failedCount; }
        public Double getSuccessRate() { return successRate; }
    }
}
