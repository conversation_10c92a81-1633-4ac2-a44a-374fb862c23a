package com.linkapi.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkapi.common.BusinessException;
import com.linkapi.common.ResultCode;
import com.linkapi.entity.Document;
import com.linkapi.entity.Document.DocumentStatus;
import com.linkapi.mapper.DocumentMapper;
import com.linkapi.service.base.BaseServiceImpl;
import com.linkapi.service.interfaces.DocumentServiceInterface;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 文档服务实现类
 * 继承基础服务实现，提供文档相关的业务操作
 *
 * <AUTHOR> API Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentService extends BaseServiceImpl<Document, Long, DocumentMapper>
        implements DocumentServiceInterface {

    private final DocumentMapper documentMapper;

    @Override
    protected DocumentMapper getMapper() {
        return documentMapper;
    }

    @Override
    protected String getEntityName() {
        return "文档";
    }

    @Override
    protected void setCreateTime(Document entity) {
        entity.setCreatedTime(LocalDateTime.now());
    }

    @Override
    protected void setUpdateTime(Document entity) {
        entity.setUpdatedTime(LocalDateTime.now());
    }

    @Override
    protected void validateBeforeCreate(Document entity) throws BusinessException {
        super.validateBeforeCreate(entity);

        if (entity.getDocumentNo() == null || entity.getDocumentNo().trim().isEmpty()) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "单据编号不能为空");
        }

        // 检查单据编号是否已存在
        Document existing = getDocumentByNo(entity.getDocumentNo());
        if (existing != null) {
            throw new BusinessException(ResultCode.DATA_ALREADY_EXISTS, "单据编号已存在");
        }
    }

    /**
     * 分页查询文档
     */
    public IPage<Document> getDocumentPage(int pageNum, int pageSize, String documentType, String status) {
        Page<Document> page = new Page<>(pageNum, pageSize);
        QueryWrapper<Document> queryWrapper = new QueryWrapper<>();
        
        if (documentType != null && !documentType.trim().isEmpty()) {
            queryWrapper.eq("document_type", documentType);
        }
        
        if (status != null && !status.trim().isEmpty()) {
            queryWrapper.eq("status", status);
        }
        
        queryWrapper.orderByDesc("created_time");
        
        return documentMapper.selectPage(page, queryWrapper);
    }

    /**
     * 根据ID获取文档
     */
    public Document getDocumentById(Long id) {
        return documentMapper.selectById(id);
    }

    /**
     * 根据单据编号获取文档
     */
    public Document getDocumentByNo(String documentNo) {
        QueryWrapper<Document> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("document_no", documentNo);
        return documentMapper.selectOne(queryWrapper);
    }

    /**
     * 创建文档
     */
    @Transactional
    public Document createDocument(Document document) {
        document.setCreatedTime(LocalDateTime.now());
        document.setUpdatedTime(LocalDateTime.now());
        document.setStatus(DocumentStatus.PENDING);
        document.setRetryCount(0);
        
        documentMapper.insert(document);
        return document;
    }

    /**
     * 更新文档
     */
    @Transactional
    public Document updateDocument(Document document) {
        document.setUpdatedTime(LocalDateTime.now());
        documentMapper.updateById(document);
        return document;
    }

    /**
     * 删除文档
     */
    @Transactional
    public boolean deleteDocument(Long id) {
        return documentMapper.deleteById(id) > 0;
    }

    /**
     * 获取待处理文档列表
     */
    public List<Document> getPendingDocuments() {
        QueryWrapper<Document> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", "PENDING");
        queryWrapper.orderByAsc("created_time");
        return documentMapper.selectList(queryWrapper);
    }

    /**
     * 更新文档状态
     */
    @Transactional
    public boolean updateDocumentStatus(Long id, DocumentStatus status, String errorMessage) {
        Document document = new Document();
        document.setId(id);
        document.setStatus(status);
        document.setUpdatedTime(LocalDateTime.now());

        if (errorMessage != null) {
            document.setErrorMessage(errorMessage);
        }

        if (DocumentStatus.SUCCESS.equals(status)) {
            document.setLastPushTime(LocalDateTime.now());
        }

        return documentMapper.updateById(document) > 0;
    }

    /**
     * 增加重试次数
     */
    @Transactional
    public boolean incrementRetryCount(Long id) {
        Document document = documentMapper.selectById(id);
        if (document != null) {
            document.setRetryCount(document.getRetryCount() + 1);
            document.setUpdatedTime(LocalDateTime.now());
            return documentMapper.updateById(document) > 0;
        }
        return false;
    }

    @Override
    public boolean retryDocument(Long id) throws BusinessException {
        Document document = getById(id);
        if (document == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND, "文档不存在");
        }

        if (!DocumentStatus.FAILED.equals(document.getStatus())) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "只能重试失败状态的文档");
        }

        // 重置状态为待处理
        document.setStatus(DocumentStatus.PENDING);
        document.setRetryCount(document.getRetryCount() + 1);
        document.setErrorMessage(null);
        document.setUpdatedTime(LocalDateTime.now());

        return documentMapper.updateById(document) > 0;
    }

    @Override
    public int batchUpdateStatus(List<Long> ids, DocumentStatus status) throws BusinessException {
        if (ids == null || ids.isEmpty()) {
            throw new BusinessException(ResultCode.PARAM_ERROR, "文档ID列表不能为空");
        }

        int updateCount = 0;
        for (Long id : ids) {
            if (updateDocumentStatus(id, status, null)) {
                updateCount++;
            }
        }

        return updateCount;
    }

    /**
     * 获取文档统计信息
     */
    @Override
    public DocumentServiceInterface.DocumentStatistics getDocumentStatistics() throws BusinessException {
        try {
            // 总文档数
            Long totalCount = documentMapper.selectCount(new QueryWrapper<>());

            // 各状态文档数
            Long pendingCount = documentMapper.selectCount(
                new QueryWrapper<Document>().eq("status", DocumentStatus.PENDING));
            Long successCount = documentMapper.selectCount(
                new QueryWrapper<Document>().eq("status", DocumentStatus.SUCCESS));
            Long failedCount = documentMapper.selectCount(
                new QueryWrapper<Document>().eq("status", DocumentStatus.FAILED));
            Long pushingCount = documentMapper.selectCount(
                new QueryWrapper<Document>().eq("status", DocumentStatus.PUSHING));
            Long validatedCount = documentMapper.selectCount(
                new QueryWrapper<Document>().eq("status", DocumentStatus.VALIDATED));

            DocumentServiceInterface.DocumentStatistics statistics = new DocumentServiceInterface.DocumentStatistics();
            statistics.setTotalCount(totalCount != null ? totalCount : 0);
            statistics.setPendingCount(pendingCount != null ? pendingCount : 0);
            statistics.setSuccessCount(successCount != null ? successCount : 0);
            statistics.setFailedCount(failedCount != null ? failedCount : 0);
            statistics.setProcessingCount((pushingCount != null ? pushingCount : 0) + (validatedCount != null ? validatedCount : 0));

            return statistics;
        } catch (Exception e) {
            log.error("获取文档统计信息失败", e);
            throw new BusinessException(ResultCode.DATABASE_ERROR, "获取文档统计信息失败");
        }
    }

    /**
     * 文档统计信息内部类
     */
    public static class DocumentStatistics {
        private final Long totalCount;
        private final Long todayCount;
        private final Long successCount;
        private final Long failedCount;
        private final Double successRate;

        public DocumentStatistics(Long totalCount, Long todayCount, Long successCount, Long failedCount, Double successRate) {
            this.totalCount = totalCount;
            this.todayCount = todayCount;
            this.successCount = successCount;
            this.failedCount = failedCount;
            this.successRate = successRate;
        }

        // Getters
        public Long getTotalCount() { return totalCount; }
        public Long getTodayCount() { return todayCount; }
        public Long getSuccessCount() { return successCount; }
        public Long getFailedCount() { return failedCount; }
        public Double getSuccessRate() { return successRate; }
    }
}
