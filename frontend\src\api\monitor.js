import request from '@/utils/request'

/**
 * 系统监控相关API
 */

// 获取系统信息
export function getSystemInfo() {
  return request({
    url: '/monitor/system',
    method: 'get'
  })
}

// 获取应用信息
export function getApplicationInfo() {
  return request({
    url: '/monitor/application',
    method: 'get'
  })
}

// 获取服务状态列表
export function getServicesList() {
  return request({
    url: '/monitor/services',
    method: 'get'
  })
}

// 获取服务状态（别名，保持兼容性）
export function getServiceStatus() {
  return request({
    url: '/monitor/services',
    method: 'get'
  })
}

// 检查指定服务状态
export function checkService(serviceName) {
  return request({
    url: `/monitor/services/${serviceName}/check`,
    method: 'post'
  })
}

// 获取实时性能数据
export function getRealtimeData(type) {
  return request({
    url: `/monitor/realtime/${type}`,
    method: 'get'
  })
}

// 获取所有监控数据（组合接口）
export function getAllMonitorData() {
  return Promise.all([
    getSystemInfo(),
    getApplicationInfo(),
    getServiceStatus()
  ])
}