package com.linkapi.dto;

import com.linkapi.entity.FieldMappingConfig;
import com.linkapi.entity.FieldMappingRule;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 字段映射配置DTO
 * 用于前后端数据传输
 * 
 * <AUTHOR> API Team
 */
@Data
public class FieldMappingConfigDTO {

    /**
     * 配置ID
     */
    private Long id;

    /**
     * 配置名称
     */
    @NotBlank(message = "配置名称不能为空")
    @Size(max = 100, message = "配置名称长度不能超过100个字符")
    private String configName;

    /**
     * 配置描述
     */
    @Size(max = 500, message = "配置描述长度不能超过500个字符")
    private String configDescription;

    /**
     * 配置类型
     */
    @NotNull(message = "配置类型不能为空")
    private FieldMappingConfig.ConfigType configType;

    /**
     * 模块类型
     */
    @NotBlank(message = "模块类型不能为空")
    private String moduleType;

    /**
     * 源系统
     */
    @NotBlank(message = "源系统不能为空")
    private String sourceSystem;

    /**
     * 目标系统
     */
    @NotBlank(message = "目标系统不能为空")
    private String targetSystem;

    /**
     * 配置版本
     */
    @NotBlank(message = "配置版本不能为空")
    private String configVersion;

    /**
     * 是否启用
     */
    private Boolean isEnabled;

    /**
     * 是否为默认配置
     */
    private Boolean isDefault;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 状态
     */
    private FieldMappingConfig.ConfigStatus status;

    /**
     * 字段映射规则列表
     */
    @Valid
    @NotEmpty(message = "字段映射规则不能为空")
    private List<FieldMappingRuleDTO> fieldRules;

    /**
     * 扩展配置
     */
    private Map<String, Object> extendedConfig;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;

    /**
     * 字段映射规则DTO
     */
    @Data
    public static class FieldMappingRuleDTO {

        /**
         * 规则ID
         */
        private Long id;

        /**
         * 源字段名
         */
        @NotBlank(message = "源字段名不能为空")
        private String sourceField;

        /**
         * 源字段显示名
         */
        private String sourceFieldLabel;

        /**
         * 目标字段名
         */
        @NotBlank(message = "目标字段名不能为空")
        private String targetField;

        /**
         * 目标字段显示名
         */
        private String targetFieldLabel;

        /**
         * 字段类型
         */
        @NotNull(message = "字段类型不能为空")
        private FieldMappingRule.FieldType fieldType;

        /**
         * 是否必填
         */
        private Boolean isRequired;

        /**
         * 默认值
         */
        private String defaultValue;

        /**
         * 转换规则类型
         */
        private FieldMappingRule.TransformType transformType;

        /**
         * 转换规则表达式
         */
        private String transformExpression;

        /**
         * 验证规则
         */
        private ValidationRuleDTO validationRules;

        /**
         * 字段描述
         */
        private String fieldDescription;

        /**
         * 排序号
         */
        private Integer sortOrder;

        /**
         * 是否启用
         */
        private Boolean isEnabled;
    }

    /**
     * 验证规则DTO
     */
    @Data
    public static class ValidationRuleDTO {

        /**
         * 是否必填
         */
        private Boolean required;

        /**
         * 最小长度
         */
        private Integer minLength;

        /**
         * 最大长度
         */
        private Integer maxLength;

        /**
         * 最小值
         */
        private Double minValue;

        /**
         * 最大值
         */
        private Double maxValue;

        /**
         * 正则表达式
         */
        private String pattern;

        /**
         * 枚举值列表
         */
        private List<String> enumValues;

        /**
         * 自定义验证函数
         */
        private String customValidator;

        /**
         * 错误消息
         */
        private String errorMessage;
    }

    /**
     * 配置预览DTO
     */
    @Data
    public static class ConfigPreviewDTO {

        /**
         * 配置ID
         */
        private Long id;

        /**
         * 配置名称
         */
        private String configName;

        /**
         * 配置类型
         */
        private FieldMappingConfig.ConfigType configType;

        /**
         * 模块类型
         */
        private String moduleType;

        /**
         * 源系统
         */
        private String sourceSystem;

        /**
         * 目标系统
         */
        private String targetSystem;

        /**
         * 配置版本
         */
        private String configVersion;

        /**
         * 是否启用
         */
        private Boolean isEnabled;

        /**
         * 是否为默认配置
         */
        private Boolean isDefault;

        /**
         * 状态
         */
        private FieldMappingConfig.ConfigStatus status;

        /**
         * 字段规则数量
         */
        private Integer ruleCount;

        /**
         * 创建时间
         */
        private LocalDateTime createdTime;

        /**
         * 更新时间
         */
        private LocalDateTime updatedTime;
    }

    /**
     * 配置查询DTO
     */
    @Data
    public static class ConfigQueryDTO {

        /**
         * 配置名称（模糊查询）
         */
        private String configName;

        /**
         * 配置类型
         */
        private FieldMappingConfig.ConfigType configType;

        /**
         * 模块类型
         */
        private String moduleType;

        /**
         * 源系统
         */
        private String sourceSystem;

        /**
         * 目标系统
         */
        private String targetSystem;

        /**
         * 是否启用
         */
        private Boolean isEnabled;

        /**
         * 状态
         */
        private FieldMappingConfig.ConfigStatus status;

        /**
         * 创建时间开始
         */
        private LocalDateTime createdTimeStart;

        /**
         * 创建时间结束
         */
        private LocalDateTime createdTimeEnd;

        /**
         * 页码
         */
        private Integer pageNum = 1;

        /**
         * 页大小
         */
        private Integer pageSize = 20;
    }
}
