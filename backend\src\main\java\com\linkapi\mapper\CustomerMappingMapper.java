package com.linkapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linkapi.entity.CustomerMapping;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 客户映射Mapper接口
 * 
 * <AUTHOR> API Team
 */
@Mapper
public interface CustomerMappingMapper extends BaseMapper<CustomerMapping> {

    /**
     * 根据外部编码查询映射
     */
    @Select("SELECT * FROM customer_mapping WHERE deleted = 0 AND external_code = #{externalCode} AND status = 'ACTIVE'")
    CustomerMapping selectByExternalCode(@Param("externalCode") String externalCode);

    /**
     * 根据金蝶编码查询映射
     */
    @Select("SELECT * FROM customer_mapping WHERE deleted = 0 AND kingdee_code = #{kingdeeCode} AND status = 'ACTIVE'")
    CustomerMapping selectByKingdeeCode(@Param("kingdeeCode") String kingdeeCode);

    /**
     * 查询所有有效映射
     */
    @Select("SELECT * FROM customer_mapping WHERE deleted = 0 AND status = 'ACTIVE' ORDER BY created_time DESC")
    List<CustomerMapping> selectActiveMapping();

    /**
     * 批量插入映射
     */
    int batchInsert(@Param("list") List<CustomerMapping> mappings);

    /**
     * 统计映射数量
     */
    @Select("SELECT " +
            "COUNT(*) as total_count, " +
            "SUM(CASE WHEN status = 'ACTIVE' THEN 1 ELSE 0 END) as active_count, " +
            "SUM(CASE WHEN status = 'INACTIVE' THEN 1 ELSE 0 END) as inactive_count " +
            "FROM customer_mapping WHERE deleted = 0")
    Map<String, Object> selectMappingStatistics();

    /**
     * 查询未映射的客户
     */
    @Select("SELECT DISTINCT d.customer_name " +
            "FROM documents d " +
            "LEFT JOIN customer_mapping cm ON d.customer_name = cm.external_code AND cm.deleted = 0 " +
            "WHERE d.deleted = 0 AND cm.id IS NULL " +
            "ORDER BY d.customer_name")
    List<Map<String, Object>> selectUnmappedCustomers();

    /**
     * 根据关键词搜索映射
     */
    @Select("SELECT * FROM customer_mapping " +
            "WHERE deleted = 0 " +
            "AND (external_code LIKE CONCAT('%', #{keyword}, '%') " +
            "OR external_name LIKE CONCAT('%', #{keyword}, '%') " +
            "OR kingdee_code LIKE CONCAT('%', #{keyword}, '%') " +
            "OR kingdee_name LIKE CONCAT('%', #{keyword}, '%')) " +
            "ORDER BY created_time DESC")
    List<CustomerMapping> searchMapping(@Param("keyword") String keyword);
}