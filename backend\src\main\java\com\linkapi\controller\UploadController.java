package com.linkapi.controller;

import com.linkapi.common.Result;
import com.linkapi.entity.Document;
import com.linkapi.entity.DocumentDetail;
import com.linkapi.service.DocumentService;
import com.linkapi.service.ExcelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * Excel上传控制器
 * 
 * <AUTHOR> API Team
 */
@Slf4j
@RestController
@RequestMapping("/upload")
@RequiredArgsConstructor
public class UploadController {

    private final ExcelService excelService;
    private final DocumentService documentService;

    /**
     * 上传Excel文件并解析为单据
     */
    @PostMapping("/excel")
    public Result<Map<String, Object>> uploadExcel(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.error("请选择要上传的文件");
        }

        try {
            log.info("开始处理Excel文件: {}", file.getOriginalFilename());
            
            // 解析Excel文件为单据列表
            List<Document> documents = excelService.parseExcelToDocuments(file);
            
            // 保存单据到数据库
            int successCount = 0;
            int errorCount = 0;
            
            for (Document document : documents) {
                try {
                    documentService.createDocument(document);
                    successCount++;
                } catch (Exception e) {
                    log.error("保存单据失败: {}", document.getDocumentNo(), e);
                    errorCount++;
                }
            }
            
            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "Excel文件处理完成");
            result.put("totalRows", documents.size());
            result.put("successRows", successCount);
            result.put("errorRows", errorCount);
            result.put("documentCount", successCount);
            result.put("fileName", file.getOriginalFilename());
            result.put("fileSize", file.getSize());
            
            log.info("Excel文件处理完成: 总行数={}, 成功={}, 失败={}", 
                documents.size(), successCount, errorCount);
            
            return Result.success("Excel文件上传成功", result);
            
        } catch (Exception e) {
            log.error("Excel文件处理失败: {}", e.getMessage(), e);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Excel文件处理失败: " + e.getMessage());
            result.put("fileName", file.getOriginalFilename());
            result.put("fileSize", file.getSize());
            
            return Result.error("Excel文件处理失败");
        }
    }

    /**
     * 获取Excel模板
     */
    @GetMapping("/template")
    public ResponseEntity<Resource> getTemplate() {
        try {
            log.info("开始下载Excel模板");
            
            // 创建Excel模板
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("数据导入模板");
            
            // 创建表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerFont.setFontHeightInPoints((short) 12);
            headerStyle.setFont(headerFont);
            headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            
            // 创建表头
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "单据编号", "单据类型", "单据日期", "客户名称", "仓库名称",
                "商品编码", "商品名称", "数量", "单价", "金额", "备注"
            };
            
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
                sheet.setColumnWidth(i, 4000);
            }
            
            // 添加示例数据
            Row exampleRow = sheet.createRow(1);
            String[] exampleData = {
                "SO202509050001", "销售订单", "2025-09-05", "测试客户", "主仓库",
                "P001", "测试商品", "10", "100.00", "1000.00", "示例备注"
            };
            
            for (int i = 0; i < exampleData.length; i++) {
                Cell cell = exampleRow.createCell(i);
                cell.setCellValue(exampleData[i]);
            }
            
            // 将工作簿写入字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            workbook.close();
            
            // 创建资源
            ByteArrayResource resource = new ByteArrayResource(outputStream.toByteArray());
            
            // 设置文件名
            String filename = "数据导入模板_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";
            
            log.info("Excel模板创建成功，文件名: {}", filename);
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                    .header(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                    .body(resource);
                    
        } catch (Exception e) {
            log.error("Excel模板下载失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 获取上传历史
     */
    @GetMapping("/history")
    public Result<Map<String, Object>> getUploadHistory(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String fileName,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            log.info("查询上传历史，页码: {}, 页大小: {}, 文件名: {}, 状态: {}", page, size, fileName, status);
            
            // 查询上传历史记录
            IPage<Document> documentPage = documentService.getDocumentPage(page, size, null, status);
            
            // 构造返回结果
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> records = new ArrayList<>();
            
            for (Document document : documentPage.getRecords()) {
                Map<String, Object> record = new HashMap<>();
                record.put("id", document.getId());
                record.put("documentNo", document.getDocumentNo());
                record.put("documentType", document.getDocumentType());
                record.put("fileName", document.getSourceFile());
                record.put("status", document.getStatus());
                record.put("uploadTime", document.getCreatedTime());
                record.put("processTime", document.getUpdatedTime());
                record.put("errorMessage", document.getErrorMessage());
                record.put("totalAmount", document.getTotalAmount());
                record.put("totalQuantity", document.getTotalQuantity());
                records.add(record);
            }
            
            result.put("records", records);
            result.put("total", documentPage.getTotal());
            result.put("page", page);
            result.put("size", size);
            result.put("pages", documentPage.getPages());
            
            log.info("上传历史查询成功，共{}条记录", documentPage.getTotal());
            return Result.success("查询成功", result);
            
        } catch (Exception e) {
            log.error("查询上传历史失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取上传结果
     */
    @GetMapping("/result/{id}")
    public Result<Map<String, Object>> getUploadResult(@PathVariable Long id) {
        try {
            log.info("查询上传结果，ID: {}", id);
            
            // 查询单据信息
            Document document = documentService.getDocumentById(id);
            if (document == null) {
                return Result.error("上传记录不存在");
            }
            
            // 构造返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("id", document.getId());
            result.put("documentNo", document.getDocumentNo());
            result.put("documentType", document.getDocumentType());
            result.put("fileName", document.getSourceFile());
            result.put("status", document.getStatus());
            result.put("uploadTime", document.getCreatedTime());
            result.put("processTime", document.getUpdatedTime());
            result.put("customerName", document.getCustomerName());
            result.put("warehouseName", document.getWarehouseName());
            result.put("totalAmount", document.getTotalAmount());
            result.put("totalQuantity", document.getTotalQuantity());
            result.put("retryCount", document.getRetryCount());
            result.put("errorMessage", document.getErrorMessage());
            result.put("kingdeeDocumentNo", document.getKingdeeDocumentNo());
            
            // 获取单据明细
            if (document.getDetails() != null && !document.getDetails().isEmpty()) {
                List<Map<String, Object>> details = new ArrayList<>();
                for (DocumentDetail detail : document.getDetails()) {
                    Map<String, Object> detailMap = new HashMap<>();
                    detailMap.put("id", detail.getId());
                    detailMap.put("productCode", detail.getProductCode());
                    detailMap.put("productName", detail.getProductName());
                    detailMap.put("quantity", detail.getQuantity());
                    detailMap.put("unitPrice", detail.getUnitPrice());
                    detailMap.put("amount", detail.getAmount());
                    details.add(detailMap);
                }
                result.put("details", details);
            }
            
            // 添加统计信息
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("detailCount", document.getDetails() != null ? document.getDetails().size() : 0);
            statistics.put("isProcessed", document.getStatus() == Document.DocumentStatus.SUCCESS);
            statistics.put("hasError", document.getStatus() == Document.DocumentStatus.FAILED);
            result.put("statistics", statistics);
            
            log.info("上传结果查询成功，单据编号: {}", document.getDocumentNo());
            return Result.success("查询成功", result);
            
        } catch (Exception e) {
            log.error("查询上传结果失败，ID: {}", id, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 删除上传记录
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteUploadRecord(@PathVariable Long id) {
        try {
            log.info("删除上传记录，ID: {}", id);
            
            // 检查记录是否存在
            Document document = documentService.getDocumentById(id);
            if (document == null) {
                return Result.error("上传记录不存在");
            }
            
            // 检查是否可以删除（只能删除失败或待处理的记录）
            if (document.getStatus() == Document.DocumentStatus.SUCCESS) {
                return Result.error("不能删除已成功处理的记录");
            }
            
            if (document.getStatus() == Document.DocumentStatus.PUSHING) {
                return Result.error("不能删除正在推送中的记录");
            }
            
            // 执行删除操作
            boolean success = documentService.deleteDocument(id);
            if (success) {
                log.info("上传记录删除成功，单据编号: {}", document.getDocumentNo());
                return Result.success("删除成功");
            } else {
                return Result.error("删除失败");
            }
            
        } catch (Exception e) {
            log.error("删除上传记录失败，ID: {}", id, e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 重新处理上传文件
     */
    @PostMapping("/{id}/reprocess")
    public Result<String> reprocessUpload(@PathVariable Long id) {
        try {
            log.info("重新处理上传文件，ID: {}", id);
            
            // 检查记录是否存在
            Document document = documentService.getDocumentById(id);
            if (document == null) {
                return Result.error("上传记录不存在");
            }
            
            // 检查是否可以重新处理（只能重新处理失败的记录）
            if (document.getStatus() == Document.DocumentStatus.SUCCESS) {
                return Result.error("已成功处理的记录不需要重新处理");
            }
            
            if (document.getStatus() == Document.DocumentStatus.PUSHING) {
                return Result.error("正在推送中的记录不能重新处理");
            }
            
            // 检查重试次数
            int maxRetryCount = 3; // 最大重试次数
            if (!document.canRetry(maxRetryCount)) {
                return Result.error("该记录已超过最大重试次数（" + maxRetryCount + "次）");
            }
            
            // 重置状态为待校验
            document.setStatus(Document.DocumentStatus.PENDING);
            document.setErrorMessage(null);
            document.incrementRetryCount();
            document.setUpdatedTime(LocalDateTime.now());
            
            // 更新数据库
            Document updatedDocument = documentService.updateDocument(document);
            if (updatedDocument != null) {
                log.info("重新处理设置成功，单据编号: {}，重试次数: {}", 
                    document.getDocumentNo(), document.getRetryCount());
                
                // 这里可以添加异步处理逻辑，比如发送到消息队列
                // 暂时只是更新状态，实际的重新处理逻辑由定时任务或手动触发
                
                return Result.success("重新处理设置成功，系统将自动重新处理该记录");
            } else {
                return Result.error("重新处理设置失败");
            }
            
        } catch (Exception e) {
            log.error("重新处理上传文件失败，ID: {}", id, e);
            return Result.error("重新处理失败：" + e.getMessage());
        }
    }

    /**
     * 获取支持的文件类型
     */
    @GetMapping("/supported-types")
    public Result<String[]> getSupportedFileTypes() {
        String[] types = {".xlsx", ".xls"};
        return Result.success("查询成功", types);
    }

    /**
     * 获取上传配置
     */
    @GetMapping("/config")
    public Result<Map<String, Object>> getUploadConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("maxFileSize", "10MB");
        config.put("supportedTypes", new String[]{".xlsx", ".xls"});
        config.put("maxRows", 5000);
        
        return Result.success("查询成功", config);
    }
}