package com.linkapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linkapi.entity.SystemLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 系统日志Mapper接口
 * 
 * <AUTHOR> API Team
 */
@Mapper
public interface SystemLogMapper extends BaseMapper<SystemLog> {

    /**
     * 获取日志统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as total_count, " +
            "SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count, " +
            "SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_count, " +
            "SUM(CASE WHEN status = 'PROCESSING' THEN 1 ELSE 0 END) as processing_count, " +
            "SUM(CASE WHEN status = 'WARNING' THEN 1 ELSE 0 END) as warning_count, " +
            "AVG(execution_time) as avg_execution_time " +
            "FROM system_logs")
    Map<String, Object> selectLogStatistics();

    /**
     * 获取今日日志统计
     */
    @Select("SELECT " +
            "COUNT(*) as today_count, " +
            "SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as today_success, " +
            "SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as today_failed, " +
            "AVG(execution_time) as today_avg_time " +
            "FROM system_logs WHERE DATE(created_time) = CURDATE()")
    Map<String, Object> selectTodayLogStatistics();

    /**
     * 获取错误日志统计
     */
    @Select("SELECT " +
            "DATE(created_time) as date, " +
            "COUNT(*) as error_count, " +
            "log_type, " +
            "operation " +
            "FROM system_logs " +
            "WHERE status = 'FAILED' AND created_time >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY) " +
            "GROUP BY DATE(created_time), log_type, operation " +
            "ORDER BY date DESC, error_count DESC")
    List<Map<String, Object>> selectErrorLogStatistics(@Param("days") int days);

    /**
     * 获取API调用趋势
     */
    @Select("SELECT " +
            "DATE(created_time) as date, " +
            "COUNT(*) as call_count, " +
            "SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count, " +
            "SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_count, " +
            "AVG(execution_time) as avg_time " +
            "FROM system_logs " +
            "WHERE log_type = 'API_CALL' AND created_time >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY) " +
            "GROUP BY DATE(created_time) " +
            "ORDER BY date")
    List<Map<String, Object>> selectApiCallTrend(@Param("days") int days);

    /**
     * 根据操作类型统计日志
     */
    @Select("SELECT " +
            "operation, " +
            "COUNT(*) as count, " +
            "SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count, " +
            "SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_count, " +
            "AVG(execution_time) as avg_time " +
            "FROM system_logs " +
            "GROUP BY operation " +
            "ORDER BY count DESC")
    List<Map<String, Object>> selectLogCountByOperation();

    /**
     * 根据日志类型统计
     */
    @Select("SELECT " +
            "log_type, " +
            "COUNT(*) as count, " +
            "SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count, " +
            "SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_count " +
            "FROM system_logs " +
            "GROUP BY log_type " +
            "ORDER BY count DESC")
    List<Map<String, Object>> selectLogCountByType();

    /**
     * 获取最近的错误日志
     */
    @Select("SELECT * FROM system_logs " +
            "WHERE status = 'FAILED' " +
            "ORDER BY created_time DESC " +
            "LIMIT #{limit}")
    List<SystemLog> selectRecentErrorLogs(@Param("limit") int limit);

    /**
     * 获取慢查询日志
     */
    @Select("SELECT * FROM system_logs " +
            "WHERE execution_time >= #{minTime} " +
            "ORDER BY execution_time DESC " +
            "LIMIT #{limit}")
    List<SystemLog> selectSlowLogs(@Param("minTime") long minTime, @Param("limit") int limit);

    /**
     * 根据业务ID查询日志
     */
    @Select("SELECT * FROM system_logs " +
            "WHERE business_id = #{businessId} " +
            "ORDER BY created_time DESC")
    List<SystemLog> selectByBusinessId(@Param("businessId") String businessId);

    /**
     * 根据IP地址统计访问量
     */
    @Select("SELECT " +
            "ip_address, " +
            "COUNT(*) as access_count, " +
            "SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count, " +
            "SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_count " +
            "FROM system_logs " +
            "WHERE ip_address IS NOT NULL " +
            "GROUP BY ip_address " +
            "ORDER BY access_count DESC " +
            "LIMIT #{limit}")
    List<Map<String, Object>> selectAccessStatsByIp(@Param("limit") int limit);

    /**
     * 获取小时级别的访问统计
     */
    @Select("SELECT " +
            "HOUR(created_time) as hour, " +
            "COUNT(*) as count " +
            "FROM system_logs " +
            "WHERE DATE(created_time) = CURDATE() " +
            "GROUP BY HOUR(created_time) " +
            "ORDER BY hour")
    List<Map<String, Object>> selectHourlyAccessStats();

    /**
     * 清理过期日志
     */
    @Select("DELETE FROM system_logs WHERE created_time < #{expireTime}")
    int deleteExpiredLogs(@Param("expireTime") String expireTime);

    /**
     * 获取系统性能统计
     */
    @Select("SELECT " +
            "operation, " +
            "COUNT(*) as call_count, " +
            "AVG(execution_time) as avg_time, " +
            "MIN(execution_time) as min_time, " +
            "MAX(execution_time) as max_time, " +
            "STDDEV(execution_time) as std_time " +
            "FROM system_logs " +
            "WHERE execution_time IS NOT NULL " +
            "GROUP BY operation " +
            "ORDER BY avg_time DESC")
    List<Map<String, Object>> selectPerformanceStats();

    /**
     * 获取用户操作统计
     */
    @Select("SELECT " +
            "operator, " +
            "COUNT(*) as operation_count, " +
            "SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count, " +
            "SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_count " +
            "FROM system_logs " +
            "WHERE operator IS NOT NULL " +
            "GROUP BY operator " +
            "ORDER BY operation_count DESC")
    List<Map<String, Object>> selectUserOperationStats();
}