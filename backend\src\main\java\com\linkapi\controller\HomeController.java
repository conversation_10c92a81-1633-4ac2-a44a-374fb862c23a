package com.linkapi.controller;

import com.linkapi.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 首页控制器
 * 
 * <AUTHOR> API Team
 */
@Slf4j
@RestController
@RequestMapping("/home")
public class HomeController {

    /**
     * 获取首页统计数据
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getStatistics() {
        Map<String, Object> data = new HashMap<>();
        
        // 模拟统计数据
        data.put("totalDocuments", 1250);
        data.put("todayDocuments", 45);
        data.put("successRate", 98.5);
        data.put("errorCount", 3);
        
        // 最近处理记录
        Map<String, Object> recentActivity = new HashMap<>();
        recentActivity.put("lastProcessTime", LocalDateTime.now().minusMinutes(5));
        recentActivity.put("lastDocument", "销售订单-SO202509040001");
        recentActivity.put("status", "成功");
        data.put("recentActivity", recentActivity);
        
        // 系统状态
        Map<String, Object> systemStatus = new HashMap<>();
        systemStatus.put("apiStatus", "正常");
        systemStatus.put("databaseStatus", "正常");
        systemStatus.put("kingdeeStatus", "正常");
        systemStatus.put("lastUpdateTime", LocalDateTime.now());
        data.put("systemStatus", systemStatus);
        
        return Result.success("获取首页数据成功", data);
    }

    /**
     * 获取快捷操作菜单
     */
    @GetMapping("/quickActions")
    public Result<Map<String, Object>> getQuickActions() {
        Map<String, Object> data = new HashMap<>();
        
        // 快捷操作列表
        data.put("actions", new String[]{
            "上传单据",
            "查看日志",
            "数据映射",
            "系统设置"
        });
        
        return Result.success("获取快捷操作成功", data);
    }
}
