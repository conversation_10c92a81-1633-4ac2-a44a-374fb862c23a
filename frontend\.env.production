# 生产环境配置
NODE_ENV=production

# API基础路径
VITE_APP_BASE_API=/api

# 后端服务地址
VITE_APP_SERVER_URL=http://localhost:8080

# 是否开启Mock
VITE_APP_MOCK=false

# 是否开启代理
VITE_APP_PROXY=false

# 是否开启HTTPS
VITE_APP_HTTPS=true

# 是否开启eslint
VITE_APP_ESLINT=false

# 是否开启stylelint
VITE_APP_STYLELINT=false

# 是否开启TypeScript检查
VITE_APP_TS_CHECK=false

# 是否开启源码映射
VITE_APP_SOURCEMAP=false

# 是否开启调试工具
VITE_APP_DEBUG=false

# 日志级别
VITE_APP_LOG_LEVEL=error

# 是否开启错误日志
VITE_APP_ERROR_LOG=true

# 错误日志环境
VITE_APP_ERROR_LOG_ENV=production

# 是否开启性能监控
VITE_APP_PERFORMANCE=true

# 是否开启包分析
VITE_APP_ANALYZE=false

# 是否开启PWA
VITE_APP_PWA=true

# 是否开启CDN
VITE_APP_CDN=true

# CDN基础路径
VITE_APP_CDN_URL=http://localhost:3001

# 是否开启gzip压缩
VITE_APP_GZIP=true

# 是否开启Brotli压缩
VITE_APP_BROTLI=true

# 构建输出目录
VITE_APP_OUTPUT_DIR=dist

# 静态资源目录
VITE_APP_ASSETS_DIR=assets

# 是否开启代码分割
VITE_APP_CODE_SPLITTING=true

# 是否开启Tree Shaking
VITE_APP_TREE_SHAKING=true

# 是否开启压缩
VITE_APP_MINIFY=true

# 是否开启缓存
VITE_APP_CACHE=true

# 缓存时间(秒)
VITE_APP_CACHE_TIME=31536000

# 开发工具
VITE_APP_DEV_TOOLS=false