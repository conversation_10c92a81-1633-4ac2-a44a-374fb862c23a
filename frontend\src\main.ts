import { createApp } from 'vue'
import { createPinia } from 'pinia'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import 'normalize.css'
import 'nprogress/nprogress.css'

import App from './App.vue'
import router from './router'
import { setupDirectives } from './directives'
import { setupGlobalProperties } from './utils/global'

// 引入通用组件
import { installCommonComponents } from '@/components/common'

// 引入错误处理插件
import { errorHandlerPlugin } from '@/plugins/errorHandler'

// 样式文件
import './styles/index.scss'

// 创建应用实例
const app = createApp(App)

// 创建 Pinia 实例
const pinia = createPinia()

// 注册 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 使用插件
app.use(pinia)
app.use(router)
app.use(ElementPlus, {
  locale: zhCn,
  size: 'default'
})

// 安装通用组件
installCommonComponents(app)

// 安装错误处理插件
app.use(errorHandlerPlugin, {
  logToConsole: import.meta.env.DEV,
  showMessage: true,
  showNotification: false,
  reportToServer: import.meta.env.PROD,
  reportUrl: '/api/error-report'
})

// 设置全局指令（在Element Plus之后注册，避免冲突）
setupDirectives(app)

// 设置全局属性
setupGlobalProperties(app)

// 全局警告处理（保留开发环境的警告处理）
if (import.meta.env.DEV) {
  app.config.warnHandler = (msg, instance, trace) => {
    console.warn('全局警告:', msg)
    console.warn('组件实例:', instance)
    console.warn('组件追踪:', trace)
  }
}

// 挂载应用
app.mount('#app')

// 开发环境下的调试信息
if (import.meta.env.DEV) {
  console.log('🚀 Link API 管理系统启动成功!')
  console.log('📦 Vue版本:', app.version)
  console.log('🌍 环境:', import.meta.env.MODE)
  console.log('🔗 API地址:', import.meta.env.VITE_APP_BASE_API)
}

// 性能监控
if (import.meta.env.PROD) {
  // 监控首屏加载时间
  window.addEventListener('load', () => {
    const loadTime = performance.now()
    console.log(`📊 页面加载时间: ${loadTime.toFixed(2)}ms`)
  })
}

export default app