server:
  port: 8080
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: link-api-backend
  
  # 排除SpringDoc自动配置
  autoconfigure:
    exclude:
      - org.springdoc.core.configuration.SpringDocConfiguration
      - org.springdoc.webmvc.ui.SwaggerConfig
  
  # 数据源配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************************************************************
    username: root
    password: kingdee2025
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: DatebookHikariCP
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
      enabled: true
  
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 日志配置
logging:
  level:
    com.linkapi: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'
  file:
    name: logs/link-api.log
    max-size: 100MB
    max-history: 30

# 应用配置
app:
  # 文件存储配置
  file:
    upload-path: ./uploads/
    temp-path: ./temp/
    max-size: 50MB
  
  # 金蝶API配置
  kingdee:
    base-url: https://api.kingdee.com
    app-id: your-app-id
    app-secret: your-app-secret
    timeout: 30000
    retry-times: 3
    # 星辰客户端配置
    client_id: "test-client-id"
    client_secret: "test-client-secret"
  
  # 业务配置
  business:
    # 单据处理配置
    document:
      batch-size: 100
      max-retry: 3
      retry-interval: 5000
    
    # 基础资料同步配置
    sync:
      enabled: true
      cron: '0 0 2 * * ?'  # 每天凌晨2点同步
      batch-size: 500

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true