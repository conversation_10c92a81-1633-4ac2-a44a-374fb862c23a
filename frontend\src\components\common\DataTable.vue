<template>
  <div 
    ref="componentRef"
    :class="componentClass"
    :style="componentStyle"
    v-bind="testAttributes"
    class="data-table"
  >
    <!-- 表格工具栏 -->
    <div v-if="showToolbar" class="table-toolbar">
      <div class="toolbar-left">
        <slot name="toolbar-left">
          <el-button
            v-if="showRefresh"
            type="primary"
            :icon="RefreshIcon"
            @click="handleRefresh"
            :loading="loading"
          >
            刷新
          </el-button>
        </slot>
      </div>
      
      <div class="toolbar-right">
        <slot name="toolbar-right">
          <!-- 列设置 -->
          <el-button
            v-if="showColumnSettings"
            :icon="SettingIcon"
            @click="showColumnDialog = true"
          >
            列设置
          </el-button>
          
          <!-- 导出 -->
          <el-button
            v-if="showExport"
            :icon="DownloadIcon"
            @click="handleExport"
          >
            导出
          </el-button>
        </slot>
      </div>
    </div>

    <!-- 表格主体 -->
    <el-table
      ref="tableRef"
      :data="tableData"
      :loading="loading"
      :height="height"
      :max-height="maxHeight"
      :stripe="stripe"
      :border="border"
      :size="size"
      :fit="fit"
      :show-header="showHeader"
      :highlight-current-row="highlightCurrentRow"
      :row-class-name="rowClassName"
      :row-style="rowStyle"
      :cell-class-name="cellClassName"
      :cell-style="cellStyle"
      :header-row-class-name="headerRowClassName"
      :header-row-style="headerRowStyle"
      :header-cell-class-name="headerCellClassName"
      :header-cell-style="headerCellStyle"
      :row-key="rowKey"
      :empty-text="emptyText"
      :default-expand-all="defaultExpandAll"
      :expand-row-keys="expandRowKeys"
      :default-sort="defaultSort"
      :tooltip-effect="tooltipEffect"
      :show-summary="showSummary"
      :sum-text="sumText"
      :summary-method="summaryMethod"
      :span-method="spanMethod"
      :select-on-indeterminate="selectOnIndeterminate"
      :indent="indent"
      :lazy="lazy"
      :load="load"
      :tree-props="treeProps"
      @select="handleSelect"
      @select-all="handleSelectAll"
      @selection-change="handleSelectionChange"
      @cell-mouse-enter="handleCellMouseEnter"
      @cell-mouse-leave="handleCellMouseLeave"
      @cell-click="handleCellClick"
      @cell-dblclick="handleCellDblclick"
      @row-click="handleRowClick"
      @row-contextmenu="handleRowContextmenu"
      @row-dblclick="handleRowDblclick"
      @header-click="handleHeaderClick"
      @header-contextmenu="handleHeaderContextmenu"
      @sort-change="handleSortChange"
      @filter-change="handleFilterChange"
      @current-change="handleCurrentChange"
      @header-dragend="handleHeaderDragend"
      @expand-change="handleExpandChange"
    >
      <!-- 动态列 -->
      <template v-for="column in visibleColumns" :key="column.key">
        <!-- 选择列 -->
        <el-table-column
          v-if="column.type === 'selection'"
          type="selection"
          :width="column.width || 55"
          :fixed="column.fixed"
          :selectable="selectable"
        />
        
        <!-- 索引列 -->
        <el-table-column
          v-else-if="column.type === 'index'"
          type="index"
          :label="column.title"
          :width="column.width || 60"
          :fixed="column.fixed"
          :index="indexMethod"
        />
        
        <!-- 展开列 -->
        <el-table-column
          v-else-if="column.type === 'expand'"
          type="expand"
          :width="column.width || 55"
          :fixed="column.fixed"
        >
          <template #default="scope">
            <slot name="expand" :row="scope.row" :index="scope.$index" />
          </template>
        </el-table-column>
        
        <!-- 普通列 -->
        <el-table-column
          v-else
          :prop="column.key"
          :label="column.title"
          :width="column.width"
          :min-width="column.minWidth"
          :fixed="column.fixed"
          :render-header="column.renderHeader"
          :sortable="column.sortable"
          :sort-method="column.sortMethod"
          :sort-by="column.sortBy"
          :sort-orders="column.sortOrders"
          :resizable="column.resizable"
          :formatter="column.formatter"
          :show-overflow-tooltip="column.showOverflowTooltip"
          :align="column.align"
          :header-align="column.headerAlign"
          :class-name="column.className"
          :label-class-name="column.labelClassName"
          :filters="column.filters"
          :filter-placement="column.filterPlacement"
          :filter-multiple="column.filterMultiple"
          :filter-method="column.filterMethod"
          :filtered-value="column.filteredValue"
        >
          <template #default="scope">
            <!-- 自定义渲染 -->
            <template v-if="column.render">
              <component
                :is="column.render(scope.row[column.key], scope.row, scope.$index)"
              />
            </template>
            
            <!-- 插槽渲染 -->
            <template v-else-if="$slots[`column-${column.key}`]">
              <slot
                :name="`column-${column.key}`"
                :row="scope.row"
                :column="column"
                :index="scope.$index"
                :value="scope.row[column.key]"
              />
            </template>
            
            <!-- 标签类型 -->
            <template v-else-if="column.type === 'tag'">
              <el-tag
                v-if="scope.row[column.key]"
                :type="getTagType(scope.row[column.key], column)"
                :size="size"
              >
                {{ getTagLabel(scope.row[column.key], column) }}
              </el-tag>
            </template>
            
            <!-- 链接类型 -->
            <template v-else-if="column.type === 'link'">
              <el-link
                :type="column.linkType || 'primary'"
                @click="handleLinkClick(scope.row, column, scope.$index)"
              >
                {{ scope.row[column.key] }}
              </el-link>
            </template>
            
            <!-- 操作类型 -->
            <template v-else-if="column.type === 'action'">
              <slot
                name="action"
                :row="scope.row"
                :column="column"
                :index="scope.$index"
              />
            </template>
            
            <!-- 默认显示 -->
            <template v-else>
              {{ formatCellValue(scope.row[column.key], column) }}
            </template>
          </template>
        </el-table-column>
      </template>
      
      <!-- 空状态 -->
      <template #empty>
        <slot name="empty">
          <div class="table-empty">
            <el-empty :description="emptyText" />
          </div>
        </slot>
      </template>
    </el-table>

    <!-- 分页 -->
    <div v-if="showPagination && pagination" class="table-pagination">
      <el-pagination
        v-model:current-page="pagination.current"
        v-model:page-size="pagination.pageSize"
        :page-sizes="pagination.pageSizes || [10, 20, 50, 100]"
        :small="pagination.small"
        :disabled="loading"
        :background="true"
        :total="pagination.total"
        :layout="paginationLayout"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 列设置对话框 -->
    <el-dialog
      v-model="showColumnDialog"
      title="列设置"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="column-settings">
        <el-checkbox
          v-model="checkAll"
          :indeterminate="isIndeterminate"
          @change="handleCheckAllChange"
        >
          全选
        </el-checkbox>
        
        <el-divider />
        
        <el-checkbox-group v-model="checkedColumns" @change="handleCheckedColumnsChange">
          <div v-for="element in columnSettings" :key="element.key" class="column-item">
            <el-icon class="drag-handle">
              <Rank />
            </el-icon>
            <el-checkbox :label="element.key">
              {{ element.title }}
            </el-checkbox>
          </div>
        </el-checkbox-group>
      </div>
      
      <template #footer>
        <el-button @click="showColumnDialog = false">取消</el-button>
        <el-button type="primary" @click="handleColumnSettingsConfirm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { ElTable, ElTableColumn, ElButton, ElTag, ElLink, ElPagination, ElDialog, ElCheckbox, ElCheckboxGroup, ElDivider, ElEmpty, ElIcon } from 'element-plus'
import { Refresh as RefreshIcon, Setting as SettingIcon, Download as DownloadIcon, Rank } from '@element-plus/icons-vue'
// import draggable from 'vuedraggable'
import { useBaseComponent } from '@/composables/useBaseComponent'
import type { TableColumn, PaginationConfig, BaseComponentProps } from '@/types/component'

// Props定义
interface DataTableProps extends BaseComponentProps {
  // 数据
  data?: any[]
  columns: TableColumn[]
  pagination?: PaginationConfig
  
  // 表格属性
  height?: string | number
  maxHeight?: string | number
  stripe?: boolean
  border?: boolean
  fit?: boolean
  showHeader?: boolean
  highlightCurrentRow?: boolean
  rowKey?: string | ((row: any) => string)
  emptyText?: string
  defaultExpandAll?: boolean
  expandRowKeys?: any[]
  defaultSort?: { prop: string; order: string }
  tooltipEffect?: 'dark' | 'light'
  showSummary?: boolean
  sumText?: string
  summaryMethod?: (param: { columns: any[]; data: any[] }) => any[]
  spanMethod?: (param: { row: any; column: any; rowIndex: number; columnIndex: number }) => number[] | { rowspan: number; colspan: number }
  selectOnIndeterminate?: boolean
  indent?: number
  lazy?: boolean
  load?: (row: any, treeNode: any, resolve: (data: any[]) => void) => void
  treeProps?: { hasChildren?: string; children?: string }
  
  // 样式函数
  rowClassName?: string | ((param: { row: any; rowIndex: number }) => string)
  rowStyle?: any | ((param: { row: any; rowIndex: number }) => any)
  cellClassName?: string | ((param: { row: any; column: any; rowIndex: number; columnIndex: number }) => string)
  cellStyle?: any | ((param: { row: any; column: any; rowIndex: number; columnIndex: number }) => any)
  headerRowClassName?: string | ((param: { rowIndex: number }) => string)
  headerRowStyle?: any | ((param: { rowIndex: number }) => any)
  headerCellClassName?: string | ((param: { row: any; column: any; rowIndex: number; columnIndex: number }) => string)
  headerCellStyle?: any | ((param: { row: any; column: any; rowIndex: number; columnIndex: number }) => any)
  
  // 功能开关
  showToolbar?: boolean
  showRefresh?: boolean
  showColumnSettings?: boolean
  showExport?: boolean
  showPagination?: boolean
  
  // 选择相关
  selectable?: (row: any, index: number) => boolean
  
  // 索引方法
  indexMethod?: (index: number) => number
}

const props = withDefaults(defineProps<DataTableProps>(), {
  data: () => [],
  stripe: true,
  border: true,
  fit: true,
  showHeader: true,
  highlightCurrentRow: true,
  emptyText: '暂无数据',
  tooltipEffect: 'dark',
  showToolbar: true,
  showRefresh: true,
  showColumnSettings: true,
  showExport: false,
  showPagination: true
})

// Emits定义
const emit = defineEmits<{
  refresh: []
  export: []
  'selection-change': [selection: any[]]
  'sort-change': [param: { column: any; prop: string; order: string }]
  'filter-change': [filters: any]
  'row-click': [row: any, column: any, event: Event]
  'row-dblclick': [row: any, column: any, event: Event]
  'cell-click': [row: any, column: any, cell: any, event: Event]
  'cell-dblclick': [row: any, column: any, cell: any, event: Event]
  'link-click': [row: any, column: TableColumn, index: number]
  'pagination-change': [pagination: PaginationConfig]
}>()

// 使用基础组件功能
const { componentRef, componentClass, componentStyle, testAttributes } = useBaseComponent(props)

// 表格引用
const tableRef = ref<InstanceType<typeof ElTable>>()

// 表格数据
const tableData = computed(() => props.data || [])

// 加载状态
const loading = ref(false)

// 列设置相关
const showColumnDialog = ref(false)
const columnSettings = ref<TableColumn[]>([])
const checkedColumns = ref<string[]>([])
const checkAll = ref(true)
const isIndeterminate = ref(false)

// 可见列
const visibleColumns = computed(() => {
  return props.columns.filter(column => column.visible !== false)
})

// 分页布局
const paginationLayout = computed(() => {
  const layouts = ['total', 'sizes', 'prev', 'pager', 'next', 'jumper']
  return layouts.join(', ')
})

// 初始化列设置
const initColumnSettings = () => {
  columnSettings.value = [...props.columns]
  checkedColumns.value = props.columns
    .filter(col => col.visible !== false)
    .map(col => col.key)
  updateCheckAllStatus()
}

// 更新全选状态
const updateCheckAllStatus = () => {
  const checkedCount = checkedColumns.value.length
  const totalCount = columnSettings.value.length
  
  checkAll.value = checkedCount === totalCount
  isIndeterminate.value = checkedCount > 0 && checkedCount < totalCount
}

// 事件处理
const handleRefresh = () => {
  emit('refresh')
}

const handleExport = () => {
  emit('export')
}

const handleSelect = (selection: any[], row: any) => {
  // 处理单行选择
}

const handleSelectAll = (selection: any[]) => {
  // 处理全选
}

const handleSelectionChange = (selection: any[]) => {
  emit('selection-change', selection)
}

const handleSortChange = (param: { column: any; prop: string; order: string }) => {
  emit('sort-change', param)
}

const handleFilterChange = (filters: any) => {
  emit('filter-change', filters)
}

const handleRowClick = (row: any, column: any, event: Event) => {
  emit('row-click', row, column, event)
}

const handleRowDblclick = (row: any, column: any, event: Event) => {
  emit('row-dblclick', row, column, event)
}

const handleCellClick = (row: any, column: any, cell: any, event: Event) => {
  emit('cell-click', row, column, cell, event)
}

const handleCellDblclick = (row: any, column: any, cell: any, event: Event) => {
  emit('cell-dblclick', row, column, cell, event)
}

const handleLinkClick = (row: any, column: TableColumn, index: number) => {
  emit('link-click', row, column, index)
}

const handleSizeChange = (size: number) => {
  if (props.pagination) {
    const newPagination = { ...props.pagination, pageSize: size, current: 1 }
    emit('pagination-change', newPagination)
  }
}

const handleCurrentChange = (current: number) => {
  if (props.pagination) {
    const newPagination = { ...props.pagination, current }
    emit('pagination-change', newPagination)
  }
}

// 列设置相关方法
const handleCheckAllChange = (checked: boolean) => {
  checkedColumns.value = checked ? columnSettings.value.map(col => col.key) : []
  isIndeterminate.value = false
}

const handleCheckedColumnsChange = () => {
  updateCheckAllStatus()
}

const handleColumnSettingsConfirm = () => {
  // 更新列的可见性
  columnSettings.value.forEach(col => {
    col.visible = checkedColumns.value.includes(col.key)
  })
  
  showColumnDialog.value = false
}

// 工具方法
const getTagType = (value: any, column: TableColumn): string => {
  if (column.dict) {
    const dictItem = column.dict.find(item => item.value === value)
    return dictItem?.color || 'info'
  }
  return 'info'
}

const getTagLabel = (value: any, column: TableColumn): string => {
  if (column.dict) {
    const dictItem = column.dict.find(item => item.value === value)
    return dictItem?.label || value
  }
  return value
}

const formatCellValue = (value: any, column: TableColumn): string => {
  if (column.formatter) {
    return column.formatter(value, {}, 0)
  }
  
  if (value === null || value === undefined) {
    return ''
  }
  
  if (column.type === 'date' && value) {
    return new Date(value).toLocaleDateString()
  }
  
  if (column.type === 'datetime' && value) {
    return new Date(value).toLocaleString()
  }
  
  return String(value)
}

// 其他事件处理方法（简化版）
const handleCellMouseEnter = () => {}
const handleCellMouseLeave = () => {}
const handleRowContextmenu = () => {}
const handleHeaderClick = () => {}
const handleHeaderContextmenu = () => {}
const handleHeaderDragend = () => {}
const handleExpandChange = () => {}

// 生命周期
onMounted(() => {
  initColumnSettings()
})

// 监听列变化
watch(() => props.columns, () => {
  initColumnSettings()
}, { deep: true })

// 暴露方法
defineExpose({
  tableRef,
  clearSelection: () => tableRef.value?.clearSelection(),
  toggleRowSelection: (row: any, selected?: boolean) => tableRef.value?.toggleRowSelection(row, selected),
  toggleAllSelection: () => tableRef.value?.toggleAllSelection(),
  toggleRowExpansion: (row: any, expanded?: boolean) => tableRef.value?.toggleRowExpansion(row, expanded),
  setCurrentRow: (row: any) => tableRef.value?.setCurrentRow(row),
  clearSort: () => tableRef.value?.clearSort(),
  clearFilter: (columnKeys?: string[]) => tableRef.value?.clearFilter(columnKeys),
  doLayout: () => tableRef.value?.doLayout(),
  sort: (prop: string, order: string) => tableRef.value?.sort(prop, order)
})
</script>

<style lang="scss" scoped>
.data-table {
  .table-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .toolbar-left,
    .toolbar-right {
      display: flex;
      gap: 8px;
    }
  }
  
  .table-pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 16px;
  }
  
  .table-empty {
    padding: 40px 0;
  }
  
  .column-settings {
    .column-item {
      display: flex;
      align-items: center;
      padding: 8px 0;
      
      .drag-handle {
        margin-right: 8px;
        cursor: move;
        color: #909399;
      }
    }
  }
}
</style>
