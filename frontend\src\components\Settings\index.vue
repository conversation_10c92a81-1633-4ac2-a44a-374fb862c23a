<template>
  <div class="settings-container">
    <el-icon class="settings-icon">
      <Setting />
    </el-icon>
  </div>
</template>

<script setup>
import { Setting } from '@element-plus/icons-vue'
</script>

<style scoped>
.settings-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.settings-container:hover {
  background-color: var(--el-color-primary-light-9);
}

.settings-icon {
  font-size: 18px;
  color: var(--el-text-color-regular);
}

.settings-container:hover .settings-icon {
  color: var(--el-color-primary);
}
</style>
