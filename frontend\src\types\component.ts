/**
 * 组件通用类型定义
 * 定义组件的标准化接口和类型
 */

import type { PropType, VNode } from 'vue'

/**
 * 组件大小枚举
 */
export type ComponentSize = 'large' | 'default' | 'small'

/**
 * 组件状态枚举
 */
export type ComponentStatus = 'success' | 'warning' | 'danger' | 'info'

/**
 * 组件主题枚举
 */
export type ComponentTheme = 'primary' | 'success' | 'warning' | 'danger' | 'info'

/**
 * 基础组件Props接口
 */
export interface BaseComponentProps {
  /**
   * 组件ID
   */
  id?: string
  
  /**
   * 自定义类名
   */
  class?: string | string[] | Record<string, boolean>
  
  /**
   * 自定义样式
   */
  style?: string | Record<string, string | number>
  
  /**
   * 是否禁用
   */
  disabled?: boolean
  
  /**
   * 是否只读
   */
  readonly?: boolean
  
  /**
   * 组件大小
   */
  size?: ComponentSize
  
  /**
   * 是否显示加载状态
   */
  loading?: boolean
  
  /**
   * 测试ID，用于自动化测试
   */
  testId?: string
}

/**
 * 表单组件Props接口
 */
export interface FormComponentProps extends BaseComponentProps {
  /**
   * 字段名
   */
  name?: string
  
  /**
   * 字段值
   */
  modelValue?: any
  
  /**
   * 占位符
   */
  placeholder?: string
  
  /**
   * 是否必填
   */
  required?: boolean
  
  /**
   * 验证规则
   */
  rules?: ValidationRule[]
  
  /**
   * 错误信息
   */
  error?: string
  
  /**
   * 帮助文本
   */
  help?: string
  
  /**
   * 标签文本
   */
  label?: string
  
  /**
   * 标签宽度
   */
  labelWidth?: string | number
}

/**
 * 验证规则接口
 */
export interface ValidationRule {
  /**
   * 规则类型
   */
  type?: 'required' | 'email' | 'url' | 'number' | 'integer' | 'float' | 'array' | 'object' | 'enum' | 'date' | 'boolean' | 'string' | 'method' | 'regexp'
  
  /**
   * 是否必填
   */
  required?: boolean
  
  /**
   * 错误信息
   */
  message?: string
  
  /**
   * 最小长度/值
   */
  min?: number
  
  /**
   * 最大长度/值
   */
  max?: number
  
  /**
   * 长度
   */
  len?: number
  
  /**
   * 正则表达式
   */
  pattern?: RegExp
  
  /**
   * 自定义验证函数
   */
  validator?: (rule: ValidationRule, value: any, callback: (error?: string) => void) => void
  
  /**
   * 触发验证的事件
   */
  trigger?: 'blur' | 'change' | 'submit'
}

/**
 * 表格列配置接口
 */
export interface TableColumn {
  /**
   * 列键
   */
  key: string
  
  /**
   * 列标题
   */
  title: string
  
  /**
   * 列宽度
   */
  width?: string | number
  
  /**
   * 最小宽度
   */
  minWidth?: string | number
  
  /**
   * 是否可排序
   */
  sortable?: boolean
  
  /**
   * 是否可筛选
   */
  filterable?: boolean
  
  /**
   * 对齐方式
   */
  align?: 'left' | 'center' | 'right'
  
  /**
   * 是否固定列
   */
  fixed?: 'left' | 'right' | boolean
  
  /**
   * 自定义渲染函数
   */
  render?: (value: any, row: any, index: number) => VNode | string
  
  /**
   * 格式化函数
   */
  formatter?: (value: any, row: any, index: number) => string
  
  /**
   * 是否显示
   */
  visible?: boolean
  
  /**
   * 列类型
   */
  type?: 'text' | 'number' | 'date' | 'datetime' | 'boolean' | 'tag' | 'link' | 'action'
  
  /**
   * 字典数据（用于枚举类型）
   */
  dict?: Array<{ label: string; value: any; color?: string }>
}

/**
 * 分页配置接口
 */
export interface PaginationConfig {
  /**
   * 当前页码
   */
  current: number
  
  /**
   * 每页条数
   */
  pageSize: number
  
  /**
   * 总条数
   */
  total: number
  
  /**
   * 每页条数选项
   */
  pageSizes?: number[]
  
  /**
   * 是否显示总条数
   */
  showTotal?: boolean
  
  /**
   * 是否显示每页条数选择器
   */
  showSizeChanger?: boolean
  
  /**
   * 是否显示快速跳转
   */
  showQuickJumper?: boolean
  
  /**
   * 是否显示页码
   */
  showPager?: boolean
  
  /**
   * 小型分页
   */
  small?: boolean
}

/**
 * 对话框配置接口
 */
export interface DialogConfig {
  /**
   * 标题
   */
  title?: string
  
  /**
   * 宽度
   */
  width?: string | number
  
  /**
   * 是否可拖拽
   */
  draggable?: boolean
  
  /**
   * 是否显示关闭按钮
   */
  showClose?: boolean
  
  /**
   * 是否点击遮罩关闭
   */
  closeOnClickModal?: boolean
  
  /**
   * 是否按ESC关闭
   */
  closeOnPressEscape?: boolean
  
  /**
   * 是否显示遮罩
   */
  modal?: boolean
  
  /**
   * 是否全屏
   */
  fullscreen?: boolean
  
  /**
   * 是否居中
   */
  center?: boolean
  
  /**
   * 自定义类名
   */
  customClass?: string
}

/**
 * 组件事件接口
 */
export interface ComponentEvents {
  /**
   * 点击事件
   */
  click?: (event: MouseEvent) => void
  
  /**
   * 双击事件
   */
  dblclick?: (event: MouseEvent) => void
  
  /**
   * 鼠标进入事件
   */
  mouseenter?: (event: MouseEvent) => void
  
  /**
   * 鼠标离开事件
   */
  mouseleave?: (event: MouseEvent) => void
  
  /**
   * 焦点事件
   */
  focus?: (event: FocusEvent) => void
  
  /**
   * 失焦事件
   */
  blur?: (event: FocusEvent) => void
  
  /**
   * 值变化事件
   */
  change?: (value: any, oldValue?: any) => void
  
  /**
   * 输入事件
   */
  input?: (value: any) => void
}

/**
 * 组件插槽接口
 */
export interface ComponentSlots {
  /**
   * 默认插槽
   */
  default?: () => VNode[]
  
  /**
   * 头部插槽
   */
  header?: () => VNode[]
  
  /**
   * 底部插槽
   */
  footer?: () => VNode[]
  
  /**
   * 前缀插槽
   */
  prefix?: () => VNode[]
  
  /**
   * 后缀插槽
   */
  suffix?: () => VNode[]
  
  /**
   * 空状态插槽
   */
  empty?: () => VNode[]
  
  /**
   * 加载状态插槽
   */
  loading?: () => VNode[]
}
