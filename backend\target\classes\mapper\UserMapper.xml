<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.linkapi.mapper.UserMapper">

    <!-- 用户结果映射 -->
    <resultMap id="UserResultMap" type="com.linkapi.entity.User">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="real_name" property="realName" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="avatar" property="avatar" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="is_admin" property="isAdmin" jdbcType="BOOLEAN"/>
        <result column="last_login_time" property="lastLoginTime" jdbcType="TIMESTAMP"/>
        <result column="last_login_ip" property="lastLoginIp" jdbcType="VARCHAR"/>
        <result column="login_count" property="loginCount" jdbcType="INTEGER"/>
        <result column="is_deleted" property="isDeleted" jdbcType="BOOLEAN"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="BIGINT"/>
        <result column="update_by" property="updateBy" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 用户角色结果映射 -->
    <resultMap id="UserWithRolesResultMap" type="com.linkapi.entity.User" extends="UserResultMap">
        <collection property="roles" ofType="com.linkapi.entity.Role">
            <id column="role_id" property="id" jdbcType="BIGINT"/>
            <result column="role_code" property="roleCode" jdbcType="VARCHAR"/>
            <result column="role_name" property="roleName" jdbcType="VARCHAR"/>
            <result column="role_status" property="status" jdbcType="TINYINT"/>
        </collection>
    </resultMap>

    <!-- 用户权限结果映射 -->
    <resultMap id="UserWithPermissionsResultMap" type="com.linkapi.entity.User" extends="UserResultMap">
        <collection property="permissions" ofType="com.linkapi.entity.Permission">
            <id column="permission_id" property="id" jdbcType="BIGINT"/>
            <result column="permission_code" property="permissionCode" jdbcType="VARCHAR"/>
            <result column="permission_name" property="permissionName" jdbcType="VARCHAR"/>
            <result column="permission_type" property="type" jdbcType="TINYINT"/>
        </collection>
    </resultMap>

    <!-- 根据用户名查询用户（含角色和权限） -->
    <select id="findByUsernameWithRoles" parameterType="string" resultMap="UserWithRolesResultMap">
        SELECT 
            u.id, u.username, u.password, u.real_name, u.email, u.phone, u.avatar,
            u.status, u.last_login_time, u.last_login_ip, u.login_count,
            u.deleted, u.created_time, u.updated_time, u.created_by, u.updated_by,
            r.id as role_id, r.role_code, r.role_name, r.status as role_status
        FROM users u
        LEFT JOIN user_roles ur ON u.id = ur.user_id
        LEFT JOIN roles r ON ur.role_id = r.id AND r.deleted = 0
        WHERE u.username = #{username} AND u.deleted = 0
    </select>

    <!-- 根据用户名查询用户 -->
    <select id="findByUsername" parameterType="string" resultMap="UserResultMap">
        SELECT 
            id, username, password, real_name, email, phone, avatar,
            status, last_login_time, last_login_ip, login_count,
            deleted, created_time, updated_time, created_by, updated_by
        FROM users 
        WHERE username = #{username} AND deleted = 0
    </select>

    <!-- 根据邮箱查询用户 -->
    <select id="findByEmail" parameterType="string" resultMap="UserResultMap">
        SELECT 
            id, username, password, real_name, email, phone, avatar,
            status, is_admin, last_login_time, last_login_ip, login_count,
            is_deleted, create_time, update_time, create_by, update_by
        FROM users 
        WHERE email = #{email} AND deleted = 0
    </select>

    <!-- 根据手机号查询用户 -->
    <select id="findByPhone" parameterType="string" resultMap="UserResultMap">
        SELECT 
            id, username, password, real_name, email, phone, avatar,
            status, is_admin, last_login_time, last_login_ip, login_count,
            is_deleted, create_time, update_time, create_by, update_by
        FROM users 
        WHERE phone = #{phone} AND deleted = 0
    </select>

    <!-- 根据用户ID查询角色列表 -->
    <select id="findRolesByUserId" parameterType="long" resultType="com.linkapi.entity.Role">
        SELECT 
            r.id, r.role_code, r.role_name, r.description, r.status,
            r.deleted, r.created_time, r.updated_time, r.created_by, r.updated_by
        FROM roles r
        INNER JOIN user_roles ur ON r.id = ur.role_id
        WHERE ur.user_id = #{userId} AND r.deleted = 0
        ORDER BY r.created_time
    </select>

    <!-- 根据用户ID查询权限列表 -->
    <select id="findPermissionsByUserId" parameterType="long" resultType="com.linkapi.entity.Permission">
        SELECT DISTINCT
            p.id, p.permission_code, p.permission_name, p.permission_type, p.parent_id,
            p.path, p.component, p.icon, p.sort_order, p.status,
            p.deleted, p.created_time, p.updated_time, p.created_by, p.updated_by
        FROM permissions p
        INNER JOIN role_permissions rp ON p.id = rp.permission_id
        INNER JOIN user_roles ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = #{userId} 
          AND p.deleted = 0
        ORDER BY p.sort_order, p.created_time
    </select>

    <!-- 检查用户名是否存在 -->
    <select id="existsByUsername" parameterType="string" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM users 
        WHERE username = #{username} AND deleted = 0
    </select>

    <!-- 检查邮箱是否存在 -->
    <select id="existsByEmail" parameterType="string" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM users 
        WHERE email = #{email} AND deleted = 0
    </select>

    <!-- 检查手机号是否存在 -->
    <select id="existsByPhone" parameterType="string" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM users 
        WHERE phone = #{phone} AND deleted = 0
    </select>

    <!-- 更新用户最后登录信息 -->
    <update id="updateLastLoginInfo">
        UPDATE users 
        SET last_login_time = #{loginTime},
            last_login_ip = #{loginIp},
            login_count = login_count + 1,
            updated_time = NOW()
        WHERE id = #{userId}
    </update>

    <!-- 插入用户 -->
    <insert id="insert" parameterType="com.linkapi.entity.User" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO users (
            username, password, real_name, email, phone, avatar,
            status, deleted, created_time, updated_time, created_by, updated_by
        ) VALUES (
            #{username}, #{password}, #{realName}, #{email}, #{phone}, #{avatar},
            #{status}, 0, NOW(), NOW(), #{createdBy}, #{updatedBy}
        )
    </insert>

    <!-- 更新用户 -->
    <update id="update" parameterType="com.linkapi.entity.User">
        UPDATE users 
        SET real_name = #{realName},
            email = #{email},
            phone = #{phone},
            avatar = #{avatar},
            status = #{status},
            updated_time = NOW(),
            updated_by = #{updatedBy}
        WHERE id = #{id}
    </update>

    <!-- 更新用户密码 -->
    <update id="updatePassword">
        UPDATE users 
        SET password = #{password},
            updated_time = NOW(),
            updated_by = #{updatedBy}
        WHERE id = #{userId}
    </update>

    <!-- 更新用户状态 -->
    <update id="updateStatus">
        UPDATE users 
        SET status = #{status},
            updated_time = NOW(),
            updated_by = #{updatedBy}
        WHERE id = #{userId}
    </update>

    <!-- 逻辑删除用户 -->
    <update id="deleteById">
        UPDATE users 
        SET deleted = 1,
            updated_time = NOW(),
            updated_by = #{updatedBy}
        WHERE id = #{userId}
    </update>

    <!-- 分页查询用户列表 -->
    <select id="selectPage" resultMap="UserResultMap">
        SELECT 
            id, username, real_name, email, phone, avatar,
            status, last_login_time, last_login_ip, login_count,
            created_time, updated_time
        FROM users 
        WHERE deleted = 0
        <if test="username != null and username != ''">
            AND username LIKE CONCAT('%', #{username}, '%')
        </if>
        <if test="realName != null and realName != ''">
            AND real_name LIKE CONCAT('%', #{realName}, '%')
        </if>
        <if test="email != null and email != ''">
            AND email LIKE CONCAT('%', #{email}, '%')
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY created_time DESC
    </select>

</mapper>