package com.linkapi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.linkapi.entity.Permission;
import com.linkapi.entity.Role;
import com.linkapi.entity.User;
import com.linkapi.entity.LoginLog;
import com.linkapi.entity.UserRole;
import com.linkapi.mapper.UserMapper;
import com.linkapi.mapper.LoginLogMapper;
import com.linkapi.mapper.UserRoleMapper;
import com.linkapi.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private LoginLogMapper loginLogMapper;
    
    @Autowired
    private UserRoleMapper userRoleMapper;

    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    @Override
    public User login(String username, String password, String loginIp) {
        log.info("用户登录尝试: username={}, ip={}", username, loginIp);
        
        try {
            // 查询用户
            User user = findByUsernameWithRoles(username);
            if (user == null) {
                log.warn("用户不存在: {}", username);
                // 记录登录失败日志
                recordLoginLog(null, username, loginIp, LoginLog.Status.FAILED.getCode(), "用户不存在", null);
                throw new RuntimeException("用户名或密码错误");
            }
            
            // 检查用户状态
            if (!User.Status.ACTIVE.getCode().equals(user.getStatus())) {
                log.warn("用户状态异常: username={}, status={}", username, user.getStatus());
                String failureReason;
                if (User.Status.LOCKED.getCode().equals(user.getStatus())) {
                    failureReason = "账户已被锁定";
                    recordLoginLog(user.getId(), username, loginIp, LoginLog.Status.FAILED.getCode(), failureReason, null);
                    throw new RuntimeException("账户已被锁定，请联系管理员");
                } else {
                    failureReason = "账户已被禁用";
                    recordLoginLog(user.getId(), username, loginIp, LoginLog.Status.FAILED.getCode(), failureReason, null);
                    throw new RuntimeException("账户已被禁用，请联系管理员");
                }
            }
            
            // 验证密码
            if (!verifyPassword(password, user.getPassword())) {
                log.warn("密码错误: {}", username);
                // 记录登录失败日志
                recordLoginLog(user.getId(), username, loginIp, LoginLog.Status.FAILED.getCode(), "密码错误", null);
                throw new RuntimeException("用户名或密码错误");
            }
            
            // 更新登录信息
            userMapper.updateLastLoginInfo(user.getId(), LocalDateTime.now(), loginIp);
            
            // 记录登录成功日志
            recordLoginLog(user.getId(), username, loginIp, LoginLog.Status.SUCCESS.getCode(), null, null);
            
            // 处理角色和权限信息
            processUserRolesAndPermissions(user);
            
            log.info("用户登录成功: username={}, userId={}", username, user.getId());
            return user;
        } catch (RuntimeException e) {
            // 如果是我们抛出的业务异常，直接重新抛出
            throw e;
        } catch (Exception e) {
            // 记录系统异常登录失败日志
            log.error("登录过程中发生系统异常: username={}", username, e);
            recordLoginLog(null, username, loginIp, LoginLog.Status.FAILED.getCode(), "系统异常: " + e.getMessage(), null);
            throw new RuntimeException("登录失败，请稍后重试");
        }
    }

    @Override
    public User findByUsernameWithRoles(String username) {
        User user = userMapper.findByUsernameWithRoles(username);
        if (user != null) {
            processUserRolesAndPermissions(user);
        }
        return user;
    }

    @Override
    public List<Role> findRolesByUserId(Long userId) {
        return userMapper.findRolesByUserId(userId);
    }

    @Override
    public List<Permission> findPermissionsByUserId(Long userId) {
        return userMapper.findPermissionsByUserId(userId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createUser(User user, List<Long> roleIds) {
        try {
            // 检查用户名是否存在
            if (isUsernameExists(user.getUsername(), null)) {
                throw new RuntimeException("用户名已存在");
            }
            
            // 检查邮箱是否存在
            if (StringUtils.hasText(user.getEmail()) && isEmailExists(user.getEmail(), null)) {
                throw new RuntimeException("邮箱已存在");
            }
            
            // 加密密码
            user.setPassword(encodePassword(user.getPassword()));
            user.setPasswordUpdateTime(LocalDateTime.now());
            
            // 设置默认状态
            if (!StringUtils.hasText(user.getStatus())) {
                user.setStatus(User.Status.ACTIVE.getCode());
            }
            
            // 保存用户
            boolean result = save(user);
            
            // 分配角色
            if (result && roleIds != null && !roleIds.isEmpty()) {
                assignRolesToUser(user.getId(), roleIds);
            }
            
            log.info("创建用户成功: username={}, userId={}", user.getUsername(), user.getId());
            return result;
        } catch (Exception e) {
            log.error("创建用户失败: username={}", user.getUsername(), e);
            throw new RuntimeException("创建用户失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUser(User user, List<Long> roleIds) {
        try {
            // 检查用户名是否存在
            if (isUsernameExists(user.getUsername(), user.getId())) {
                throw new RuntimeException("用户名已存在");
            }
            
            // 检查邮箱是否存在
            if (StringUtils.hasText(user.getEmail()) && isEmailExists(user.getEmail(), user.getId())) {
                throw new RuntimeException("邮箱已存在");
            }
            
            // 如果密码不为空，则加密密码
            if (StringUtils.hasText(user.getPassword())) {
                user.setPassword(encodePassword(user.getPassword()));
                user.setPasswordUpdateTime(LocalDateTime.now());
            } else {
                // 不更新密码字段
                user.setPassword(null);
            }
            
            // 更新用户
            boolean result = updateById(user);
            
            // 重新分配角色
            if (result && roleIds != null) {
                removeUserRoles(user.getId(), null); // 移除所有角色
                if (!roleIds.isEmpty()) {
                    assignRolesToUser(user.getId(), roleIds);
                }
            }
            
            log.info("更新用户成功: username={}, userId={}", user.getUsername(), user.getId());
            return result;
        } catch (Exception e) {
            log.error("更新用户失败: username={}, userId={}", user.getUsername(), user.getId(), e);
            throw new RuntimeException("更新用户失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUser(Long userId) {
        try {
            // 删除用户角色关联
            removeUserRoles(userId, null); // 移除所有角色
            
            // 逻辑删除用户
            boolean result = removeById(userId);
            
            log.info("删除用户成功: userId={}", userId);
            return result;
        } catch (Exception e) {
            log.error("删除用户失败: userId={}", userId, e);
            throw new RuntimeException("删除用户失败: " + e.getMessage());
        }
    }

    @Override
    public boolean resetPassword(Long userId, String newPassword) {
        try {
            User user = new User();
            user.setId(userId);
            user.setPassword(encodePassword(newPassword));
            user.setPasswordUpdateTime(LocalDateTime.now());
            
            boolean result = updateById(user);
            log.info("重置密码成功: userId={}", userId);
            return result;
        } catch (Exception e) {
            log.error("重置密码失败: userId={}", userId, e);
            throw new RuntimeException("重置密码失败: " + e.getMessage());
        }
    }

    @Override
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        try {
            User user = getById(userId);
            if (user == null) {
                throw new RuntimeException("用户不存在");
            }
            
            // 验证旧密码
            if (!verifyPassword(oldPassword, user.getPassword())) {
                throw new RuntimeException("原密码错误");
            }
            
            // 更新密码
            user.setPassword(encodePassword(newPassword));
            user.setPasswordUpdateTime(LocalDateTime.now());
            
            boolean result = updateById(user);
            log.info("修改密码成功: userId={}", userId);
            return result;
        } catch (Exception e) {
            log.error("修改密码失败: userId={}", userId, e);
            throw new RuntimeException("修改密码失败: " + e.getMessage());
        }
    }

    @Override
    public boolean lockUser(Long userId, boolean locked) {
        try {
            User user = new User();
            user.setId(userId);
            user.setStatus(locked ? User.Status.LOCKED.getCode() : User.Status.ACTIVE.getCode());
            
            boolean result = updateById(user);
            log.info("{}用户成功: userId={}", locked ? "锁定" : "解锁", userId);
            return result;
        } catch (Exception e) {
            log.error("{}用户失败: userId={}", locked ? "锁定" : "解锁", userId, e);
            throw new RuntimeException((locked ? "锁定" : "解锁") + "用户失败: " + e.getMessage());
        }
    }

    @Override
    public boolean isUsernameExists(String username, Long excludeId) {
        return userMapper.countByUsername(username, excludeId) > 0;
    }

    @Override
    public boolean isEmailExists(String email, Long excludeId) {
        return userMapper.countByEmail(email, excludeId) > 0;
    }

    @Override
    public boolean verifyPassword(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    @Override
    public String encodePassword(String rawPassword) {
        return passwordEncoder.encode(rawPassword);
    }

    /**
     * 处理用户角色和权限信息
     */
    private void processUserRolesAndPermissions(User user) {
        // 查询角色和权限
        List<Role> roles = findRolesByUserId(user.getId());
        List<Permission> permissions = findPermissionsByUserId(user.getId());
        
        // 设置角色和权限
        user.setRoles(roles);
        user.setPermissions(permissions);
        
        // 设置角色编码列表
        if (roles != null && !roles.isEmpty()) {
            List<String> roleCodes = roles.stream()
                    .map(Role::getRoleCode)
                    .collect(Collectors.toList());
            user.setRoleCodes(roleCodes);
            
            // 判断是否为管理员
            user.setIsAdmin(roleCodes.contains("ADMIN"));
        }
        
        // 设置权限编码列表
        if (permissions != null && !permissions.isEmpty()) {
            List<String> permissionCodes = permissions.stream()
                    .map(Permission::getPermissionCode)
                    .collect(Collectors.toList());
            user.setPermissionCodes(permissionCodes);
        }
    }

    /**
     * 为用户分配角色
     */
    public void assignRolesToUser(Long userId, List<Long> roleIds) {
        log.info("为用户分配角色: userId={}, roleIds={}", userId, roleIds);
        
        if (roleIds == null || roleIds.isEmpty()) {
            log.warn("角色ID列表为空，跳过分配");
            return;
        }
        
        // 构建用户角色关联对象列表
        List<UserRole> userRoles = new ArrayList<>();
        for (Long roleId : roleIds) {
            UserRole userRole = new UserRole();
            userRole.setUserId(userId);
            userRole.setRoleId(roleId);
            userRoles.add(userRole);
        }
        
        // 批量插入用户角色关联
        int insertCount = userRoleMapper.batchInsert(userId, roleIds);
        log.info("成功为用户分配{}个角色: userId={}", insertCount, userId);
    }

    /**
     * 移除用户的所有角色关联
     */
    public void removeUserRoles(Long userId, List<Long> roleIds) {
        log.info("移除用户角色: userId={}, roleIds={}", userId, roleIds);
        
        if (roleIds == null || roleIds.isEmpty()) {
            // 如果roleIds为空，移除用户的所有角色
            int deleteCount = userRoleMapper.deleteByUserId(userId);
            log.info("移除用户所有角色: userId={}, 删除数量={}", userId, deleteCount);
        } else {
            // 移除指定角色
            int deleteCount = 0;
            for (Long roleId : roleIds) {
                deleteCount += userRoleMapper.deleteByUserIdAndRoleId(userId, roleId);
            }
            log.info("移除用户指定角色: userId={}, roleIds={}, 删除数量={}", userId, roleIds, deleteCount);
        }
    }
    
    /**
     * 记录登录日志
     */
    private void recordLoginLog(Long userId, String username, String loginIp, String status, String failureReason, String userAgent) {
        try {
            LoginLog loginLog = new LoginLog();
            loginLog.setUserId(userId);
            loginLog.setUsername(username);
            loginLog.setLoginIp(loginIp);
            loginLog.setLoginStatus(status);
            loginLog.setFailureReason(failureReason);
            loginLog.setUserAgent(userAgent);
            loginLog.setLoginTime(LocalDateTime.now());
            
            loginLogMapper.insertLoginLog(
                loginLog.getUserId(),
                loginLog.getUsername(),
                loginLog.getLoginIp(),
                loginLog.getLoginTime(),
                loginLog.getLoginStatus(),
                loginLog.getFailureReason(),
                null // userAgent parameter kept for method signature compatibility
            );
            log.debug("登录日志记录成功: username={}, status={}", username, status);
        } catch (Exception e) {
            // 登录日志记录失败不应该影响主流程
            log.error("记录登录日志失败: username={}, status={}", username, status, e);
        }
    }
}