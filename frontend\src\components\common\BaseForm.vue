<template>
  <div 
    ref="componentRef"
    :class="componentClass"
    :style="componentStyle"
    v-bind="testAttributes"
    class="base-form"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-width="labelWidth"
      :label-position="labelPosition"
      :label-suffix="labelSuffix"
      :hide-required-asterisk="hideRequiredAsterisk"
      :show-message="showMessage"
      :inline-message="inlineMessage"
      :status-icon="statusIcon"
      :validate-on-rule-change="validateOnRuleChange"
      :size="size"
      :disabled="disabled || loading"
      @validate="handleValidate"
    >
      <!-- 动态表单项 -->
      <template v-for="field in formFields" :key="field.name">
        <el-form-item
          :prop="field.name"
          :label="field.label"
          :label-width="field.labelWidth"
          :required="field.required"
          :rules="field.rules"
          :error="field.error"
          :show-message="field.showMessage !== false"
          :inline-message="field.inlineMessage"
          :size="field.size || size"
          :class="field.className"
          :style="field.style"
        >
          <!-- 插槽优先 -->
          <template v-if="$slots[`field-${field.name}`]">
            <slot
              :name="`field-${field.name}`"
              :field="field"
              :value="formData[field.name]"
              :update-value="(value: any) => updateFieldValue(field.name, value)"
            />
          </template>
          
          <!-- 自定义渲染函数 -->
          <template v-else-if="field.render">
            <component
              :is="field.render(field, formData[field.name], (value: any) => updateFieldValue(field.name, value))"
            />
          </template>
          
          <!-- 内置组件类型 -->
          <template v-else>
            <!-- 输入框 -->
            <el-input
              v-if="field.type === 'input' || !field.type"
              v-model="formData[field.name]"
              :type="field.inputType || 'text'"
              :placeholder="field.placeholder"
              :clearable="field.clearable !== false"
              :show-password="field.showPassword"
              :show-word-limit="field.showWordLimit"
              :maxlength="field.maxlength"
              :minlength="field.minlength"
              :prefix-icon="field.prefixIcon"
              :suffix-icon="field.suffixIcon"
              :readonly="field.readonly"
              :disabled="field.disabled"
              :size="field.size || size"
              :rows="field.rows"
              :autosize="field.autosize"
              :autocomplete="field.autocomplete"
              :resize="field.resize"
              @input="handleFieldInput(field, $event)"
              @change="handleFieldChange(field, $event)"
              @blur="handleFieldBlur(field, $event)"
              @focus="handleFieldFocus(field, $event)"
            >
              <template v-if="field.prepend" #prepend>
                {{ field.prepend }}
              </template>
              <template v-if="field.append" #append>
                {{ field.append }}
              </template>
            </el-input>
            
            <!-- 数字输入框 -->
            <el-input-number
              v-else-if="field.type === 'number'"
              v-model="formData[field.name]"
              :min="field.min"
              :max="field.max"
              :step="field.step"
              :step-strictly="field.stepStrictly"
              :precision="field.precision"
              :size="field.size || size"
              :disabled="field.disabled"
              :controls="field.controls !== false"
              :controls-position="field.controlsPosition"
              :placeholder="field.placeholder"
              @change="handleFieldChange(field, $event)"
              @blur="handleFieldBlur(field, $event)"
              @focus="handleFieldFocus(field, $event)"
            />
            
            <!-- 选择器 -->
            <el-select
              v-else-if="field.type === 'select'"
              v-model="formData[field.name]"
              :multiple="field.multiple"
              :disabled="field.disabled"
              :value-key="field.valueKey"
              :size="field.size || size"
              :clearable="field.clearable !== false"
              :collapse-tags="field.collapseTags"
              :collapse-tags-tooltip="field.collapseTagsTooltip"
              :multiple-limit="field.multipleLimit"
              :name="field.name"
              :autocomplete="field.autocomplete"
              :placeholder="field.placeholder"
              :filterable="field.filterable"
              :allow-create="field.allowCreate"
              :filter-method="field.filterMethod"
              :remote="field.remote"
              :remote-method="field.remoteMethod"
              :loading="field.loading"
              :loading-text="field.loadingText"
              :no-match-text="field.noMatchText"
              :no-data-text="field.noDataText"
              :popper-class="field.popperClass"
              :reserve-keyword="field.reserveKeyword"
              :default-first-option="field.defaultFirstOption"
              :teleported="field.teleported"
              :persistent="field.persistent"
              :automatic-dropdown="field.automaticDropdown"
              :fit-input-width="field.fitInputWidth"
              :suffix-icon="field.suffixIcon"
              :tag-type="field.tagType"
              @change="handleFieldChange(field, $event)"
              @visible-change="handleSelectVisibleChange(field, $event)"
              @remove-tag="handleSelectRemoveTag(field, $event)"
              @clear="handleSelectClear(field)"
              @blur="handleFieldBlur(field, $event)"
              @focus="handleFieldFocus(field, $event)"
            >
              <el-option
                v-for="option in field.options"
                :key="option.value"
                :label="option.label"
                :value="option.value"
                :disabled="option.disabled"
              />
            </el-select>
            
            <!-- 日期选择器 -->
            <el-date-picker
              v-else-if="field.type === 'date' || field.type === 'datetime' || field.type === 'daterange'"
              v-model="formData[field.name]"
              :type="field.type"
              :readonly="field.readonly"
              :disabled="field.disabled"
              :editable="field.editable"
              :clearable="field.clearable !== false"
              :size="field.size || size"
              :placeholder="field.placeholder"
              :start-placeholder="field.startPlaceholder"
              :end-placeholder="field.endPlaceholder"
              :time-arrow-control="field.timeArrowControl"
              :format="field.format"
              :value-format="field.valueFormat"
              :popper-class="field.popperClass"
              :range-separator="field.rangeSeparator"
              :default-value="field.defaultValue"
              :default-time="field.defaultTime"
              :disabled-date="field.disabledDate"
              :shortcuts="field.shortcuts"
              :cell-class-name="field.cellClassName"
              @change="handleFieldChange(field, $event)"
              @blur="handleFieldBlur(field, $event)"
              @focus="handleFieldFocus(field, $event)"
            />
            
            <!-- 时间选择器 -->
            <el-time-picker
              v-else-if="field.type === 'time'"
              v-model="formData[field.name]"
              :readonly="field.readonly"
              :disabled="field.disabled"
              :editable="field.editable"
              :clearable="field.clearable !== false"
              :size="field.size || size"
              :placeholder="field.placeholder"
              :start-placeholder="field.startPlaceholder"
              :end-placeholder="field.endPlaceholder"
              :is-range="field.isRange"
              :arrow-control="field.arrowControl"
              :popper-class="field.popperClass"
              :range-separator="field.rangeSeparator"
              :format="field.format"
              :value-format="field.valueFormat"
              :default-value="field.defaultValue"
              :disabled-hours="field.disabledHours"
              :disabled-minutes="field.disabledMinutes"
              :disabled-seconds="field.disabledSeconds"
              @change="handleFieldChange(field, $event)"
              @blur="handleFieldBlur(field, $event)"
              @focus="handleFieldFocus(field, $event)"
            />
            
            <!-- 开关 -->
            <el-switch
              v-else-if="field.type === 'switch'"
              v-model="formData[field.name]"
              :disabled="field.disabled"
              :loading="field.loading"
              :size="field.size || size"
              :width="field.width"
              :inline-prompt="field.inlinePrompt"
              :active-icon="field.activeIcon"
              :inactive-icon="field.inactiveIcon"
              :active-text="field.activeText"
              :inactive-text="field.inactiveText"
              :active-value="field.activeValue"
              :inactive-value="field.inactiveValue"
              :active-color="field.activeColor"
              :inactive-color="field.inactiveColor"
              :border-color="field.borderColor"
              :name="field.name"
              :validate-event="field.validateEvent"
              @change="handleFieldChange(field, $event)"
            />
            
            <!-- 单选框组 -->
            <el-radio-group
              v-else-if="field.type === 'radio'"
              v-model="formData[field.name]"
              :size="field.size || size"
              :disabled="field.disabled"
              :text-color="field.textColor"
              :fill="field.fill"
              @change="handleFieldChange(field, $event)"
            >
              <el-radio
                v-for="option in field.options"
                :key="option.value"
                :label="option.value"
                :disabled="option.disabled"
                :border="field.border"
                :size="field.size || size"
              >
                {{ option.label }}
              </el-radio>
            </el-radio-group>
            
            <!-- 多选框组 -->
            <el-checkbox-group
              v-else-if="field.type === 'checkbox'"
              v-model="formData[field.name]"
              :size="field.size || size"
              :disabled="field.disabled"
              :min="field.min"
              :max="field.max"
              :text-color="field.textColor"
              :fill="field.fill"
              @change="handleFieldChange(field, $event)"
            >
              <el-checkbox
                v-for="option in field.options"
                :key="option.value"
                :label="option.value"
                :disabled="option.disabled"
                :border="field.border"
                :size="field.size || size"
              >
                {{ option.label }}
              </el-checkbox>
            </el-checkbox-group>
            
            <!-- 滑块 -->
            <el-slider
              v-else-if="field.type === 'slider'"
              v-model="formData[field.name]"
              :min="field.min"
              :max="field.max"
              :disabled="field.disabled"
              :step="field.step"
              :show-input="field.showInput"
              :show-input-controls="field.showInputControls"
              :size="field.size || size"
              :input-size="field.inputSize"
              :show-stops="field.showStops"
              :show-tooltip="field.showTooltip"
              :format-tooltip="field.formatTooltip"
              :range="field.range"
              :vertical="field.vertical"
              :height="field.height"
              :label="field.label"
              :debounce="field.debounce"
              :tooltip-class="field.tooltipClass"
              :marks="field.marks"
              @change="handleFieldChange(field, $event)"
              @input="handleFieldInput(field, $event)"
            />
            
            <!-- 评分 -->
            <el-rate
              v-else-if="field.type === 'rate'"
              v-model="formData[field.name]"
              :max="field.max"
              :disabled="field.disabled"
              :allow-half="field.allowHalf"
              :low-threshold="field.lowThreshold"
              :high-threshold="field.highThreshold"
              :colors="field.colors"
              :void-color="field.voidColor"
              :disabled-void-color="field.disabledVoidColor"
              :icon-classes="field.iconClasses"
              :void-icon-class="field.voidIconClass"
              :disabled-void-icon-class="field.disabledVoidIconClass"
              :show-text="field.showText"
              :show-score="field.showScore"
              :text-color="field.textColor"
              :texts="field.texts"
              :score-template="field.scoreTemplate"
              :size="field.size || size"
              @change="handleFieldChange(field, $event)"
            />
            
            <!-- 颜色选择器 -->
            <el-color-picker
              v-else-if="field.type === 'color'"
              v-model="formData[field.name]"
              :disabled="field.disabled"
              :size="field.size || size"
              :show-alpha="field.showAlpha"
              :color-format="field.colorFormat"
              :popper-class="field.popperClass"
              :predefine="field.predefine"
              @change="handleFieldChange(field, $event)"
              @active-change="handleColorActiveChange(field, $event)"
            />
            
            <!-- 文件上传 -->
            <el-upload
              v-else-if="field.type === 'upload'"
              :action="field.action"
              :headers="field.headers"
              :method="field.method"
              :multiple="field.multiple"
              :data="field.data"
              :name="field.name"
              :with-credentials="field.withCredentials"
              :show-file-list="field.showFileList"
              :drag="field.drag"
              :accept="field.accept"
              :on-preview="field.onPreview"
              :on-remove="field.onRemove"
              :on-success="(response: any, file: any, fileList: any) => handleUploadSuccess(field, response, file, fileList)"
              :on-error="(error: any, file: any, fileList: any) => handleUploadError(field, error, file, fileList)"
              :on-progress="field.onProgress"
              :on-change="(file: any, fileList: any) => handleUploadChange(field, file, fileList)"
              :before-upload="field.beforeUpload"
              :before-remove="field.beforeRemove"
              :list-type="field.listType"
              :auto-upload="field.autoUpload"
              :file-list="field.fileList"
              :http-request="field.httpRequest"
              :disabled="field.disabled"
              :limit="field.limit"
              :on-exceed="field.onExceed"
            >
              <template v-if="field.drag">
                <el-icon class="el-icon--upload">
                  <upload-filled />
                </el-icon>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
              </template>
              <template v-else>
                <el-button type="primary">点击上传</el-button>
              </template>
              
              <template v-if="field.tip" #tip>
                <div class="el-upload__tip">{{ field.tip }}</div>
              </template>
            </el-upload>
          </template>
          
          <!-- 帮助文本 -->
          <template v-if="field.help" #help>
            <div class="field-help">{{ field.help }}</div>
          </template>
        </el-form-item>
      </template>
      
      <!-- 自定义插槽 -->
      <slot />
      
      <!-- 表单操作按钮 -->
      <el-form-item v-if="showActions" class="form-actions">
        <slot name="actions">
          <el-button
            v-if="showSubmit"
            type="primary"
            :loading="loading"
            :disabled="disabled"
            @click="handleSubmit"
          >
            {{ submitText }}
          </el-button>
          
          <el-button
            v-if="showReset"
            :disabled="disabled || loading"
            @click="handleReset"
          >
            {{ resetText }}
          </el-button>
          
          <el-button
            v-if="showCancel"
            :disabled="loading"
            @click="handleCancel"
          >
            {{ cancelText }}
          </el-button>
        </slot>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick, onMounted } from 'vue'
import { 
  ElForm, ElFormItem, ElInput, ElInputNumber, ElSelect, ElOption, 
  ElDatePicker, ElTimePicker, ElSwitch, ElRadioGroup, ElRadio, 
  ElCheckboxGroup, ElCheckbox, ElSlider, ElRate, ElColorPicker, 
  ElUpload, ElButton, ElIcon 
} from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import { useBaseComponent, useFormComponent } from '@/composables/useBaseComponent'
import type { FormComponentProps, ValidationRule, ComponentSize } from '@/types/component'

// 表单字段接口
interface FormField {
  name: string
  label?: string
  type?: string
  placeholder?: string
  required?: boolean
  rules?: ValidationRule[]
  options?: Array<{ label: string; value: any; disabled?: boolean }>
  disabled?: boolean
  readonly?: boolean
  size?: ComponentSize
  className?: string
  style?: any
  labelWidth?: string
  error?: string
  help?: string
  showMessage?: boolean
  inlineMessage?: boolean
  
  // 输入框特有属性
  inputType?: string
  clearable?: boolean
  showPassword?: boolean
  showWordLimit?: boolean
  maxlength?: number
  minlength?: number
  prefixIcon?: string
  suffixIcon?: string
  rows?: number
  autosize?: boolean | { minRows?: number; maxRows?: number }
  autocomplete?: string
  resize?: 'none' | 'both' | 'horizontal' | 'vertical'
  prepend?: string
  append?: string
  
  // 数字输入框特有属性
  min?: number
  max?: number
  step?: number
  stepStrictly?: boolean
  precision?: number
  controls?: boolean
  controlsPosition?: 'right' | ''
  
  // 选择器特有属性
  multiple?: boolean
  valueKey?: string
  collapseTags?: boolean
  collapseTagsTooltip?: boolean
  multipleLimit?: number
  filterable?: boolean
  allowCreate?: boolean
  filterMethod?: (query: string) => void
  remote?: boolean
  remoteMethod?: (query: string) => void
  loading?: boolean
  loadingText?: string
  noMatchText?: string
  noDataText?: string
  popperClass?: string
  reserveKeyword?: boolean
  defaultFirstOption?: boolean
  teleported?: boolean
  persistent?: boolean
  automaticDropdown?: boolean
  fitInputWidth?: boolean
  tagType?: 'success' | 'info' | 'warning' | 'danger'
  
  // 日期时间选择器特有属性
  editable?: boolean
  format?: string
  valueFormat?: string
  startPlaceholder?: string
  endPlaceholder?: string
  rangeSeparator?: string
  defaultValue?: Date | [Date, Date]
  defaultTime?: Date | [Date, Date]
  disabledDate?: (time: Date) => boolean
  shortcuts?: Array<{ text: string; value: Date | (() => Date) }>
  cellClassName?: (date: Date) => string
  timeArrowControl?: boolean
  isRange?: boolean
  arrowControl?: boolean
  disabledHours?: () => number[]
  disabledMinutes?: (hour: number) => number[]
  disabledSeconds?: (hour: number, minute: number) => number[]
  
  // 开关特有属性
  width?: number
  inlinePrompt?: boolean
  activeIcon?: string
  inactiveIcon?: string
  activeText?: string
  inactiveText?: string
  activeValue?: boolean | string | number
  inactiveValue?: boolean | string | number
  activeColor?: string
  inactiveColor?: string
  borderColor?: string
  validateEvent?: boolean
  
  // 单选框/多选框特有属性
  textColor?: string
  fill?: string
  border?: boolean
  
  // 滑块特有属性
  showInput?: boolean
  showInputControls?: boolean
  inputSize?: ComponentSize
  showStops?: boolean
  showTooltip?: boolean
  formatTooltip?: (value: number) => string
  range?: boolean
  vertical?: boolean
  height?: string
  debounce?: number
  tooltipClass?: string
  marks?: Record<number, string>
  
  // 评分特有属性
  allowHalf?: boolean
  lowThreshold?: number
  highThreshold?: number
  colors?: string[] | Record<number, string>
  voidColor?: string
  disabledVoidColor?: string
  iconClasses?: string[] | Record<number, string>
  voidIconClass?: string
  disabledVoidIconClass?: string
  showText?: boolean
  showScore?: boolean
  textColor?: string
  texts?: string[]
  scoreTemplate?: string
  
  // 颜色选择器特有属性
  showAlpha?: boolean
  colorFormat?: 'hsl' | 'hsv' | 'hex' | 'rgb'
  predefine?: string[]
  
  // 文件上传特有属性
  action?: string
  headers?: Record<string, any>
  method?: string
  data?: Record<string, any>
  withCredentials?: boolean
  showFileList?: boolean
  drag?: boolean
  accept?: string
  onPreview?: (file: any) => void
  onRemove?: (file: any, fileList: any[]) => void
  onProgress?: (event: any, file: any, fileList: any[]) => void
  beforeUpload?: (file: any) => boolean | Promise<boolean>
  beforeRemove?: (file: any, fileList: any[]) => boolean | Promise<boolean>
  listType?: 'text' | 'picture' | 'picture-card'
  autoUpload?: boolean
  fileList?: any[]
  httpRequest?: (options: any) => void
  limit?: number
  onExceed?: (files: any[], fileList: any[]) => void
  tip?: string
  
  // 自定义渲染
  render?: (field: FormField, value: any, updateValue: (value: any) => void) => any
}

// Props定义
interface BaseFormProps extends FormComponentProps {
  // 表单数据
  modelValue: Record<string, any>
  fields: FormField[]
  
  // 表单属性
  labelPosition?: 'left' | 'right' | 'top'
  labelWidth?: string
  labelSuffix?: string
  hideRequiredAsterisk?: boolean
  showMessage?: boolean
  inlineMessage?: boolean
  statusIcon?: boolean
  validateOnRuleChange?: boolean
  
  // 操作按钮
  showActions?: boolean
  showSubmit?: boolean
  showReset?: boolean
  showCancel?: boolean
  submitText?: string
  resetText?: string
  cancelText?: string
}

const props = withDefaults(defineProps<BaseFormProps>(), {
  labelPosition: 'right',
  labelWidth: '100px',
  labelSuffix: '：',
  hideRequiredAsterisk: false,
  showMessage: true,
  inlineMessage: false,
  statusIcon: false,
  validateOnRuleChange: true,
  showActions: true,
  showSubmit: true,
  showReset: true,
  showCancel: false,
  submitText: '提交',
  resetText: '重置',
  cancelText: '取消'
})

// Emits定义
const emit = defineEmits<{
  'update:modelValue': [value: Record<string, any>]
  submit: [data: Record<string, any>]
  reset: []
  cancel: []
  validate: [prop: string, isValid: boolean, message: string]
  'field-change': [field: FormField, value: any, oldValue: any]
  'field-input': [field: FormField, value: any]
  'field-focus': [field: FormField, event: FocusEvent]
  'field-blur': [field: FormField, event: FocusEvent]
}>()

// 使用基础组件功能
const { componentRef, componentClass, componentStyle, testAttributes, setLoading } = useBaseComponent(props)

// 表单引用
const formRef = ref<InstanceType<typeof ElForm>>()

// 表单数据
const formData = reactive<Record<string, any>>({ ...props.modelValue })

// 表单规则
const formRules = computed(() => {
  const rules: Record<string, ValidationRule[]> = {}
  
  props.fields.forEach(field => {
    if (field.rules && field.rules.length > 0) {
      rules[field.name] = field.rules
    } else if (field.required) {
      rules[field.name] = [
        { required: true, message: `请输入${field.label}`, trigger: 'blur' }
      ]
    }
  })
  
  return rules
})

// 方法
const updateFieldValue = (fieldName: string, value: any) => {
  const oldValue = formData[fieldName]
  formData[fieldName] = value
  
  // 更新父组件数据
  emit('update:modelValue', { ...formData })
  
  // 触发字段变化事件
  const field = props.fields.find(f => f.name === fieldName)
  if (field) {
    emit('field-change', field, value, oldValue)
  }
}

// 事件处理
const handleValidate = (prop: string, isValid: boolean, message: string) => {
  emit('validate', prop, isValid, message)
}

const handleFieldInput = (field: FormField, value: any) => {
  updateFieldValue(field.name, value)
  emit('field-input', field, value)
}

const handleFieldChange = (field: FormField, value: any) => {
  updateFieldValue(field.name, value)
}

const handleFieldFocus = (field: FormField, event: FocusEvent) => {
  emit('field-focus', field, event)
}

const handleFieldBlur = (field: FormField, event: FocusEvent) => {
  emit('field-blur', field, event)
}

const handleSelectVisibleChange = (field: FormField, visible: boolean) => {
  // 处理选择器显示/隐藏
}

const handleSelectRemoveTag = (field: FormField, value: any) => {
  // 处理选择器标签移除
}

const handleSelectClear = (field: FormField) => {
  updateFieldValue(field.name, field.multiple ? [] : undefined)
}

const handleColorActiveChange = (field: FormField, value: string) => {
  // 处理颜色选择器活动变化
}

const handleUploadSuccess = (field: FormField, response: any, file: any, fileList: any[]) => {
  updateFieldValue(field.name, fileList)
}

const handleUploadError = (field: FormField, error: any, file: any, fileList: any[]) => {
  console.error('Upload error:', error)
}

const handleUploadChange = (field: FormField, file: any, fileList: any[]) => {
  updateFieldValue(field.name, fileList)
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    setLoading(true)
    const isValid = await formRef.value.validate()
    
    if (isValid) {
      emit('submit', { ...formData })
    }
  } catch (error) {
    console.error('Form validation failed:', error)
  } finally {
    setLoading(false)
  }
}

const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
    emit('reset')
  }
}

const handleCancel = () => {
  emit('cancel')
}

// 监听外部数据变化
watch(() => props.modelValue, (newValue) => {
  Object.assign(formData, newValue)
}, { deep: true })

// 暴露方法
defineExpose({
  formRef,
  validate: () => formRef.value?.validate(),
  validateField: (props: string | string[]) => formRef.value?.validateField(props),
  resetFields: () => formRef.value?.resetFields(),
  scrollToField: (prop: string) => formRef.value?.scrollToField(prop),
  clearValidate: (props?: string | string[]) => formRef.value?.clearValidate(props)
})
</script>

<style lang="scss" scoped>
.base-form {
  .form-actions {
    margin-top: 20px;
    text-align: center;
    
    .el-button + .el-button {
      margin-left: 12px;
    }
  }
  
  .field-help {
    font-size: 12px;
    color: #909399;
    margin-top: 4px;
  }
  
  .el-upload__tip {
    font-size: 12px;
    color: #606266;
    margin-top: 7px;
  }
}
</style>
