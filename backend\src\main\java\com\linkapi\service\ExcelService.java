package com.linkapi.service;

import com.linkapi.common.BusinessException;
import com.linkapi.common.ResultCode;
import com.linkapi.entity.Document;
import com.linkapi.entity.DocumentDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Excel文件解析服务
 * 
 * <AUTHOR> API Team
 */
@Slf4j
@Service
public class ExcelService {

    private static final DateTimeFormatter[] DATE_FORMATTERS = {
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd")
    };

    /**
     * 解析Excel文件为单据列表
     * 
     * @param file Excel文件
     * @return 单据列表
     */
    public List<Document> parseExcelToDocuments(MultipartFile file) {
        validateFile(file);
        
        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {
            
            Sheet sheet = workbook.getSheetAt(0);
            return parseSheet(sheet, file.getOriginalFilename());
            
        } catch (IOException e) {
            log.error("Excel文件读取失败: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.EXCEL_READ_ERROR, "Excel文件读取失败: " + e.getMessage());
        }
    }

    /**
     * 验证文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new BusinessException(ResultCode.FILE_NOT_FOUND, "文件不能为空");
        }
        
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !originalFilename.toLowerCase().endsWith(".xlsx")) {
            throw new BusinessException(ResultCode.FILE_FORMAT_ERROR, "只支持.xlsx格式的Excel文件");
        }
        
        // 检查文件大小（50MB限制）
        if (file.getSize() > 50 * 1024 * 1024) {
            throw new BusinessException(ResultCode.FILE_SIZE_EXCEEDED, "文件大小不能超过50MB");
        }
    }

    /**
     * 解析工作表
     */
    private List<Document> parseSheet(Sheet sheet, String sourceFile) {
        if (sheet.getPhysicalNumberOfRows() <= 1) {
            throw new BusinessException(ResultCode.EXCEL_EMPTY_ERROR, "Excel文件没有数据行");
        }
        
        // 验证表头
        Row headerRow = sheet.getRow(0);
        validateHeader(headerRow);
        
        List<Document> documents = new ArrayList<>();
        Iterator<Row> rowIterator = sheet.iterator();
        
        // 跳过表头
        if (rowIterator.hasNext()) {
            rowIterator.next();
        }
        
        int rowIndex = 1;
        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            try {
                Document document = parseRowToDocument(row, rowIndex, sourceFile);
                if (document != null) {
                    documents.add(document);
                }
            } catch (Exception e) {
                log.warn("第{}行数据解析失败: {}", rowIndex + 1, e.getMessage());
                // 继续处理下一行，不中断整个解析过程
            }
            rowIndex++;
        }
        
        if (documents.isEmpty()) {
            throw new BusinessException(ResultCode.EXCEL_DATA_ERROR, "没有解析到有效的数据行");
        }
        
        return documents;
    }

    /**
     * 验证表头
     */
    private void validateHeader(Row headerRow) {
        if (headerRow == null) {
            throw new BusinessException(ResultCode.EXCEL_HEADER_ERROR, "Excel文件缺少表头");
        }
        
        String[] expectedHeaders = {
            "订单编号", "单据类型", "发货日期", "店铺名称", "仓库", 
            "商品编码", "商品名称", "数量", "单价", "金额", "备注"
        };
        
        for (int i = 0; i < expectedHeaders.length; i++) {
            Cell cell = headerRow.getCell(i);
            String cellValue = getCellStringValue(cell);
            if (!expectedHeaders[i].equals(cellValue)) {
                throw new BusinessException(ResultCode.EXCEL_HEADER_ERROR, 
                    String.format("第%d列表头应为'%s'，实际为'%s'", i + 1, expectedHeaders[i], cellValue));
            }
        }
    }

    /**
     * 解析行数据为单据
     */
    private Document parseRowToDocument(Row row, int rowIndex, String sourceFile) {
        if (isEmptyRow(row)) {
            return null;
        }
        
        try {
            // 解析单据头信息
            String documentNo = getCellStringValue(row.getCell(0));
            String documentTypeStr = getCellStringValue(row.getCell(1));
            String documentDateStr = getCellStringValue(row.getCell(2));
            String customerName = getCellStringValue(row.getCell(3));
            String warehouseName = getCellStringValue(row.getCell(4));
            String remark = getCellStringValue(row.getCell(10));
            
            // 验证必填字段
            validateRequiredFields(documentNo, documentTypeStr, documentDateStr, 
                customerName, warehouseName, rowIndex);
            
            // 创建单据对象
            Document document = new Document();
            document.setDocumentNo(documentNo);
            document.setDocumentType(parseDocumentType(documentTypeStr));
            document.setDocumentDate(parseDateTime(documentDateStr));
            document.setCustomerName(customerName);
            document.setWarehouseName(warehouseName);
            document.setRemark(remark);
            document.setSourceFile(sourceFile);
            document.setStatus(Document.DocumentStatus.PENDING);
            document.setRetryCount(0);
            
            // 解析单据明细
            DocumentDetail detail = parseRowToDocumentDetail(row, rowIndex);
            List<DocumentDetail> details = new ArrayList<>();
            details.add(detail);
            document.setDetails(details);
            
            // 计算总金额和总数量
            document.calculateTotals();
            
            return document;
            
        } catch (Exception e) {
            throw new BusinessException(ResultCode.EXCEL_DATA_ERROR, 
                String.format("第%d行数据解析失败: %s", rowIndex + 1, e.getMessage()));
        }
    }

    /**
     * 解析行数据为单据明细
     */
    private DocumentDetail parseRowToDocumentDetail(Row row, int rowIndex) {
        String productCode = getCellStringValue(row.getCell(5));
        String productName = getCellStringValue(row.getCell(6));
        String quantityStr = getCellStringValue(row.getCell(7));
        String unitPriceStr = getCellStringValue(row.getCell(8));
        String amountStr = getCellStringValue(row.getCell(9));
        
        // 验证必填字段
        if (productCode == null || productCode.trim().isEmpty()) {
            throw new IllegalArgumentException("商品编码不能为空");
        }
        if (quantityStr == null || quantityStr.trim().isEmpty()) {
            throw new IllegalArgumentException("数量不能为空");
        }
        if (unitPriceStr == null || unitPriceStr.trim().isEmpty()) {
            throw new IllegalArgumentException("单价不能为空");
        }
        
        DocumentDetail detail = new DocumentDetail();
        detail.setLineNo(1);
        detail.setProductCode(productCode.trim());
        detail.setProductName(productName != null ? productName.trim() : null);
        
        try {
            BigDecimal quantity = new BigDecimal(quantityStr.trim());
            BigDecimal unitPrice = new BigDecimal(unitPriceStr.trim());
            
            detail.setQuantity(quantity);
            detail.setUnitPrice(unitPrice);
            
            // 如果金额为空，则自动计算
            if (amountStr == null || amountStr.trim().isEmpty()) {
                detail.calculateAmount();
            } else {
                BigDecimal amount = new BigDecimal(amountStr.trim());
                detail.setAmount(amount);
                
                // 验证金额计算是否正确
                if (!detail.isAmountValid()) {
                    log.warn("第{}行金额计算不正确，将重新计算", rowIndex + 1);
                    detail.calculateAmount();
                }
            }
            
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("数量、单价或金额格式不正确");
        }
        
        return detail;
    }

    /**
     * 验证必填字段
     */
    private void validateRequiredFields(String documentNo, String documentType, 
            String documentDate, String customerName, String warehouseName, int rowIndex) {
        if (documentNo == null || documentNo.trim().isEmpty()) {
            throw new IllegalArgumentException("订单编号不能为空");
        }
        if (documentType == null || documentType.trim().isEmpty()) {
            throw new IllegalArgumentException("单据类型不能为空");
        }
        if (documentDate == null || documentDate.trim().isEmpty()) {
            throw new IllegalArgumentException("发货日期不能为空");
        }
        if (customerName == null || customerName.trim().isEmpty()) {
            throw new IllegalArgumentException("店铺名称不能为空");
        }
        if (warehouseName == null || warehouseName.trim().isEmpty()) {
            throw new IllegalArgumentException("仓库不能为空");
        }
    }

    /**
     * 解析单据类型
     */
    private Document.DocumentType parseDocumentType(String documentTypeStr) {
        String type = documentTypeStr.trim();
        switch (type) {
            case "销售出库":
            case "销售":
            case "出库":
                return Document.DocumentType.SALE_OUT;
            case "退货":
            case "销售退货":
                return Document.DocumentType.SALE_RETURN;
            case "采购入库":
            case "采购":
            case "入库":
                return Document.DocumentType.PURCHASE_IN;
            case "调拨":
            case "库存调拨":
                return Document.DocumentType.STOCK_TRANSFER;
            default:
                throw new IllegalArgumentException("不支持的单据类型: " + type);
        }
    }

    /**
     * 解析日期时间
     */
    private LocalDateTime parseDateTime(String dateTimeStr) {
        String dateTime = dateTimeStr.trim();
        
        for (DateTimeFormatter formatter : DATE_FORMATTERS) {
            try {
                if (formatter.toString().contains("HH:mm:ss")) {
                    return LocalDateTime.parse(dateTime, formatter);
                } else {
                    return LocalDateTime.parse(dateTime + " 00:00:00", 
                        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                }
            } catch (DateTimeParseException ignored) {
                // 尝试下一个格式
            }
        }
        
        throw new IllegalArgumentException("日期格式不正确: " + dateTime);
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getLocalDateTimeCellValue().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                } else {
                    // 处理数字，避免科学计数法
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == (long) numericValue) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return null;
        }
    }

    /**
     * 检查是否为空行
     */
    private boolean isEmptyRow(Row row) {
        if (row == null) {
            return true;
        }
        
        for (int i = 0; i < 6; i++) { // 检查前6列是否都为空
            Cell cell = row.getCell(i);
            String cellValue = getCellStringValue(cell);
            if (cellValue != null && !cellValue.trim().isEmpty()) {
                return false;
            }
        }
        
        return true;
    }
}