package com.linkapi.controller;

import com.linkapi.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 文件控制器
 * 
 * <AUTHOR> API Team
 */
@Slf4j
@RestController
@RequestMapping("/file")
public class FileController {

    @Value("${file.upload.path:./uploads}")
    private String uploadPath;

    @Value("${file.upload.max-size:10485760}") // 10MB
    private long maxFileSize;

    /**
     * 上传文件
     */
    @PostMapping("/upload")
    public Result<Map<String, Object>> uploadFile(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.error("请选择要上传的文件");
        }

        // 检查文件大小
        if (file.getSize() > maxFileSize) {
            return Result.error("文件大小超过限制（最大10MB）");
        }

        try {
            // 创建上传目录
            Path uploadDir = Paths.get(uploadPath);
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
            }

            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String extension = "";
            if (originalFilename != null && originalFilename.contains(".")) {
                extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
            String filename = timestamp + "_" + uuid + extension;

            // 保存文件
            Path filePath = uploadDir.resolve(filename);
            Files.copy(file.getInputStream(), filePath);

            // 返回文件信息
            Map<String, Object> data = new HashMap<>();
            data.put("filename", filename);
            data.put("originalFilename", originalFilename);
            data.put("size", file.getSize());
            data.put("contentType", file.getContentType());
            data.put("uploadTime", LocalDateTime.now());
            data.put("filePath", filePath.toString());

            log.info("文件上传成功: {}", filename);
            return Result.success("文件上传成功", data);

        } catch (IOException e) {
            log.error("文件上传失败", e);
            return Result.error("文件上传失败：" + e.getMessage());
        }
    }

    /**
     * 批量上传文件
     */
    @PostMapping("/upload/batch")
    public Result<Map<String, Object>> uploadFiles(@RequestParam("files") MultipartFile[] files) {
        if (files.length == 0) {
            return Result.error("请选择要上传的文件");
        }

        Map<String, Object> result = new HashMap<>();
        result.put("successCount", 0);
        result.put("failCount", 0);
        result.put("successFiles", new HashMap<String, Object>());
        result.put("failFiles", new HashMap<String, String>());

        for (MultipartFile file : files) {
            if (!file.isEmpty()) {
                Result<Map<String, Object>> uploadResult = uploadFile(file);
                if (uploadResult.getCode() == 200) {
                    ((Map<String, Object>) result.get("successFiles")).put(file.getOriginalFilename(), uploadResult.getData());
                    result.put("successCount", (Integer) result.get("successCount") + 1);
                } else {
                    ((Map<String, String>) result.get("failFiles")).put(file.getOriginalFilename(), uploadResult.getMessage());
                    result.put("failCount", (Integer) result.get("failCount") + 1);
                }
            }
        }

        return Result.success("批量上传完成", result);
    }

    /**
     * 删除文件
     */
    @DeleteMapping("/{filename}")
    public Result<String> deleteFile(@PathVariable String filename) {
        try {
            Path filePath = Paths.get(uploadPath, filename);
            if (Files.exists(filePath)) {
                Files.delete(filePath);
                log.info("文件删除成功: {}", filename);
                return Result.success("文件删除成功", "success");
            } else {
                return Result.error("文件不存在");
            }
        } catch (IOException e) {
            log.error("文件删除失败", e);
            return Result.error("文件删除失败：" + e.getMessage());
        }
    }

    /**
     * 获取文件信息
     */
    @GetMapping("/{filename}/info")
    public Result<Map<String, Object>> getFileInfo(@PathVariable String filename) {
        try {
            Path filePath = Paths.get(uploadPath, filename);
            if (Files.exists(filePath)) {
                Map<String, Object> data = new HashMap<>();
                data.put("filename", filename);
                data.put("size", Files.size(filePath));
                data.put("lastModified", Files.getLastModifiedTime(filePath).toString());
                data.put("contentType", Files.probeContentType(filePath));
                data.put("exists", true);

                return Result.success("获取文件信息成功", data);
            } else {
                return Result.error("文件不存在");
            }
        } catch (IOException e) {
            log.error("获取文件信息失败", e);
            return Result.error("获取文件信息失败：" + e.getMessage());
        }
    }

    /**
     * 列出上传目录中的所有文件
     */
    @GetMapping("/list")
    public Result<Map<String, Object>> listFiles() {
        try {
            Path uploadDir = Paths.get(uploadPath);
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
            }

            Map<String, Object> data = new HashMap<>();
            data.put("files", Files.list(uploadDir)
                    .filter(Files::isRegularFile)
                    .map(path -> {
                        try {
                            Map<String, Object> fileInfo = new HashMap<>();
                            fileInfo.put("filename", path.getFileName().toString());
                            fileInfo.put("size", Files.size(path));
                            fileInfo.put("lastModified", Files.getLastModifiedTime(path).toString());
                            return fileInfo;
                        } catch (IOException e) {
                            log.error("获取文件信息失败: {}", path.getFileName(), e);
                            return null;
                        }
                    })
                    .filter(fileInfo -> fileInfo != null)
                    .toArray());

            return Result.success("获取文件列表成功", data);
        } catch (IOException e) {
            log.error("获取文件列表失败", e);
            return Result.error("获取文件列表失败：" + e.getMessage());
        }
    }

    /**
     * 清空上传目录
     */
    @DeleteMapping("/clear")
    public Result<String> clearFiles() {
        try {
            Path uploadDir = Paths.get(uploadPath);
            if (Files.exists(uploadDir)) {
                Files.list(uploadDir)
                        .filter(Files::isRegularFile)
                        .forEach(path -> {
                            try {
                                Files.delete(path);
                            } catch (IOException e) {
                                log.error("删除文件失败: {}", path.getFileName(), e);
                            }
                        });
            }

            log.info("上传目录清空成功");
            return Result.success("清空成功", "success");
        } catch (IOException e) {
            log.error("清空上传目录失败", e);
            return Result.error("清空失败：" + e.getMessage());
        }
    }
}
