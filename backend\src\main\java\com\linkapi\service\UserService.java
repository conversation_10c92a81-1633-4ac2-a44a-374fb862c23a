package com.linkapi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.linkapi.entity.User;
import com.linkapi.entity.Permission;
import com.linkapi.entity.Role;

import java.util.List;

/**
 * 用户服务接口
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface UserService extends IService<User> {

    /**
     * 用户登录
     *
     * @param username 用户名
     * @param password 密码
     * @param loginIp 登录IP
     * @return 用户信息（包含角色和权限）
     */
    User login(String username, String password, String loginIp);

    /**
     * 根据用户名查询用户（包含角色和权限）
     *
     * @param username 用户名
     * @return 用户信息
     */
    User findByUsernameWithRoles(String username);

    /**
     * 根据用户ID查询用户角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<Role> findRolesByUserId(Long userId);

    /**
     * 根据用户ID查询用户权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    List<Permission> findPermissionsByUserId(Long userId);

    /**
     * 创建用户
     *
     * @param user 用户信息
     * @param roleIds 角色ID列表
     * @return 创建结果
     */
    boolean createUser(User user, List<Long> roleIds);

    /**
     * 更新用户
     *
     * @param user 用户信息
     * @param roleIds 角色ID列表
     * @return 更新结果
     */
    boolean updateUser(User user, List<Long> roleIds);

    /**
     * 删除用户
     *
     * @param userId 用户ID
     * @return 删除结果
     */
    boolean deleteUser(Long userId);

    /**
     * 重置用户密码
     *
     * @param userId 用户ID
     * @param newPassword 新密码
     * @return 重置结果
     */
    boolean resetPassword(Long userId, String newPassword);

    /**
     * 修改用户密码
     *
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 修改结果
     */
    boolean changePassword(Long userId, String oldPassword, String newPassword);

    /**
     * 锁定/解锁用户
     *
     * @param userId 用户ID
     * @param locked 是否锁定
     * @return 操作结果
     */
    boolean lockUser(Long userId, boolean locked);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean isUsernameExists(String username, Long excludeId);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    boolean isEmailExists(String email, Long excludeId);

    /**
     * 验证密码
     *
     * @param rawPassword 原始密码
     * @param encodedPassword 加密密码
     * @return 验证结果
     */
    boolean verifyPassword(String rawPassword, String encodedPassword);

    /**
     * 加密密码
     *
     * @param rawPassword 原始密码
     * @return 加密密码
     */
    String encodePassword(String rawPassword);
}