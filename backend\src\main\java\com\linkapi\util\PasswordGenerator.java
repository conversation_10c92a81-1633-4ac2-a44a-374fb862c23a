package com.linkapi.util;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

public class PasswordGenerator {
    public static void main(String[] args) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        String password = "admin123";
        String encoded = encoder.encode(password);
        System.out.println("Original: " + password);
        System.out.println("Encoded: " + encoded);
        
        // Test verification
        boolean matches = encoder.matches(password, encoded);
        System.out.println("Verification: " + matches);
    }
}