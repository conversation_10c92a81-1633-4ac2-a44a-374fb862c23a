/*! Element Plus Icons Vue v2.3.2 */

var ct=Object.defineProperty;var pt=(t,e)=>{for(var o in e)ct(t,o,{get:e[o],enumerable:!0})};var z={};pt(z,{AddLocation:()=>H,Aim:()=>A,AlarmClock:()=>q,Apple:()=>F,ArrowDown:()=>b,ArrowDownBold:()=>D,ArrowLeft:()=>P,ArrowLeftBold:()=>y,ArrowRight:()=>T,ArrowRightBold:()=>R,ArrowUp:()=>I,ArrowUpBold:()=>O,Avatar:()=>G,Back:()=>W,Baseball:()=>Q,Basketball:()=>Z,Bell:()=>J,BellFilled:()=>j,Bicycle:()=>X,Bottom:()=>t2,BottomLeft:()=>$,BottomRight:()=>o2,Bowl:()=>a2,Box:()=>r2,Briefcase:()=>n2,Brush:()=>m2,BrushFilled:()=>l2,Burger:()=>c2,Calendar:()=>p2,Camera:()=>_2,CameraFilled:()=>s2,CaretBottom:()=>f2,CaretLeft:()=>i2,CaretRight:()=>d2,CaretTop:()=>u2,Cellphone:()=>h2,ChatDotRound:()=>w2,ChatDotSquare:()=>x2,ChatLineRound:()=>C2,ChatLineSquare:()=>V2,ChatRound:()=>g2,ChatSquare:()=>M2,Check:()=>N2,Checked:()=>z2,Cherry:()=>H2,Chicken:()=>L2,ChromeFilled:()=>A2,CircleCheck:()=>F2,CircleCheckFilled:()=>S2,CircleClose:()=>y2,CircleCloseFilled:()=>D2,CirclePlus:()=>R2,CirclePlusFilled:()=>P2,Clock:()=>T2,Close:()=>I2,CloseBold:()=>O2,Cloudy:()=>G2,Coffee:()=>W2,CoffeeCup:()=>U2,Coin:()=>K2,ColdDrink:()=>Q2,Collection:()=>J2,CollectionTag:()=>Z2,Comment:()=>X2,Compass:()=>$2,Connection:()=>oe,Coordinate:()=>ae,CopyDocument:()=>ne,Cpu:()=>me,CreditCard:()=>pe,Crop:()=>_e,DArrowLeft:()=>fe,DArrowRight:()=>ie,DCaret:()=>de,DataAnalysis:()=>ue,DataBoard:()=>he,DataLine:()=>ve,Delete:()=>xe,DeleteFilled:()=>we,DeleteLocation:()=>Be,Dessert:()=>ke,Discount:()=>Ee,Dish:()=>ge,DishDot:()=>Ve,Document:()=>Ae,DocumentAdd:()=>Me,DocumentChecked:()=>Ne,DocumentCopy:()=>ze,DocumentDelete:()=>He,DocumentRemove:()=>Le,Download:()=>Se,Drizzling:()=>qe,Edit:()=>be,EditPen:()=>Fe,Eleme:()=>Pe,ElemeFilled:()=>ye,ElementPlus:()=>Re,Expand:()=>Te,Failed:()=>Oe,Female:()=>Ie,Files:()=>Ge,Film:()=>We,Filter:()=>Ke,Finished:()=>Qe,FirstAidKit:()=>je,Flag:()=>Je,Fold:()=>Xe,Folder:()=>a0,FolderAdd:()=>Ye,FolderChecked:()=>$e,FolderDelete:()=>e0,FolderOpened:()=>o0,FolderRemove:()=>t0,Food:()=>r0,Football:()=>l0,ForkSpoon:()=>m0,Fries:()=>c0,FullScreen:()=>p0,Goblet:()=>i0,GobletFull:()=>s0,GobletSquare:()=>f0,GobletSquareFull:()=>_0,GoldMedal:()=>u0,Goods:()=>v0,GoodsFilled:()=>h0,Grape:()=>w0,Grid:()=>B0,Guide:()=>k0,Handbag:()=>C0,Headset:()=>E0,Help:()=>g0,HelpFilled:()=>V0,Hide:()=>N0,Histogram:()=>z0,HomeFilled:()=>H0,HotWater:()=>L0,House:()=>A0,IceCream:()=>F0,IceCreamRound:()=>S0,IceCreamSquare:()=>q0,IceDrink:()=>D0,IceTea:()=>b0,InfoFilled:()=>y0,Iphone:()=>P0,Key:()=>R0,KnifeFork:()=>T0,Lightning:()=>I0,Link:()=>G0,List:()=>U0,Loading:()=>W0,Location:()=>j0,LocationFilled:()=>K0,LocationInformation:()=>Q0,Lock:()=>X0,Lollipop:()=>Y0,MagicStick:()=>$0,Magnet:()=>e1,Male:()=>o1,Management:()=>t1,MapLocation:()=>r1,Medal:()=>l1,Memo:()=>m1,Menu:()=>c1,Message:()=>_1,MessageBox:()=>p1,Mic:()=>f1,Microphone:()=>i1,MilkTea:()=>d1,Minus:()=>u1,Money:()=>h1,Monitor:()=>v1,Moon:()=>x1,MoonNight:()=>B1,More:()=>C1,MoreFilled:()=>k1,MostlyCloudy:()=>E1,Mouse:()=>g1,Mug:()=>M1,Mute:()=>L1,MuteNotification:()=>z1,NoSmoking:()=>A1,Notebook:()=>q1,Notification:()=>D1,Odometer:()=>b1,OfficeBuilding:()=>y1,Open:()=>R1,Operation:()=>T1,Opportunity:()=>O1,Orange:()=>I1,Paperclip:()=>G1,PartlyCloudy:()=>W1,Pear:()=>K1,Phone:()=>Z1,PhoneFilled:()=>Q1,Picture:()=>$1,PictureFilled:()=>j1,PictureRounded:()=>X1,PieChart:()=>o4,Place:()=>t4,Platform:()=>a4,Plus:()=>r4,Pointer:()=>n4,Position:()=>l4,Postcard:()=>c4,Pouring:()=>p4,Present:()=>s4,PriceTag:()=>f4,Printer:()=>i4,Promotion:()=>d4,QuartzWatch:()=>u4,QuestionFilled:()=>h4,Rank:()=>v4,Reading:()=>k4,ReadingLamp:()=>B4,Refresh:()=>V4,RefreshLeft:()=>C4,RefreshRight:()=>E4,Refrigerator:()=>g4,Remove:()=>z4,RemoveFilled:()=>M4,Right:()=>H4,ScaleToOriginal:()=>L4,School:()=>A4,Scissor:()=>S4,Search:()=>q4,Select:()=>F4,Sell:()=>D4,SemiSelect:()=>b4,Service:()=>y4,SetUp:()=>P4,Setting:()=>R4,Share:()=>T4,Ship:()=>O4,Shop:()=>I4,ShoppingBag:()=>U4,ShoppingCart:()=>Q4,ShoppingCartFull:()=>K4,ShoppingTrolley:()=>Z4,Smoking:()=>J4,Soccer:()=>X4,SoldOut:()=>Y4,Sort:()=>oo,SortDown:()=>$4,SortUp:()=>eo,Stamp:()=>to,Star:()=>ro,StarFilled:()=>ao,Stopwatch:()=>lo,SuccessFilled:()=>mo,Sugar:()=>co,Suitcase:()=>_o,SuitcaseLine:()=>po,Sunny:()=>fo,Sunrise:()=>io,Sunset:()=>uo,Switch:()=>xo,SwitchButton:()=>vo,SwitchFilled:()=>Bo,TakeawayBox:()=>ko,Ticket:()=>Co,Tickets:()=>Eo,Timer:()=>Vo,ToiletPaper:()=>Mo,Tools:()=>No,Top:()=>So,TopLeft:()=>Ho,TopRight:()=>Ao,TrendCharts:()=>qo,Trophy:()=>Do,TrophyBase:()=>Fo,TurnOff:()=>yo,Umbrella:()=>Po,Unlock:()=>To,Upload:()=>Io,UploadFilled:()=>Oo,User:()=>Uo,UserFilled:()=>Go,Van:()=>Wo,VideoCamera:()=>Qo,VideoCameraFilled:()=>Ko,VideoPause:()=>Zo,VideoPlay:()=>jo,View:()=>Jo,Wallet:()=>Yo,WalletFilled:()=>Xo,WarnTriangleFilled:()=>$o,Warning:()=>ot,WarningFilled:()=>et,Watch:()=>tt,Watermelon:()=>at,WindPower:()=>rt,ZoomIn:()=>nt,ZoomOut:()=>lt});import{defineComponent as st}from"vue";import{createElementVNode as l,openBlock as _t,createElementBlock as ft}from"vue";var it=st({name:"AddLocation",__name:"add-location",setup(t){return(e,o)=>(_t(),ft("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[l("path",{fill:"currentColor",d:"M288 896h448q32 0 32 32t-32 32H288q-32 0-32-32t32-32"}),l("path",{fill:"currentColor",d:"M800 416a288 288 0 1 0-576 0c0 118.144 94.528 272.128 288 456.576C705.472 688.128 800 534.144 800 416M512 960C277.312 746.688 160 565.312 160 416a352 352 0 0 1 704 0c0 149.312-117.312 330.688-352 544"}),l("path",{fill:"currentColor",d:"M544 384h96a32 32 0 1 1 0 64h-96v96a32 32 0 0 1-64 0v-96h-96a32 32 0 0 1 0-64h96v-96a32 32 0 0 1 64 0z"})]))}}),H=it;import{defineComponent as dt}from"vue";import{createElementVNode as L,openBlock as ut,createElementBlock as ht}from"vue";var vt=dt({name:"Aim",__name:"aim",setup(t){return(e,o)=>(ut(),ht("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[L("path",{fill:"currentColor",d:"M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768m0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896"}),L("path",{fill:"currentColor",d:"M512 96a32 32 0 0 1 32 32v192a32 32 0 0 1-64 0V128a32 32 0 0 1 32-32m0 576a32 32 0 0 1 32 32v192a32 32 0 1 1-64 0V704a32 32 0 0 1 32-32M96 512a32 32 0 0 1 32-32h192a32 32 0 0 1 0 64H128a32 32 0 0 1-32-32m576 0a32 32 0 0 1 32-32h192a32 32 0 1 1 0 64H704a32 32 0 0 1-32-32"})]))}}),A=vt;import{defineComponent as wt}from"vue";import{createElementVNode as S,openBlock as Bt,createElementBlock as xt}from"vue";var kt=wt({name:"AlarmClock",__name:"alarm-clock",setup(t){return(e,o)=>(Bt(),xt("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[S("path",{fill:"currentColor",d:"M512 832a320 320 0 1 0 0-640 320 320 0 0 0 0 640m0 64a384 384 0 1 1 0-768 384 384 0 0 1 0 768"}),S("path",{fill:"currentColor",d:"m292.288 824.576 55.424 32-48 83.136a32 32 0 1 1-55.424-32zm439.424 0-55.424 32 48 83.136a32 32 0 1 0 55.424-32zM512 512h160a32 32 0 1 1 0 64H480a32 32 0 0 1-32-32V320a32 32 0 0 1 64 0zM90.496 312.256A160 160 0 0 1 312.32 90.496l-46.848 46.848a96 96 0 0 0-128 128L90.56 312.256zm835.264 0A160 160 0 0 0 704 90.496l46.848 46.848a96 96 0 0 1 128 128z"})]))}}),q=kt;import{defineComponent as Ct}from"vue";import{createElementVNode as Et,openBlock as Vt,createElementBlock as gt}from"vue";var Mt=Ct({name:"Apple",__name:"apple",setup(t){return(e,o)=>(Vt(),gt("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Et("path",{fill:"currentColor",d:"M599.872 203.776a189.4 189.4 0 0 1 64.384-4.672l2.624.128c31.168 1.024 51.2 4.096 79.488 16.32 37.632 16.128 74.496 45.056 111.488 89.344 96.384 115.264 82.752 372.8-34.752 521.728-7.68 9.728-32 41.6-30.72 39.936a427 427 0 0 1-30.08 35.776c-31.232 32.576-65.28 49.216-110.08 50.048-31.36.64-53.568-5.312-84.288-18.752l-6.528-2.88c-20.992-9.216-30.592-11.904-47.296-11.904-18.112 0-28.608 2.88-51.136 12.672l-6.464 2.816c-28.416 12.224-48.32 18.048-76.16 19.2-74.112 2.752-116.928-38.08-180.672-132.16-96.64-142.08-132.608-349.312-55.04-486.4 46.272-81.92 129.92-133.632 220.672-135.04 32.832-.576 60.288 6.848 99.648 22.72 27.136 10.88 34.752 13.76 37.376 14.272 16.256-20.16 27.776-36.992 34.56-50.24 13.568-26.304 27.2-59.968 40.704-100.8a32 32 0 1 1 60.8 20.224c-12.608 37.888-25.408 70.4-38.528 97.664m-51.52 78.08c-14.528 17.792-31.808 37.376-51.904 58.816a32 32 0 1 1-46.72-43.776l12.288-13.248c-28.032-11.2-61.248-26.688-95.68-26.112-70.4 1.088-135.296 41.6-171.648 105.792C121.6 492.608 176 684.16 247.296 788.992c34.816 51.328 76.352 108.992 130.944 106.944 52.48-2.112 72.32-34.688 135.872-34.688s81.28 34.688 136.96 33.536c56.448-1.088 75.776-39.04 126.848-103.872 107.904-136.768 107.904-362.752 35.776-449.088-72.192-86.272-124.672-84.096-151.68-85.12-41.472-4.288-81.6 12.544-113.664 25.152"})]))}}),F=Mt;import{defineComponent as Nt}from"vue";import{createElementVNode as zt,openBlock as Ht,createElementBlock as Lt}from"vue";var At=Nt({name:"ArrowDownBold",__name:"arrow-down-bold",setup(t){return(e,o)=>(Ht(),Lt("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[zt("path",{fill:"currentColor",d:"M104.704 338.752a64 64 0 0 1 90.496 0l316.8 316.8 316.8-316.8a64 64 0 0 1 90.496 90.496L557.248 791.296a64 64 0 0 1-90.496 0L104.704 429.248a64 64 0 0 1 0-90.496"})]))}}),D=At;import{defineComponent as St}from"vue";import{createElementVNode as qt,openBlock as Ft,createElementBlock as Dt}from"vue";var bt=St({name:"ArrowDown",__name:"arrow-down",setup(t){return(e,o)=>(Ft(),Dt("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[qt("path",{fill:"currentColor",d:"M831.872 340.864 512 652.672 192.128 340.864a30.59 30.59 0 0 0-42.752 0 29.12 29.12 0 0 0 0 41.6L489.664 714.24a32 32 0 0 0 44.672 0l340.288-331.712a29.12 29.12 0 0 0 0-41.728 30.59 30.59 0 0 0-42.752 0z"})]))}}),b=bt;import{defineComponent as yt}from"vue";import{createElementVNode as Pt,openBlock as Rt,createElementBlock as Tt}from"vue";var Ot=yt({name:"ArrowLeftBold",__name:"arrow-left-bold",setup(t){return(e,o)=>(Rt(),Tt("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Pt("path",{fill:"currentColor",d:"M685.248 104.704a64 64 0 0 1 0 90.496L368.448 512l316.8 316.8a64 64 0 0 1-90.496 90.496L232.704 557.248a64 64 0 0 1 0-90.496l362.048-362.048a64 64 0 0 1 90.496 0"})]))}}),y=Ot;import{defineComponent as It}from"vue";import{createElementVNode as Gt,openBlock as Ut,createElementBlock as Wt}from"vue";var Kt=It({name:"ArrowLeft",__name:"arrow-left",setup(t){return(e,o)=>(Ut(),Wt("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Gt("path",{fill:"currentColor",d:"M609.408 149.376 277.76 489.6a32 32 0 0 0 0 44.672l331.648 340.352a29.12 29.12 0 0 0 41.728 0 30.59 30.59 0 0 0 0-42.752L339.264 511.936l311.872-319.872a30.59 30.59 0 0 0 0-42.688 29.12 29.12 0 0 0-41.728 0"})]))}}),P=Kt;import{defineComponent as Qt}from"vue";import{createElementVNode as Zt,openBlock as jt,createElementBlock as Jt}from"vue";var Xt=Qt({name:"ArrowRightBold",__name:"arrow-right-bold",setup(t){return(e,o)=>(jt(),Jt("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Zt("path",{fill:"currentColor",d:"M338.752 104.704a64 64 0 0 0 0 90.496l316.8 316.8-316.8 316.8a64 64 0 0 0 90.496 90.496l362.048-362.048a64 64 0 0 0 0-90.496L429.248 104.704a64 64 0 0 0-90.496 0"})]))}}),R=Xt;import{defineComponent as Yt}from"vue";import{createElementVNode as $t,openBlock as e6,createElementBlock as o6}from"vue";var t6=Yt({name:"ArrowRight",__name:"arrow-right",setup(t){return(e,o)=>(e6(),o6("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[$t("path",{fill:"currentColor",d:"M340.864 149.312a30.59 30.59 0 0 0 0 42.752L652.736 512 340.864 831.872a30.59 30.59 0 0 0 0 42.752 29.12 29.12 0 0 0 41.728 0L714.24 534.336a32 32 0 0 0 0-44.672L382.592 149.376a29.12 29.12 0 0 0-41.728 0z"})]))}}),T=t6;import{defineComponent as a6}from"vue";import{createElementVNode as r6,openBlock as n6,createElementBlock as l6}from"vue";var m6=a6({name:"ArrowUpBold",__name:"arrow-up-bold",setup(t){return(e,o)=>(n6(),l6("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[r6("path",{fill:"currentColor",d:"M104.704 685.248a64 64 0 0 0 90.496 0l316.8-316.8 316.8 316.8a64 64 0 0 0 90.496-90.496L557.248 232.704a64 64 0 0 0-90.496 0L104.704 594.752a64 64 0 0 0 0 90.496"})]))}}),O=m6;import{defineComponent as c6}from"vue";import{createElementVNode as p6,openBlock as s6,createElementBlock as _6}from"vue";var f6=c6({name:"ArrowUp",__name:"arrow-up",setup(t){return(e,o)=>(s6(),_6("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[p6("path",{fill:"currentColor",d:"m488.832 344.32-339.84 356.672a32 32 0 0 0 0 44.16l.384.384a29.44 29.44 0 0 0 42.688 0l320-335.872 319.872 335.872a29.44 29.44 0 0 0 42.688 0l.384-.384a32 32 0 0 0 0-44.16L535.168 344.32a32 32 0 0 0-46.336 0"})]))}}),I=f6;import{defineComponent as i6}from"vue";import{createElementVNode as d6,openBlock as u6,createElementBlock as h6}from"vue";var v6=i6({name:"Avatar",__name:"avatar",setup(t){return(e,o)=>(u6(),h6("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[d6("path",{fill:"currentColor",d:"M628.736 528.896A416 416 0 0 1 928 928H96a415.87 415.87 0 0 1 299.264-399.104L512 704zM720 304a208 208 0 1 1-416 0 208 208 0 0 1 416 0"})]))}}),G=v6;import{defineComponent as w6}from"vue";import{createElementVNode as U,openBlock as B6,createElementBlock as x6}from"vue";var k6=w6({name:"Back",__name:"back",setup(t){return(e,o)=>(B6(),x6("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[U("path",{fill:"currentColor",d:"M224 480h640a32 32 0 1 1 0 64H224a32 32 0 0 1 0-64"}),U("path",{fill:"currentColor",d:"m237.248 512 265.408 265.344a32 32 0 0 1-45.312 45.312l-288-288a32 32 0 0 1 0-45.312l288-288a32 32 0 1 1 45.312 45.312z"})]))}}),W=k6;import{defineComponent as C6}from"vue";import{createElementVNode as K,openBlock as E6,createElementBlock as V6}from"vue";var g6=C6({name:"Baseball",__name:"baseball",setup(t){return(e,o)=>(E6(),V6("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[K("path",{fill:"currentColor",d:"M195.2 828.8a448 448 0 1 1 633.6-633.6 448 448 0 0 1-633.6 633.6m45.248-45.248a384 384 0 1 0 543.104-543.104 384 384 0 0 0-543.104 543.104"}),K("path",{fill:"currentColor",d:"M497.472 96.896c22.784 4.672 44.416 9.472 64.896 14.528a256.128 256.128 0 0 0 350.208 350.208c5.056 20.48 9.856 42.112 14.528 64.896A320.128 320.128 0 0 1 497.472 96.896M108.48 491.904a320.128 320.128 0 0 1 423.616 423.68c-23.04-3.648-44.992-7.424-65.728-11.52a256.128 256.128 0 0 0-346.496-346.432 1737 1737 0 0 1-11.392-65.728"})]))}}),Q=g6;import{defineComponent as M6}from"vue";import{createElementVNode as N6,openBlock as z6,createElementBlock as H6}from"vue";var L6=M6({name:"Basketball",__name:"basketball",setup(t){return(e,o)=>(z6(),H6("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[N6("path",{fill:"currentColor",d:"M778.752 788.224a382.46 382.46 0 0 0 116.032-245.632 256.51 256.51 0 0 0-241.728-13.952 762.9 762.9 0 0 1 125.696 259.584m-55.04 44.224a699.65 699.65 0 0 0-125.056-269.632 256.13 256.13 0 0 0-56.064 331.968 382.7 382.7 0 0 0 181.12-62.336m-254.08 61.248A320.13 320.13 0 0 1 557.76 513.6a716 716 0 0 0-48.192-48.128 320.13 320.13 0 0 1-379.264 88.384 382.4 382.4 0 0 0 110.144 229.696 382.4 382.4 0 0 0 229.184 110.08zM129.28 481.088a256.13 256.13 0 0 0 331.072-56.448 699.65 699.65 0 0 0-268.8-124.352 382.66 382.66 0 0 0-62.272 180.8m106.56-235.84a762.9 762.9 0 0 1 258.688 125.056 256.51 256.51 0 0 0-13.44-241.088A382.46 382.46 0 0 0 235.84 245.248m318.08-114.944c40.576 89.536 37.76 193.92-8.448 281.344a780 780 0 0 1 66.176 66.112 320.83 320.83 0 0 1 282.112-8.128 382.4 382.4 0 0 0-110.144-229.12 382.4 382.4 0 0 0-229.632-110.208zM828.8 828.8a448 448 0 1 1-633.6-633.6 448 448 0 0 1 633.6 633.6"})]))}}),Z=L6;import{defineComponent as A6}from"vue";import{createElementVNode as S6,openBlock as q6,createElementBlock as F6}from"vue";var D6=A6({name:"BellFilled",__name:"bell-filled",setup(t){return(e,o)=>(q6(),F6("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[S6("path",{fill:"currentColor",d:"M640 832a128 128 0 0 1-256 0zm192-64H134.4a38.4 38.4 0 0 1 0-76.8H192V448c0-154.88 110.08-284.16 256.32-313.6a64 64 0 1 1 127.36 0A320.13 320.13 0 0 1 832 448v243.2h57.6a38.4 38.4 0 0 1 0 76.8z"})]))}}),j=D6;import{defineComponent as b6}from"vue";import{createElementVNode as m,openBlock as y6,createElementBlock as P6}from"vue";var R6=b6({name:"Bell",__name:"bell",setup(t){return(e,o)=>(y6(),P6("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[m("path",{fill:"currentColor",d:"M512 64a64 64 0 0 1 64 64v64H448v-64a64 64 0 0 1 64-64"}),m("path",{fill:"currentColor",d:"M256 768h512V448a256 256 0 1 0-512 0zm256-640a320 320 0 0 1 320 320v384H192V448a320 320 0 0 1 320-320"}),m("path",{fill:"currentColor",d:"M96 768h832q32 0 32 32t-32 32H96q-32 0-32-32t32-32m352 128h128a64 64 0 0 1-128 0"})]))}}),J=R6;import{defineComponent as T6}from"vue";import{createElementVNode as a,openBlock as O6,createElementBlock as I6}from"vue";var G6=T6({name:"Bicycle",__name:"bicycle",setup(t){return(e,o)=>(O6(),I6("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[a("path",{fill:"currentColor",d:"M256 832a128 128 0 1 0 0-256 128 128 0 0 0 0 256m0 64a192 192 0 1 1 0-384 192 192 0 0 1 0 384"}),a("path",{fill:"currentColor",d:"M288 672h320q32 0 32 32t-32 32H288q-32 0-32-32t32-32"}),a("path",{fill:"currentColor",d:"M768 832a128 128 0 1 0 0-256 128 128 0 0 0 0 256m0 64a192 192 0 1 1 0-384 192 192 0 0 1 0 384"}),a("path",{fill:"currentColor",d:"M480 192a32 32 0 0 1 0-64h160a32 32 0 0 1 31.04 24.256l96 384a32 32 0 0 1-62.08 15.488L615.04 192zM96 384a32 32 0 0 1 0-64h128a32 32 0 0 1 30.336 21.888l64 192a32 32 0 1 1-60.672 20.224L200.96 384z"}),a("path",{fill:"currentColor",d:"m373.376 599.808-42.752-47.616 320-288 42.752 47.616z"})]))}}),X=G6;import{defineComponent as U6}from"vue";import{createElementVNode as Y,openBlock as W6,createElementBlock as K6}from"vue";var Q6=U6({name:"BottomLeft",__name:"bottom-left",setup(t){return(e,o)=>(W6(),K6("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Y("path",{fill:"currentColor",d:"M256 768h416a32 32 0 1 1 0 64H224a32 32 0 0 1-32-32V352a32 32 0 0 1 64 0z"}),Y("path",{fill:"currentColor",d:"M246.656 822.656a32 32 0 0 1-45.312-45.312l544-544a32 32 0 0 1 45.312 45.312z"})]))}}),$=Q6;import{defineComponent as Z6}from"vue";import{createElementVNode as e2,openBlock as j6,createElementBlock as J6}from"vue";var X6=Z6({name:"BottomRight",__name:"bottom-right",setup(t){return(e,o)=>(j6(),J6("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[e2("path",{fill:"currentColor",d:"M352 768a32 32 0 1 0 0 64h448a32 32 0 0 0 32-32V352a32 32 0 0 0-64 0v416z"}),e2("path",{fill:"currentColor",d:"M777.344 822.656a32 32 0 0 0 45.312-45.312l-544-544a32 32 0 0 0-45.312 45.312z"})]))}}),o2=X6;import{defineComponent as Y6}from"vue";import{createElementVNode as $6,openBlock as e3,createElementBlock as o3}from"vue";var t3=Y6({name:"Bottom",__name:"bottom",setup(t){return(e,o)=>(e3(),o3("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[$6("path",{fill:"currentColor",d:"M544 805.888V168a32 32 0 1 0-64 0v637.888L246.656 557.952a30.72 30.72 0 0 0-45.312 0 35.52 35.52 0 0 0 0 48.064l288 306.048a30.72 30.72 0 0 0 45.312 0l288-306.048a35.52 35.52 0 0 0 0-48 30.72 30.72 0 0 0-45.312 0L544 805.824z"})]))}}),t2=t3;import{defineComponent as a3}from"vue";import{createElementVNode as r3,openBlock as n3,createElementBlock as l3}from"vue";var m3=a3({name:"Bowl",__name:"bowl",setup(t){return(e,o)=>(n3(),l3("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[r3("path",{fill:"currentColor",d:"M714.432 704a351.74 351.74 0 0 0 148.16-256H161.408a351.74 351.74 0 0 0 148.16 256zM288 766.592A415.68 415.68 0 0 1 96 416a32 32 0 0 1 32-32h768a32 32 0 0 1 32 32 415.68 415.68 0 0 1-192 350.592V832a64 64 0 0 1-64 64H352a64 64 0 0 1-64-64zM493.248 320h-90.496l254.4-254.4a32 32 0 1 1 45.248 45.248zm187.328 0h-128l269.696-155.712a32 32 0 0 1 32 55.424zM352 768v64h320v-64z"})]))}}),a2=m3;import{defineComponent as c3}from"vue";import{createElementVNode as c,openBlock as p3,createElementBlock as s3}from"vue";var _3=c3({name:"Box",__name:"box",setup(t){return(e,o)=>(p3(),s3("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[c("path",{fill:"currentColor",d:"M317.056 128 128 344.064V896h768V344.064L706.944 128zm-14.528-64h418.944a32 32 0 0 1 24.064 10.88l206.528 236.096A32 32 0 0 1 960 332.032V928a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V332.032a32 32 0 0 1 7.936-21.12L278.4 75.008A32 32 0 0 1 302.528 64"}),c("path",{fill:"currentColor",d:"M64 320h896v64H64z"}),c("path",{fill:"currentColor",d:"M448 327.872V640h128V327.872L526.08 128h-28.16zM448 64h128l64 256v352a32 32 0 0 1-32 32H416a32 32 0 0 1-32-32V320z"})]))}}),r2=_3;import{defineComponent as f3}from"vue";import{createElementVNode as i3,openBlock as d3,createElementBlock as u3}from"vue";var h3=f3({name:"Briefcase",__name:"briefcase",setup(t){return(e,o)=>(d3(),u3("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[i3("path",{fill:"currentColor",d:"M320 320V128h384v192h192v192H128V320zM128 576h768v320H128zm256-256h256.064V192H384z"})]))}}),n2=h3;import{defineComponent as v3}from"vue";import{createElementVNode as w3,openBlock as B3,createElementBlock as x3}from"vue";var k3=v3({name:"BrushFilled",__name:"brush-filled",setup(t){return(e,o)=>(B3(),x3("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[w3("path",{fill:"currentColor",d:"M608 704v160a96 96 0 0 1-192 0V704h-96a128 128 0 0 1-128-128h640a128 128 0 0 1-128 128zM192 512V128.064h640V512z"})]))}}),l2=k3;import{defineComponent as C3}from"vue";import{createElementVNode as E3,openBlock as V3,createElementBlock as g3}from"vue";var M3=C3({name:"Brush",__name:"brush",setup(t){return(e,o)=>(V3(),g3("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[E3("path",{fill:"currentColor",d:"M896 448H128v192a64 64 0 0 0 64 64h192v192h256V704h192a64 64 0 0 0 64-64zm-770.752-64c0-47.552 5.248-90.24 15.552-128 14.72-54.016 42.496-107.392 83.2-160h417.28l-15.36 70.336L736 96h211.2c-24.832 42.88-41.92 96.256-51.2 160a664 664 0 0 0-6.144 128H960v256a128 128 0 0 1-128 128H704v160a32 32 0 0 1-32 32H352a32 32 0 0 1-32-32V768H192A128 128 0 0 1 64 640V384zm64 0h636.544c-2.048-45.824.256-91.584 6.848-137.216 4.48-30.848 10.688-59.776 18.688-86.784h-96.64l-221.12 141.248L561.92 160H256.512c-25.856 37.888-43.776 75.456-53.952 112.832-8.768 32.064-13.248 69.12-13.312 111.168"})]))}}),m2=M3;import{defineComponent as N3}from"vue";import{createElementVNode as z3,openBlock as H3,createElementBlock as L3}from"vue";var A3=N3({name:"Burger",__name:"burger",setup(t){return(e,o)=>(H3(),L3("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[z3("path",{fill:"currentColor",d:"M160 512a32 32 0 0 0-32 32v64a32 32 0 0 0 30.08 32H864a32 32 0 0 0 32-32v-64a32 32 0 0 0-32-32zm736-58.56A96 96 0 0 1 960 544v64a96 96 0 0 1-51.968 85.312L855.36 833.6a96 96 0 0 1-89.856 62.272H258.496A96 96 0 0 1 168.64 833.6l-52.608-140.224A96 96 0 0 1 64 608v-64a96 96 0 0 1 64-90.56V448a384 384 0 1 1 768 5.44M832 448a320 320 0 0 0-640 0zM512 704H188.352l40.192 107.136a32 32 0 0 0 29.952 20.736h507.008a32 32 0 0 0 29.952-20.736L835.648 704z"})]))}}),c2=A3;import{defineComponent as S3}from"vue";import{createElementVNode as q3,openBlock as F3,createElementBlock as D3}from"vue";var b3=S3({name:"Calendar",__name:"calendar",setup(t){return(e,o)=>(F3(),D3("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[q3("path",{fill:"currentColor",d:"M128 384v512h768V192H768v32a32 32 0 1 1-64 0v-32H320v32a32 32 0 0 1-64 0v-32H128v128h768v64zm192-256h384V96a32 32 0 1 1 64 0v32h160a32 32 0 0 1 32 32v768a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h160V96a32 32 0 0 1 64 0zm-32 384h64a32 32 0 0 1 0 64h-64a32 32 0 0 1 0-64m0 192h64a32 32 0 1 1 0 64h-64a32 32 0 1 1 0-64m192-192h64a32 32 0 0 1 0 64h-64a32 32 0 0 1 0-64m0 192h64a32 32 0 1 1 0 64h-64a32 32 0 1 1 0-64m192-192h64a32 32 0 1 1 0 64h-64a32 32 0 1 1 0-64m0 192h64a32 32 0 1 1 0 64h-64a32 32 0 1 1 0-64"})]))}}),p2=b3;import{defineComponent as y3}from"vue";import{createElementVNode as P3,openBlock as R3,createElementBlock as T3}from"vue";var O3=y3({name:"CameraFilled",__name:"camera-filled",setup(t){return(e,o)=>(R3(),T3("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[P3("path",{fill:"currentColor",d:"M160 224a64 64 0 0 0-64 64v512a64 64 0 0 0 64 64h704a64 64 0 0 0 64-64V288a64 64 0 0 0-64-64H748.416l-46.464-92.672A64 64 0 0 0 644.736 96H379.328a64 64 0 0 0-57.216 35.392L275.776 224zm352 435.2a115.2 115.2 0 1 0 0-230.4 115.2 115.2 0 0 0 0 230.4m0 140.8a256 256 0 1 1 0-512 256 256 0 0 1 0 512"})]))}}),s2=O3;import{defineComponent as I3}from"vue";import{createElementVNode as G3,openBlock as U3,createElementBlock as W3}from"vue";var K3=I3({name:"Camera",__name:"camera",setup(t){return(e,o)=>(U3(),W3("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[G3("path",{fill:"currentColor",d:"M896 256H128v576h768zm-199.424-64-32.064-64h-304.96l-32 64zM96 192h160l46.336-92.608A64 64 0 0 1 359.552 64h304.96a64 64 0 0 1 57.216 35.328L768.192 192H928a32 32 0 0 1 32 32v640a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V224a32 32 0 0 1 32-32m416 512a160 160 0 1 0 0-320 160 160 0 0 0 0 320m0 64a224 224 0 1 1 0-448 224 224 0 0 1 0 448"})]))}}),_2=K3;import{defineComponent as Q3}from"vue";import{createElementVNode as Z3,openBlock as j3,createElementBlock as J3}from"vue";var X3=Q3({name:"CaretBottom",__name:"caret-bottom",setup(t){return(e,o)=>(j3(),J3("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Z3("path",{fill:"currentColor",d:"m192 384 320 384 320-384z"})]))}}),f2=X3;import{defineComponent as Y3}from"vue";import{createElementVNode as $3,openBlock as ea,createElementBlock as oa}from"vue";var ta=Y3({name:"CaretLeft",__name:"caret-left",setup(t){return(e,o)=>(ea(),oa("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[$3("path",{fill:"currentColor",d:"M672 192 288 511.936 672 832z"})]))}}),i2=ta;import{defineComponent as aa}from"vue";import{createElementVNode as ra,openBlock as na,createElementBlock as la}from"vue";var ma=aa({name:"CaretRight",__name:"caret-right",setup(t){return(e,o)=>(na(),la("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[ra("path",{fill:"currentColor",d:"M384 192v640l384-320.064z"})]))}}),d2=ma;import{defineComponent as ca}from"vue";import{createElementVNode as pa,openBlock as sa,createElementBlock as _a}from"vue";var fa=ca({name:"CaretTop",__name:"caret-top",setup(t){return(e,o)=>(sa(),_a("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[pa("path",{fill:"currentColor",d:"M512 320 192 704h639.936z"})]))}}),u2=fa;import{defineComponent as ia}from"vue";import{createElementVNode as da,openBlock as ua,createElementBlock as ha}from"vue";var va=ia({name:"Cellphone",__name:"cellphone",setup(t){return(e,o)=>(ua(),ha("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[da("path",{fill:"currentColor",d:"M256 128a64 64 0 0 0-64 64v640a64 64 0 0 0 64 64h512a64 64 0 0 0 64-64V192a64 64 0 0 0-64-64zm0-64h512a128 128 0 0 1 128 128v640a128 128 0 0 1-128 128H256a128 128 0 0 1-128-128V192A128 128 0 0 1 256 64m128 128h256a32 32 0 1 1 0 64H384a32 32 0 0 1 0-64m128 640a64 64 0 1 1 0-128 64 64 0 0 1 0 128"})]))}}),h2=va;import{defineComponent as wa}from"vue";import{createElementVNode as v2,openBlock as Ba,createElementBlock as xa}from"vue";var ka=wa({name:"ChatDotRound",__name:"chat-dot-round",setup(t){return(e,o)=>(Ba(),xa("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[v2("path",{fill:"currentColor",d:"m174.72 855.68 135.296-45.12 23.68 11.84C388.096 849.536 448.576 864 512 864c211.84 0 384-166.784 384-352S723.84 160 512 160 128 326.784 128 512c0 69.12 24.96 139.264 70.848 199.232l22.08 28.8-46.272 115.584zm-45.248 82.56A32 32 0 0 1 89.6 896l58.368-145.92C94.72 680.32 64 596.864 64 512 64 299.904 256 96 512 96s448 203.904 448 416-192 416-448 416a461.06 461.06 0 0 1-206.912-48.384l-175.616 58.56z"}),v2("path",{fill:"currentColor",d:"M512 563.2a51.2 51.2 0 1 1 0-102.4 51.2 51.2 0 0 1 0 102.4m192 0a51.2 51.2 0 1 1 0-102.4 51.2 51.2 0 0 1 0 102.4m-384 0a51.2 51.2 0 1 1 0-102.4 51.2 51.2 0 0 1 0 102.4"})]))}}),w2=ka;import{defineComponent as Ca}from"vue";import{createElementVNode as B2,openBlock as Ea,createElementBlock as Va}from"vue";var ga=Ca({name:"ChatDotSquare",__name:"chat-dot-square",setup(t){return(e,o)=>(Ea(),Va("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[B2("path",{fill:"currentColor",d:"M273.536 736H800a64 64 0 0 0 64-64V256a64 64 0 0 0-64-64H224a64 64 0 0 0-64 64v570.88zM296 800 147.968 918.4A32 32 0 0 1 96 893.44V256a128 128 0 0 1 128-128h576a128 128 0 0 1 128 128v416a128 128 0 0 1-128 128z"}),B2("path",{fill:"currentColor",d:"M512 499.2a51.2 51.2 0 1 1 0-102.4 51.2 51.2 0 0 1 0 102.4m192 0a51.2 51.2 0 1 1 0-102.4 51.2 51.2 0 0 1 0 102.4m-384 0a51.2 51.2 0 1 1 0-102.4 51.2 51.2 0 0 1 0 102.4"})]))}}),x2=ga;import{defineComponent as Ma}from"vue";import{createElementVNode as k2,openBlock as Na,createElementBlock as za}from"vue";var Ha=Ma({name:"ChatLineRound",__name:"chat-line-round",setup(t){return(e,o)=>(Na(),za("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[k2("path",{fill:"currentColor",d:"m174.72 855.68 135.296-45.12 23.68 11.84C388.096 849.536 448.576 864 512 864c211.84 0 384-166.784 384-352S723.84 160 512 160 128 326.784 128 512c0 69.12 24.96 139.264 70.848 199.232l22.08 28.8-46.272 115.584zm-45.248 82.56A32 32 0 0 1 89.6 896l58.368-145.92C94.72 680.32 64 596.864 64 512 64 299.904 256 96 512 96s448 203.904 448 416-192 416-448 416a461.06 461.06 0 0 1-206.912-48.384l-175.616 58.56z"}),k2("path",{fill:"currentColor",d:"M352 576h320q32 0 32 32t-32 32H352q-32 0-32-32t32-32m32-192h256q32 0 32 32t-32 32H384q-32 0-32-32t32-32"})]))}}),C2=Ha;import{defineComponent as La}from"vue";import{createElementVNode as E2,openBlock as Aa,createElementBlock as Sa}from"vue";var qa=La({name:"ChatLineSquare",__name:"chat-line-square",setup(t){return(e,o)=>(Aa(),Sa("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[E2("path",{fill:"currentColor",d:"M160 826.88 273.536 736H800a64 64 0 0 0 64-64V256a64 64 0 0 0-64-64H224a64 64 0 0 0-64 64zM296 800 147.968 918.4A32 32 0 0 1 96 893.44V256a128 128 0 0 1 128-128h576a128 128 0 0 1 128 128v416a128 128 0 0 1-128 128z"}),E2("path",{fill:"currentColor",d:"M352 512h320q32 0 32 32t-32 32H352q-32 0-32-32t32-32m0-192h320q32 0 32 32t-32 32H352q-32 0-32-32t32-32"})]))}}),V2=qa;import{defineComponent as Fa}from"vue";import{createElementVNode as Da,openBlock as ba,createElementBlock as ya}from"vue";var Pa=Fa({name:"ChatRound",__name:"chat-round",setup(t){return(e,o)=>(ba(),ya("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Da("path",{fill:"currentColor",d:"m174.72 855.68 130.048-43.392 23.424 11.392C382.4 849.984 444.352 864 512 864c223.744 0 384-159.872 384-352 0-192.832-159.104-352-384-352S128 319.168 128 512a341.12 341.12 0 0 0 69.248 204.288l21.632 28.8-44.16 110.528zm-45.248 82.56A32 32 0 0 1 89.6 896l56.512-141.248A405.12 405.12 0 0 1 64 512C64 299.904 235.648 96 512 96s448 203.904 448 416-173.44 416-448 416c-79.68 0-150.848-17.152-211.712-46.72l-170.88 56.96z"})]))}}),g2=Pa;import{defineComponent as Ra}from"vue";import{createElementVNode as Ta,openBlock as Oa,createElementBlock as Ia}from"vue";var Ga=Ra({name:"ChatSquare",__name:"chat-square",setup(t){return(e,o)=>(Oa(),Ia("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Ta("path",{fill:"currentColor",d:"M273.536 736H800a64 64 0 0 0 64-64V256a64 64 0 0 0-64-64H224a64 64 0 0 0-64 64v570.88zM296 800 147.968 918.4A32 32 0 0 1 96 893.44V256a128 128 0 0 1 128-128h576a128 128 0 0 1 128 128v416a128 128 0 0 1-128 128z"})]))}}),M2=Ga;import{defineComponent as Ua}from"vue";import{createElementVNode as Wa,openBlock as Ka,createElementBlock as Qa}from"vue";var Za=Ua({name:"Check",__name:"check",setup(t){return(e,o)=>(Ka(),Qa("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Wa("path",{fill:"currentColor",d:"M406.656 706.944 195.84 496.256a32 32 0 1 0-45.248 45.248l256 256 512-512a32 32 0 0 0-45.248-45.248L406.592 706.944z"})]))}}),N2=Za;import{defineComponent as ja}from"vue";import{createElementVNode as Ja,openBlock as Xa,createElementBlock as Ya}from"vue";var $a=ja({name:"Checked",__name:"checked",setup(t){return(e,o)=>(Xa(),Ya("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Ja("path",{fill:"currentColor",d:"M704 192h160v736H160V192h160.064v64H704zM311.616 537.28l-45.312 45.248L447.36 763.52l316.8-316.8-45.312-45.184L447.36 673.024zM384 192V96h256v96z"})]))}}),z2=$a;import{defineComponent as e8}from"vue";import{createElementVNode as o8,openBlock as t8,createElementBlock as a8}from"vue";var r8=e8({name:"Cherry",__name:"cherry",setup(t){return(e,o)=>(t8(),a8("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[o8("path",{fill:"currentColor",d:"M261.056 449.6c13.824-69.696 34.88-128.96 63.36-177.728 23.744-40.832 61.12-88.64 112.256-143.872H320a32 32 0 0 1 0-64h384a32 32 0 1 1 0 64H554.752c14.912 39.168 41.344 86.592 79.552 141.76 47.36 68.48 84.8 106.752 106.304 114.304a224 224 0 1 1-84.992 14.784c-22.656-22.912-47.04-53.76-73.92-92.608-38.848-56.128-67.008-105.792-84.352-149.312-55.296 58.24-94.528 107.52-117.76 147.2-23.168 39.744-41.088 88.768-53.568 147.072a224.064 224.064 0 1 1-64.96-1.6M288 832a160 160 0 1 0 0-320 160 160 0 0 0 0 320m448-64a160 160 0 1 0 0-320 160 160 0 0 0 0 320"})]))}}),H2=r8;import{defineComponent as n8}from"vue";import{createElementVNode as l8,openBlock as m8,createElementBlock as c8}from"vue";var p8=n8({name:"Chicken",__name:"chicken",setup(t){return(e,o)=>(m8(),c8("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[l8("path",{fill:"currentColor",d:"M349.952 716.992 478.72 588.16a106.7 106.7 0 0 1-26.176-19.072 106.7 106.7 0 0 1-19.072-26.176L304.704 671.744c.768 3.072 1.472 6.144 2.048 9.216l2.048 31.936 31.872 1.984c3.136.64 6.208 1.28 9.28 2.112m57.344 33.152a128 128 0 1 1-216.32 114.432l-1.92-32-32-1.92a128 128 0 1 1 114.432-216.32L416.64 469.248c-2.432-101.44 58.112-239.104 149.056-330.048 107.328-107.328 231.296-85.504 316.8 0 85.44 85.44 107.328 209.408 0 316.8-91.008 90.88-228.672 151.424-330.112 149.056L407.296 750.08zm90.496-226.304c49.536 49.536 233.344-7.04 339.392-113.088 78.208-78.208 63.232-163.072 0-226.304-63.168-63.232-148.032-78.208-226.24 0C504.896 290.496 448.32 474.368 497.792 523.84M244.864 708.928a64 64 0 1 0-59.84 59.84l56.32-3.52zm8.064 127.68a64 64 0 1 0 59.84-59.84l-56.32 3.52z"})]))}}),L2=p8;import{defineComponent as s8}from"vue";import{createElementVNode as p,openBlock as _8,createElementBlock as f8}from"vue";var i8=s8({name:"ChromeFilled",__name:"chrome-filled",setup(t){return(e,o)=>(_8(),f8("svg",{xmlns:"http://www.w3.org/2000/svg","xml:space":"preserve",viewBox:"0 0 1024 1024"},[p("path",{fill:"currentColor",d:"M938.67 512.01c0-44.59-6.82-87.6-19.54-128H682.67a212.37 212.37 0 0 1 42.67 128c.06 38.71-10.45 76.7-30.42 109.87l-182.91 316.8c235.65-.01 426.66-191.02 426.66-426.67"}),p("path",{fill:"currentColor",d:"M576.79 401.63a127.9 127.9 0 0 0-63.56-17.6c-22.36-.22-44.39 5.43-63.89 16.38s-35.79 26.82-47.25 46.02a128 128 0 0 0-2.16 127.44l1.24 2.13a127.9 127.9 0 0 0 46.36 46.61 127.9 127.9 0 0 0 63.38 17.44c22.29.2 44.24-5.43 63.68-16.33a127.94 127.94 0 0 0 47.16-45.79v-.01l1.11-1.92a127.98 127.98 0 0 0 .29-127.46 127.96 127.96 0 0 0-46.36-46.91"}),p("path",{fill:"currentColor",d:"M394.45 333.96A213.34 213.34 0 0 1 512 298.67h369.58A426.5 426.5 0 0 0 512 85.34a425.6 425.6 0 0 0-171.74 35.98 425.6 425.6 0 0 0-142.62 102.22l118.14 204.63a213.4 213.4 0 0 1 78.67-94.21m117.56 604.72H512zm-97.25-236.73a213.3 213.3 0 0 1-89.54-86.81L142.48 298.6c-36.35 62.81-57.13 135.68-57.13 213.42 0 203.81 142.93 374.22 333.95 416.55h.04l118.19-204.71a213.3 213.3 0 0 1-122.77-21.91"})]))}}),A2=i8;import{defineComponent as d8}from"vue";import{createElementVNode as u8,openBlock as h8,createElementBlock as v8}from"vue";var w8=d8({name:"CircleCheckFilled",__name:"circle-check-filled",setup(t){return(e,o)=>(h8(),v8("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[u8("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896m-55.808 536.384-99.52-99.584a38.4 38.4 0 1 0-54.336 54.336l126.72 126.72a38.27 38.27 0 0 0 54.336 0l262.4-262.464a38.4 38.4 0 1 0-54.272-54.336z"})]))}}),S2=w8;import{defineComponent as B8}from"vue";import{createElementVNode as q2,openBlock as x8,createElementBlock as k8}from"vue";var C8=B8({name:"CircleCheck",__name:"circle-check",setup(t){return(e,o)=>(x8(),k8("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[q2("path",{fill:"currentColor",d:"M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768m0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896"}),q2("path",{fill:"currentColor",d:"M745.344 361.344a32 32 0 0 1 45.312 45.312l-288 288a32 32 0 0 1-45.312 0l-160-160a32 32 0 1 1 45.312-45.312L480 626.752z"})]))}}),F2=C8;import{defineComponent as E8}from"vue";import{createElementVNode as V8,openBlock as g8,createElementBlock as M8}from"vue";var N8=E8({name:"CircleCloseFilled",__name:"circle-close-filled",setup(t){return(e,o)=>(g8(),M8("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[V8("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896m0 393.664L407.936 353.6a38.4 38.4 0 1 0-54.336 54.336L457.664 512 353.6 616.064a38.4 38.4 0 1 0 54.336 54.336L512 566.336 616.064 670.4a38.4 38.4 0 1 0 54.336-54.336L566.336 512 670.4 407.936a38.4 38.4 0 1 0-54.336-54.336z"})]))}}),D2=N8;import{defineComponent as z8}from"vue";import{createElementVNode as b2,openBlock as H8,createElementBlock as L8}from"vue";var A8=z8({name:"CircleClose",__name:"circle-close",setup(t){return(e,o)=>(H8(),L8("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[b2("path",{fill:"currentColor",d:"m466.752 512-90.496-90.496a32 32 0 0 1 45.248-45.248L512 466.752l90.496-90.496a32 32 0 1 1 45.248 45.248L557.248 512l90.496 90.496a32 32 0 1 1-45.248 45.248L512 557.248l-90.496 90.496a32 32 0 0 1-45.248-45.248z"}),b2("path",{fill:"currentColor",d:"M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768m0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896"})]))}}),y2=A8;import{defineComponent as S8}from"vue";import{createElementVNode as q8,openBlock as F8,createElementBlock as D8}from"vue";var b8=S8({name:"CirclePlusFilled",__name:"circle-plus-filled",setup(t){return(e,o)=>(F8(),D8("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[q8("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896m-38.4 409.6H326.4a38.4 38.4 0 1 0 0 76.8h147.2v147.2a38.4 38.4 0 0 0 76.8 0V550.4h147.2a38.4 38.4 0 0 0 0-76.8H550.4V326.4a38.4 38.4 0 1 0-76.8 0z"})]))}}),P2=b8;import{defineComponent as y8}from"vue";import{createElementVNode as s,openBlock as P8,createElementBlock as R8}from"vue";var T8=y8({name:"CirclePlus",__name:"circle-plus",setup(t){return(e,o)=>(P8(),R8("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[s("path",{fill:"currentColor",d:"M352 480h320a32 32 0 1 1 0 64H352a32 32 0 0 1 0-64"}),s("path",{fill:"currentColor",d:"M480 672V352a32 32 0 1 1 64 0v320a32 32 0 0 1-64 0"}),s("path",{fill:"currentColor",d:"M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768m0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896"})]))}}),R2=T8;import{defineComponent as O8}from"vue";import{createElementVNode as _,openBlock as I8,createElementBlock as G8}from"vue";var U8=O8({name:"Clock",__name:"clock",setup(t){return(e,o)=>(I8(),G8("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[_("path",{fill:"currentColor",d:"M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768m0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896"}),_("path",{fill:"currentColor",d:"M480 256a32 32 0 0 1 32 32v256a32 32 0 0 1-64 0V288a32 32 0 0 1 32-32"}),_("path",{fill:"currentColor",d:"M480 512h256q32 0 32 32t-32 32H480q-32 0-32-32t32-32"})]))}}),T2=U8;import{defineComponent as W8}from"vue";import{createElementVNode as K8,openBlock as Q8,createElementBlock as Z8}from"vue";var j8=W8({name:"CloseBold",__name:"close-bold",setup(t){return(e,o)=>(Q8(),Z8("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[K8("path",{fill:"currentColor",d:"M195.2 195.2a64 64 0 0 1 90.496 0L512 421.504 738.304 195.2a64 64 0 0 1 90.496 90.496L602.496 512 828.8 738.304a64 64 0 0 1-90.496 90.496L512 602.496 285.696 828.8a64 64 0 0 1-90.496-90.496L421.504 512 195.2 285.696a64 64 0 0 1 0-90.496"})]))}}),O2=j8;import{defineComponent as J8}from"vue";import{createElementVNode as X8,openBlock as Y8,createElementBlock as $8}from"vue";var er=J8({name:"Close",__name:"close",setup(t){return(e,o)=>(Y8(),$8("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[X8("path",{fill:"currentColor",d:"M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z"})]))}}),I2=er;import{defineComponent as or}from"vue";import{createElementVNode as tr,openBlock as ar,createElementBlock as rr}from"vue";var nr=or({name:"Cloudy",__name:"cloudy",setup(t){return(e,o)=>(ar(),rr("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[tr("path",{fill:"currentColor",d:"M598.4 831.872H328.192a256 256 0 0 1-34.496-510.528A352 352 0 1 1 598.4 831.872m-271.36-64h272.256a288 288 0 1 0-248.512-417.664L335.04 381.44l-34.816 3.584a192 192 0 0 0 26.88 382.848z"})]))}}),G2=nr;import{defineComponent as lr}from"vue";import{createElementVNode as mr,openBlock as cr,createElementBlock as pr}from"vue";var sr=lr({name:"CoffeeCup",__name:"coffee-cup",setup(t){return(e,o)=>(cr(),pr("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[mr("path",{fill:"currentColor",d:"M768 192a192 192 0 1 1-8 383.808A256.13 256.13 0 0 1 512 768H320A256 256 0 0 1 64 512V160a32 32 0 0 1 32-32h640a32 32 0 0 1 32 32zm0 64v256a128 128 0 1 0 0-256M96 832h640a32 32 0 1 1 0 64H96a32 32 0 1 1 0-64m32-640v320a192 192 0 0 0 192 192h192a192 192 0 0 0 192-192V192z"})]))}}),U2=sr;import{defineComponent as _r}from"vue";import{createElementVNode as fr,openBlock as ir,createElementBlock as dr}from"vue";var ur=_r({name:"Coffee",__name:"coffee",setup(t){return(e,o)=>(ir(),dr("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[fr("path",{fill:"currentColor",d:"M822.592 192h14.272a32 32 0 0 1 31.616 26.752l21.312 128A32 32 0 0 1 858.24 384h-49.344l-39.04 546.304A32 32 0 0 1 737.92 960H285.824a32 32 0 0 1-32-29.696L214.912 384H165.76a32 32 0 0 1-31.552-37.248l21.312-128A32 32 0 0 1 187.136 192h14.016l-6.72-93.696A32 32 0 0 1 226.368 64h571.008a32 32 0 0 1 31.936 34.304zm-64.128 0 4.544-64H260.736l4.544 64zm-548.16 128H820.48l-10.688-64H214.208l-10.688 64zm68.736 64 36.544 512H708.16l36.544-512z"})]))}}),W2=ur;import{defineComponent as hr}from"vue";import{createElementVNode as f,openBlock as vr,createElementBlock as wr}from"vue";var Br=hr({name:"Coin",__name:"coin",setup(t){return(e,o)=>(vr(),wr("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[f("path",{fill:"currentColor",d:"m161.92 580.736 29.888 58.88C171.328 659.776 160 681.728 160 704c0 82.304 155.328 160 352 160s352-77.696 352-160c0-22.272-11.392-44.16-31.808-64.32l30.464-58.432C903.936 615.808 928 657.664 928 704c0 129.728-188.544 224-416 224S96 833.728 96 704c0-46.592 24.32-88.576 65.92-123.264"}),f("path",{fill:"currentColor",d:"m161.92 388.736 29.888 58.88C171.328 467.84 160 489.792 160 512c0 82.304 155.328 160 352 160s352-77.696 352-160c0-22.272-11.392-44.16-31.808-64.32l30.464-58.432C903.936 423.808 928 465.664 928 512c0 129.728-188.544 224-416 224S96 641.728 96 512c0-46.592 24.32-88.576 65.92-123.264"}),f("path",{fill:"currentColor",d:"M512 544c-227.456 0-416-94.272-416-224S284.544 96 512 96s416 94.272 416 224-188.544 224-416 224m0-64c196.672 0 352-77.696 352-160S708.672 160 512 160s-352 77.696-352 160 155.328 160 352 160"})]))}}),K2=Br;import{defineComponent as xr}from"vue";import{createElementVNode as kr,openBlock as Cr,createElementBlock as Er}from"vue";var Vr=xr({name:"ColdDrink",__name:"cold-drink",setup(t){return(e,o)=>(Cr(),Er("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[kr("path",{fill:"currentColor",d:"M768 64a192 192 0 1 1-69.952 370.88L480 725.376V896h96a32 32 0 1 1 0 64H320a32 32 0 1 1 0-64h96V725.376L76.8 273.536a64 64 0 0 1-12.8-38.4v-10.688a32 32 0 0 1 32-32h71.808l-65.536-83.84a32 32 0 0 1 50.432-39.424l96.256 123.264h337.728A192.06 192.06 0 0 1 768 64M656.896 192.448H800a32 32 0 0 1 32 32v10.624a64 64 0 0 1-12.8 38.4l-80.448 107.2a128 128 0 1 0-81.92-188.16v-.064zm-357.888 64 129.472 165.76a32 32 0 0 1-50.432 39.36l-160.256-205.12H144l304 404.928 304-404.928z"})]))}}),Q2=Vr;import{defineComponent as gr}from"vue";import{createElementVNode as Mr,openBlock as Nr,createElementBlock as zr}from"vue";var Hr=gr({name:"CollectionTag",__name:"collection-tag",setup(t){return(e,o)=>(Nr(),zr("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Mr("path",{fill:"currentColor",d:"M256 128v698.88l196.032-156.864a96 96 0 0 1 119.936 0L768 826.816V128zm-32-64h576a32 32 0 0 1 32 32v797.44a32 32 0 0 1-51.968 24.96L531.968 720a32 32 0 0 0-39.936 0L243.968 918.4A32 32 0 0 1 192 893.44V96a32 32 0 0 1 32-32"})]))}}),Z2=Hr;import{defineComponent as Lr}from"vue";import{createElementVNode as j2,openBlock as Ar,createElementBlock as Sr}from"vue";var qr=Lr({name:"Collection",__name:"collection",setup(t){return(e,o)=>(Ar(),Sr("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[j2("path",{fill:"currentColor",d:"M192 736h640V128H256a64 64 0 0 0-64 64zm64-672h608a32 32 0 0 1 32 32v672a32 32 0 0 1-32 32H160l-32 57.536V192A128 128 0 0 1 256 64"}),j2("path",{fill:"currentColor",d:"M240 800a48 48 0 1 0 0 96h592v-96zm0-64h656v160a64 64 0 0 1-64 64H240a112 112 0 0 1 0-224m144-608v250.88l96-76.8 96 76.8V128zm-64-64h320v381.44a32 32 0 0 1-51.968 24.96L480 384l-108.032 86.4A32 32 0 0 1 320 445.44z"})]))}}),J2=qr;import{defineComponent as Fr}from"vue";import{createElementVNode as Dr,openBlock as br,createElementBlock as yr}from"vue";var Pr=Fr({name:"Comment",__name:"comment",setup(t){return(e,o)=>(br(),yr("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Dr("path",{fill:"currentColor",d:"M736 504a56 56 0 1 1 0-112 56 56 0 0 1 0 112m-224 0a56 56 0 1 1 0-112 56 56 0 0 1 0 112m-224 0a56 56 0 1 1 0-112 56 56 0 0 1 0 112M128 128v640h192v160l224-160h352V128z"})]))}}),X2=Pr;import{defineComponent as Rr}from"vue";import{createElementVNode as Y2,openBlock as Tr,createElementBlock as Or}from"vue";var Ir=Rr({name:"Compass",__name:"compass",setup(t){return(e,o)=>(Tr(),Or("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Y2("path",{fill:"currentColor",d:"M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768m0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896"}),Y2("path",{fill:"currentColor",d:"M725.888 315.008C676.48 428.672 624 513.28 568.576 568.64c-55.424 55.424-139.968 107.904-253.568 157.312a12.8 12.8 0 0 1-16.896-16.832c49.536-113.728 102.016-198.272 157.312-253.632 55.36-55.296 139.904-107.776 253.632-157.312a12.8 12.8 0 0 1 16.832 16.832"})]))}}),$2=Ir;import{defineComponent as Gr}from"vue";import{createElementVNode as ee,openBlock as Ur,createElementBlock as Wr}from"vue";var Kr=Gr({name:"Connection",__name:"connection",setup(t){return(e,o)=>(Ur(),Wr("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[ee("path",{fill:"currentColor",d:"M640 384v64H448a128 128 0 0 0-128 128v128a128 128 0 0 0 128 128h320a128 128 0 0 0 128-128V576a128 128 0 0 0-64-110.848V394.88c74.56 26.368 128 97.472 128 181.056v128a192 192 0 0 1-192 192H448a192 192 0 0 1-192-192V576a192 192 0 0 1 192-192z"}),ee("path",{fill:"currentColor",d:"M384 640v-64h192a128 128 0 0 0 128-128V320a128 128 0 0 0-128-128H256a128 128 0 0 0-128 128v128a128 128 0 0 0 64 110.848v70.272A192.06 192.06 0 0 1 64 448V320a192 192 0 0 1 192-192h320a192 192 0 0 1 192 192v128a192 192 0 0 1-192 192z"})]))}}),oe=Kr;import{defineComponent as Qr}from"vue";import{createElementVNode as te,openBlock as Zr,createElementBlock as jr}from"vue";var Jr=Qr({name:"Coordinate",__name:"coordinate",setup(t){return(e,o)=>(Zr(),jr("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[te("path",{fill:"currentColor",d:"M480 512h64v320h-64z"}),te("path",{fill:"currentColor",d:"M192 896h640a64 64 0 0 0-64-64H256a64 64 0 0 0-64 64m64-128h512a128 128 0 0 1 128 128v64H128v-64a128 128 0 0 1 128-128m256-256a192 192 0 1 0 0-384 192 192 0 0 0 0 384m0 64a256 256 0 1 1 0-512 256 256 0 0 1 0 512"})]))}}),ae=Jr;import{defineComponent as Xr}from"vue";import{createElementVNode as re,openBlock as Yr,createElementBlock as $r}from"vue";var en=Xr({name:"CopyDocument",__name:"copy-document",setup(t){return(e,o)=>(Yr(),$r("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[re("path",{fill:"currentColor",d:"M768 832a128 128 0 0 1-128 128H192A128 128 0 0 1 64 832V384a128 128 0 0 1 128-128v64a64 64 0 0 0-64 64v448a64 64 0 0 0 64 64h448a64 64 0 0 0 64-64z"}),re("path",{fill:"currentColor",d:"M384 128a64 64 0 0 0-64 64v448a64 64 0 0 0 64 64h448a64 64 0 0 0 64-64V192a64 64 0 0 0-64-64zm0-64h448a128 128 0 0 1 128 128v448a128 128 0 0 1-128 128H384a128 128 0 0 1-128-128V192A128 128 0 0 1 384 64"})]))}}),ne=en;import{defineComponent as on}from"vue";import{createElementVNode as le,openBlock as tn,createElementBlock as an}from"vue";var rn=on({name:"Cpu",__name:"cpu",setup(t){return(e,o)=>(tn(),an("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[le("path",{fill:"currentColor",d:"M320 256a64 64 0 0 0-64 64v384a64 64 0 0 0 64 64h384a64 64 0 0 0 64-64V320a64 64 0 0 0-64-64zm0-64h384a128 128 0 0 1 128 128v384a128 128 0 0 1-128 128H320a128 128 0 0 1-128-128V320a128 128 0 0 1 128-128"}),le("path",{fill:"currentColor",d:"M512 64a32 32 0 0 1 32 32v128h-64V96a32 32 0 0 1 32-32m160 0a32 32 0 0 1 32 32v128h-64V96a32 32 0 0 1 32-32m-320 0a32 32 0 0 1 32 32v128h-64V96a32 32 0 0 1 32-32m160 896a32 32 0 0 1-32-32V800h64v128a32 32 0 0 1-32 32m160 0a32 32 0 0 1-32-32V800h64v128a32 32 0 0 1-32 32m-320 0a32 32 0 0 1-32-32V800h64v128a32 32 0 0 1-32 32M64 512a32 32 0 0 1 32-32h128v64H96a32 32 0 0 1-32-32m0-160a32 32 0 0 1 32-32h128v64H96a32 32 0 0 1-32-32m0 320a32 32 0 0 1 32-32h128v64H96a32 32 0 0 1-32-32m896-160a32 32 0 0 1-32 32H800v-64h128a32 32 0 0 1 32 32m0-160a32 32 0 0 1-32 32H800v-64h128a32 32 0 0 1 32 32m0 320a32 32 0 0 1-32 32H800v-64h128a32 32 0 0 1 32 32"})]))}}),me=rn;import{defineComponent as nn}from"vue";import{createElementVNode as ce,openBlock as ln,createElementBlock as mn}from"vue";var cn=nn({name:"CreditCard",__name:"credit-card",setup(t){return(e,o)=>(ln(),mn("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[ce("path",{fill:"currentColor",d:"M896 324.096c0-42.368-2.496-55.296-9.536-68.48a52.35 52.35 0 0 0-22.144-22.08c-13.12-7.04-26.048-9.536-68.416-9.536H228.096c-42.368 0-55.296 2.496-68.48 9.536a52.35 52.35 0 0 0-22.08 22.144c-7.04 13.12-9.536 26.048-9.536 68.416v375.808c0 42.368 2.496 55.296 9.536 68.48a52.35 52.35 0 0 0 22.144 22.08c13.12 7.04 26.048 9.536 68.416 9.536h567.808c42.368 0 55.296-2.496 68.48-9.536a52.35 52.35 0 0 0 22.08-22.144c7.04-13.12 9.536-26.048 9.536-68.416zm64 0v375.808c0 57.088-5.952 77.76-17.088 98.56-11.136 20.928-27.52 37.312-48.384 48.448S852.928 864 795.968 864H228.032c-57.088 0-77.76-5.952-98.56-17.088a116.3 116.3 0 0 1-48.448-48.384c-11.136-20.864-17.088-41.6-17.088-98.56V324.032c0-57.088 5.952-77.76 17.088-98.56 11.136-20.928 27.52-37.312 48.384-48.448s41.6-17.088 98.56-17.088H795.84c57.088 0 77.76 5.952 98.56 17.088 20.928 11.136 37.312 27.52 48.448 48.384s17.088 41.6 17.088 98.56z"}),ce("path",{fill:"currentColor",d:"M64 320h896v64H64zm0 128h896v64H64zm128 192h256v64H192z"})]))}}),pe=cn;import{defineComponent as pn}from"vue";import{createElementVNode as se,openBlock as sn,createElementBlock as _n}from"vue";var fn=pn({name:"Crop",__name:"crop",setup(t){return(e,o)=>(sn(),_n("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[se("path",{fill:"currentColor",d:"M256 768h672a32 32 0 1 1 0 64H224a32 32 0 0 1-32-32V96a32 32 0 0 1 64 0z"}),se("path",{fill:"currentColor",d:"M832 224v704a32 32 0 1 1-64 0V256H96a32 32 0 0 1 0-64h704a32 32 0 0 1 32 32"})]))}}),_e=fn;import{defineComponent as dn}from"vue";import{createElementVNode as un,openBlock as hn,createElementBlock as vn}from"vue";var wn=dn({name:"DArrowLeft",__name:"d-arrow-left",setup(t){return(e,o)=>(hn(),vn("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[un("path",{fill:"currentColor",d:"M529.408 149.376a29.12 29.12 0 0 1 41.728 0 30.59 30.59 0 0 1 0 42.688L259.264 511.936l311.872 319.936a30.59 30.59 0 0 1-.512 43.264 29.12 29.12 0 0 1-41.216-.512L197.76 534.272a32 32 0 0 1 0-44.672zm256 0a29.12 29.12 0 0 1 41.728 0 30.59 30.59 0 0 1 0 42.688L515.264 511.936l311.872 319.936a30.59 30.59 0 0 1-.512 43.264 29.12 29.12 0 0 1-41.216-.512L453.76 534.272a32 32 0 0 1 0-44.672z"})]))}}),fe=wn;import{defineComponent as Bn}from"vue";import{createElementVNode as xn,openBlock as kn,createElementBlock as Cn}from"vue";var En=Bn({name:"DArrowRight",__name:"d-arrow-right",setup(t){return(e,o)=>(kn(),Cn("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[xn("path",{fill:"currentColor",d:"M452.864 149.312a29.12 29.12 0 0 1 41.728.064L826.24 489.664a32 32 0 0 1 0 44.672L494.592 874.624a29.12 29.12 0 0 1-41.728 0 30.59 30.59 0 0 1 0-42.752L764.736 512 452.864 192a30.59 30.59 0 0 1 0-42.688m-256 0a29.12 29.12 0 0 1 41.728.064L570.24 489.664a32 32 0 0 1 0 44.672L238.592 874.624a29.12 29.12 0 0 1-41.728 0 30.59 30.59 0 0 1 0-42.752L508.736 512 196.864 192a30.59 30.59 0 0 1 0-42.688"})]))}}),ie=En;import{defineComponent as Vn}from"vue";import{createElementVNode as gn,openBlock as Mn,createElementBlock as Nn}from"vue";var zn=Vn({name:"DCaret",__name:"d-caret",setup(t){return(e,o)=>(Mn(),Nn("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[gn("path",{fill:"currentColor",d:"m512 128 288 320H224zM224 576h576L512 896z"})]))}}),de=zn;import{defineComponent as Hn}from"vue";import{createElementVNode as Ln,openBlock as An,createElementBlock as Sn}from"vue";var qn=Hn({name:"DataAnalysis",__name:"data-analysis",setup(t){return(e,o)=>(An(),Sn("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Ln("path",{fill:"currentColor",d:"m665.216 768 110.848 192h-73.856L591.36 768H433.024L322.176 960H248.32l110.848-192H160a32 32 0 0 1-32-32V192H64a32 32 0 0 1 0-64h896a32 32 0 1 1 0 64h-64v544a32 32 0 0 1-32 32zM832 192H192v512h640zM352 448a32 32 0 0 1 32 32v64a32 32 0 0 1-64 0v-64a32 32 0 0 1 32-32m160-64a32 32 0 0 1 32 32v128a32 32 0 0 1-64 0V416a32 32 0 0 1 32-32m160-64a32 32 0 0 1 32 32v192a32 32 0 1 1-64 0V352a32 32 0 0 1 32-32"})]))}}),ue=qn;import{defineComponent as Fn}from"vue";import{createElementVNode as i,openBlock as Dn,createElementBlock as bn}from"vue";var yn=Fn({name:"DataBoard",__name:"data-board",setup(t){return(e,o)=>(Dn(),bn("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[i("path",{fill:"currentColor",d:"M32 128h960v64H32z"}),i("path",{fill:"currentColor",d:"M192 192v512h640V192zm-64-64h768v608a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32z"}),i("path",{fill:"currentColor",d:"M322.176 960H248.32l144.64-250.56 55.424 32zm453.888 0h-73.856L576 741.44l55.424-32z"})]))}}),he=yn;import{defineComponent as Pn}from"vue";import{createElementVNode as Rn,openBlock as Tn,createElementBlock as On}from"vue";var In=Pn({name:"DataLine",__name:"data-line",setup(t){return(e,o)=>(Tn(),On("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Rn("path",{fill:"currentColor",d:"M359.168 768H160a32 32 0 0 1-32-32V192H64a32 32 0 0 1 0-64h896a32 32 0 1 1 0 64h-64v544a32 32 0 0 1-32 32H665.216l110.848 192h-73.856L591.36 768H433.024L322.176 960H248.32zM832 192H192v512h640zM342.656 534.656a32 32 0 1 1-45.312-45.312L444.992 341.76l125.44 94.08L679.04 300.032a32 32 0 1 1 49.92 39.936L581.632 524.224 451.008 426.24 342.656 534.592z"})]))}}),ve=In;import{defineComponent as Gn}from"vue";import{createElementVNode as Un,openBlock as Wn,createElementBlock as Kn}from"vue";var Qn=Gn({name:"DeleteFilled",__name:"delete-filled",setup(t){return(e,o)=>(Wn(),Kn("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Un("path",{fill:"currentColor",d:"M352 192V95.936a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V192h256a32 32 0 1 1 0 64H96a32 32 0 0 1 0-64zm64 0h192v-64H416zM192 960a32 32 0 0 1-32-32V256h704v672a32 32 0 0 1-32 32zm224-192a32 32 0 0 0 32-32V416a32 32 0 0 0-64 0v320a32 32 0 0 0 32 32m192 0a32 32 0 0 0 32-32V416a32 32 0 0 0-64 0v320a32 32 0 0 0 32 32"})]))}}),we=Qn;import{defineComponent as Zn}from"vue";import{createElementVNode as d,openBlock as jn,createElementBlock as Jn}from"vue";var Xn=Zn({name:"DeleteLocation",__name:"delete-location",setup(t){return(e,o)=>(jn(),Jn("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[d("path",{fill:"currentColor",d:"M288 896h448q32 0 32 32t-32 32H288q-32 0-32-32t32-32"}),d("path",{fill:"currentColor",d:"M800 416a288 288 0 1 0-576 0c0 118.144 94.528 272.128 288 456.576C705.472 688.128 800 534.144 800 416M512 960C277.312 746.688 160 565.312 160 416a352 352 0 0 1 704 0c0 149.312-117.312 330.688-352 544"}),d("path",{fill:"currentColor",d:"M384 384h256q32 0 32 32t-32 32H384q-32 0-32-32t32-32"})]))}}),Be=Xn;import{defineComponent as Yn}from"vue";import{createElementVNode as $n,openBlock as el,createElementBlock as ol}from"vue";var tl=Yn({name:"Delete",__name:"delete",setup(t){return(e,o)=>(el(),ol("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[$n("path",{fill:"currentColor",d:"M160 256H96a32 32 0 0 1 0-64h256V95.936a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V192h256a32 32 0 1 1 0 64h-64v672a32 32 0 0 1-32 32H192a32 32 0 0 1-32-32zm448-64v-64H416v64zM224 896h576V256H224zm192-128a32 32 0 0 1-32-32V416a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32m192 0a32 32 0 0 1-32-32V416a32 32 0 0 1 64 0v320a32 32 0 0 1-32 32"})]))}}),xe=tl;import{defineComponent as al}from"vue";import{createElementVNode as rl,openBlock as nl,createElementBlock as ll}from"vue";var ml=al({name:"Dessert",__name:"dessert",setup(t){return(e,o)=>(nl(),ll("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[rl("path",{fill:"currentColor",d:"M128 416v-48a144 144 0 0 1 168.64-141.888 224.128 224.128 0 0 1 430.72 0A144 144 0 0 1 896 368v48a384 384 0 0 1-352 382.72V896h-64v-97.28A384 384 0 0 1 128 416m287.104-32.064h193.792a143.81 143.81 0 0 1 58.88-132.736 160.064 160.064 0 0 0-311.552 0 143.81 143.81 0 0 1 58.88 132.8zm-72.896 0a72 72 0 1 0-140.48 0zm339.584 0h140.416a72 72 0 1 0-140.48 0zM512 736a320 320 0 0 0 318.4-288.064H193.6A320 320 0 0 0 512 736M384 896.064h256a32 32 0 1 1 0 64H384a32 32 0 1 1 0-64"})]))}}),ke=ml;import{defineComponent as cl}from"vue";import{createElementVNode as Ce,openBlock as pl,createElementBlock as sl}from"vue";var _l=cl({name:"Discount",__name:"discount",setup(t){return(e,o)=>(pl(),sl("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Ce("path",{fill:"currentColor",d:"M224 704h576V318.336L552.512 115.84a64 64 0 0 0-81.024 0L224 318.336zm0 64v128h576V768zM593.024 66.304l259.2 212.096A32 32 0 0 1 864 303.168V928a32 32 0 0 1-32 32H192a32 32 0 0 1-32-32V303.168a32 32 0 0 1 11.712-24.768l259.2-212.096a128 128 0 0 1 162.112 0"}),Ce("path",{fill:"currentColor",d:"M512 448a64 64 0 1 0 0-128 64 64 0 0 0 0 128m0 64a128 128 0 1 1 0-256 128 128 0 0 1 0 256"})]))}}),Ee=_l;import{defineComponent as fl}from"vue";import{createElementVNode as il,openBlock as dl,createElementBlock as ul}from"vue";var hl=fl({name:"DishDot",__name:"dish-dot",setup(t){return(e,o)=>(dl(),ul("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[il("path",{fill:"currentColor",d:"m384.064 274.56.064-50.688A128 128 0 0 1 512.128 96c70.528 0 127.68 57.152 127.68 127.68v50.752A448.19 448.19 0 0 1 955.392 768H68.544A448.19 448.19 0 0 1 384 274.56zM96 832h832a32 32 0 1 1 0 64H96a32 32 0 1 1 0-64m32-128h768a384 384 0 1 0-768 0m447.808-448v-32.32a63.68 63.68 0 0 0-63.68-63.68 64 64 0 0 0-64 63.936V256z"})]))}}),Ve=hl;import{defineComponent as vl}from"vue";import{createElementVNode as wl,openBlock as Bl,createElementBlock as xl}from"vue";var kl=vl({name:"Dish",__name:"dish",setup(t){return(e,o)=>(Bl(),xl("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[wl("path",{fill:"currentColor",d:"M480 257.152V192h-96a32 32 0 0 1 0-64h256a32 32 0 1 1 0 64h-96v65.152A448 448 0 0 1 955.52 768H68.48A448 448 0 0 1 480 257.152M128 704h768a384 384 0 1 0-768 0M96 832h832a32 32 0 1 1 0 64H96a32 32 0 1 1 0-64"})]))}}),ge=kl;import{defineComponent as Cl}from"vue";import{createElementVNode as El,openBlock as Vl,createElementBlock as gl}from"vue";var Ml=Cl({name:"DocumentAdd",__name:"document-add",setup(t){return(e,o)=>(Vl(),gl("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[El("path",{fill:"currentColor",d:"M832 384H576V128H192v768h640zm-26.496-64L640 154.496V320zM160 64h480l256 256v608a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32m320 512V448h64v128h128v64H544v128h-64V640H352v-64z"})]))}}),Me=Ml;import{defineComponent as Nl}from"vue";import{createElementVNode as zl,openBlock as Hl,createElementBlock as Ll}from"vue";var Al=Nl({name:"DocumentChecked",__name:"document-checked",setup(t){return(e,o)=>(Hl(),Ll("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[zl("path",{fill:"currentColor",d:"M805.504 320 640 154.496V320zM832 384H576V128H192v768h640zM160 64h480l256 256v608a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32m318.4 582.144 180.992-180.992L704.64 510.4 478.4 736.64 320 578.304l45.248-45.312z"})]))}}),Ne=Al;import{defineComponent as Sl}from"vue";import{createElementVNode as ql,openBlock as Fl,createElementBlock as Dl}from"vue";var bl=Sl({name:"DocumentCopy",__name:"document-copy",setup(t){return(e,o)=>(Fl(),Dl("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[ql("path",{fill:"currentColor",d:"M128 320v576h576V320zm-32-64h640a32 32 0 0 1 32 32v640a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V288a32 32 0 0 1 32-32M960 96v704a32 32 0 0 1-32 32h-96v-64h64V128H384v64h-64V96a32 32 0 0 1 32-32h576a32 32 0 0 1 32 32M256 672h320v64H256zm0-192h320v64H256z"})]))}}),ze=bl;import{defineComponent as yl}from"vue";import{createElementVNode as Pl,openBlock as Rl,createElementBlock as Tl}from"vue";var Ol=yl({name:"DocumentDelete",__name:"document-delete",setup(t){return(e,o)=>(Rl(),Tl("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Pl("path",{fill:"currentColor",d:"M805.504 320 640 154.496V320zM832 384H576V128H192v768h640zM160 64h480l256 256v608a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32m308.992 546.304-90.496-90.624 45.248-45.248 90.56 90.496 90.496-90.432 45.248 45.248-90.496 90.56 90.496 90.496-45.248 45.248-90.496-90.496-90.56 90.496-45.248-45.248z"})]))}}),He=Ol;import{defineComponent as Il}from"vue";import{createElementVNode as Gl,openBlock as Ul,createElementBlock as Wl}from"vue";var Kl=Il({name:"DocumentRemove",__name:"document-remove",setup(t){return(e,o)=>(Ul(),Wl("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Gl("path",{fill:"currentColor",d:"M805.504 320 640 154.496V320zM832 384H576V128H192v768h640zM160 64h480l256 256v608a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32m192 512h320v64H352z"})]))}}),Le=Kl;import{defineComponent as Ql}from"vue";import{createElementVNode as Zl,openBlock as jl,createElementBlock as Jl}from"vue";var Xl=Ql({name:"Document",__name:"document",setup(t){return(e,o)=>(jl(),Jl("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Zl("path",{fill:"currentColor",d:"M832 384H576V128H192v768h640zm-26.496-64L640 154.496V320zM160 64h480l256 256v608a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32m160 448h384v64H320zm0-192h160v64H320zm0 384h384v64H320z"})]))}}),Ae=Xl;import{defineComponent as Yl}from"vue";import{createElementVNode as $l,openBlock as em,createElementBlock as om}from"vue";var tm=Yl({name:"Download",__name:"download",setup(t){return(e,o)=>(em(),om("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[$l("path",{fill:"currentColor",d:"M160 832h704a32 32 0 1 1 0 64H160a32 32 0 1 1 0-64m384-253.696 236.288-236.352 45.248 45.248L508.8 704 192 387.2l45.248-45.248L480 584.704V128h64z"})]))}}),Se=tm;import{defineComponent as am}from"vue";import{createElementVNode as rm,openBlock as nm,createElementBlock as lm}from"vue";var mm=am({name:"Drizzling",__name:"drizzling",setup(t){return(e,o)=>(nm(),lm("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[rm("path",{fill:"currentColor",d:"m739.328 291.328-35.2-6.592-12.8-33.408a192.064 192.064 0 0 0-365.952 23.232l-9.92 40.896-41.472 7.04a176.32 176.32 0 0 0-146.24 173.568c0 97.28 78.72 175.936 175.808 175.936h400a192 192 0 0 0 35.776-380.672M959.552 480a256 256 0 0 1-256 256h-400A239.81 239.81 0 0 1 63.744 496.192a240.32 240.32 0 0 1 199.488-236.8 256.128 256.128 0 0 1 487.872-30.976A256.064 256.064 0 0 1 959.552 480M288 800h64v64h-64zm192 0h64v64h-64zm-96 96h64v64h-64zm192 0h64v64h-64zm96-96h64v64h-64z"})]))}}),qe=mm;import{defineComponent as cm}from"vue";import{createElementVNode as pm,openBlock as sm,createElementBlock as _m}from"vue";var fm=cm({name:"EditPen",__name:"edit-pen",setup(t){return(e,o)=>(sm(),_m("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[pm("path",{fill:"currentColor",d:"m199.04 672.64 193.984 112 224-387.968-193.92-112-224 388.032zm-23.872 60.16 32.896 148.288 144.896-45.696zM455.04 229.248l193.92 112 56.704-98.112-193.984-112zM104.32 708.8l384-665.024 304.768 175.936L409.152 884.8h.064l-248.448 78.336zm384 254.272v-64h448v64z"})]))}}),Fe=fm;import{defineComponent as im}from"vue";import{createElementVNode as De,openBlock as dm,createElementBlock as um}from"vue";var hm=im({name:"Edit",__name:"edit",setup(t){return(e,o)=>(dm(),um("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[De("path",{fill:"currentColor",d:"M832 512a32 32 0 1 1 64 0v352a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h352a32 32 0 0 1 0 64H192v640h640z"}),De("path",{fill:"currentColor",d:"m469.952 554.24 52.8-7.552L847.104 222.4a32 32 0 1 0-45.248-45.248L477.44 501.44l-7.552 52.8zm422.4-422.4a96 96 0 0 1 0 135.808l-331.84 331.84a32 32 0 0 1-18.112 9.088L436.8 623.68a32 32 0 0 1-36.224-36.224l15.104-105.6a32 32 0 0 1 9.024-18.112l331.904-331.84a96 96 0 0 1 135.744 0z"})]))}}),be=hm;import{defineComponent as vm}from"vue";import{createElementVNode as wm,openBlock as Bm,createElementBlock as xm}from"vue";var km=vm({name:"ElemeFilled",__name:"eleme-filled",setup(t){return(e,o)=>(Bm(),xm("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[wm("path",{fill:"currentColor",d:"M176 64h672c61.824 0 112 50.176 112 112v672a112 112 0 0 1-112 112H176A112 112 0 0 1 64 848V176c0-61.824 50.176-112 112-112m150.528 173.568c-152.896 99.968-196.544 304.064-97.408 456.96a330.69 330.69 0 0 0 456.96 96.64c9.216-5.888 17.6-11.776 25.152-18.56a18.24 18.24 0 0 0 4.224-24.32L700.352 724.8a47.55 47.55 0 0 0-65.536-14.272A234.56 234.56 0 0 1 310.592 641.6C240 533.248 271.104 387.968 379.456 316.48a234.3 234.3 0 0 1 276.352 15.168c1.664.832 2.56 2.56 3.392 4.224 5.888 8.384 3.328 19.328-5.12 25.216L456.832 489.6a47.55 47.55 0 0 0-14.336 65.472l16 24.384c5.888 8.384 16.768 10.88 25.216 5.056l308.224-199.936a19.584 19.584 0 0 0 6.72-23.488v-.896c-4.992-9.216-10.048-17.6-15.104-26.88-99.968-151.168-304.064-194.88-456.96-95.744zM786.88 504.704l-62.208 40.32c-8.32 5.888-10.88 16.768-4.992 25.216L760 632.32c5.888 8.448 16.768 11.008 25.152 5.12l31.104-20.16a55.36 55.36 0 0 0 16-76.48l-20.224-31.04a19.52 19.52 0 0 0-25.152-5.12z"})]))}}),ye=km;import{defineComponent as Cm}from"vue";import{createElementVNode as Em,openBlock as Vm,createElementBlock as gm}from"vue";var Mm=Cm({name:"Eleme",__name:"eleme",setup(t){return(e,o)=>(Vm(),gm("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Em("path",{fill:"currentColor",d:"M300.032 188.8c174.72-113.28 408-63.36 522.24 109.44 5.76 10.56 11.52 20.16 17.28 30.72v.96a22.4 22.4 0 0 1-7.68 26.88l-352.32 228.48c-9.6 6.72-22.08 3.84-28.8-5.76l-18.24-27.84a54.336 54.336 0 0 1 16.32-74.88l225.6-146.88c9.6-6.72 12.48-19.2 5.76-28.8-.96-1.92-1.92-3.84-3.84-4.8a267.84 267.84 0 0 0-315.84-17.28c-123.84 81.6-159.36 247.68-78.72 371.52a268.096 268.096 0 0 0 370.56 78.72 54.336 54.336 0 0 1 74.88 16.32l17.28 26.88c5.76 9.6 3.84 21.12-4.8 27.84-8.64 7.68-18.24 14.4-28.8 21.12a377.92 377.92 0 0 1-522.24-110.4c-113.28-174.72-63.36-408 111.36-522.24m526.08 305.28a22.336 22.336 0 0 1 28.8 5.76l23.04 35.52a63.23 63.23 0 0 1-18.24 87.36l-35.52 23.04c-9.6 6.72-22.08 3.84-28.8-5.76l-46.08-71.04c-6.72-9.6-3.84-22.08 5.76-28.8z"})]))}}),Pe=Mm;import{defineComponent as Nm}from"vue";import{createElementVNode as zm,openBlock as Hm,createElementBlock as Lm}from"vue";var Am=Nm({name:"ElementPlus",__name:"element-plus",setup(t){return(e,o)=>(Hm(),Lm("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[zm("path",{fill:"currentColor",d:"M839.7 734.7c0 33.3-17.9 41-17.9 41S519.7 949.8 499.2 960c-10.2 5.1-20.5 5.1-30.7 0 0 0-314.9-184.3-325.1-192-5.1-5.1-10.2-12.8-12.8-20.5V368.6c0-17.9 20.5-28.2 20.5-28.2L466 158.6q19.2-7.65 38.4 0s279 161.3 309.8 179.2c17.9 7.7 28.2 25.6 25.6 46.1-.1-5-.1 317.5-.1 350.8M714.2 371.2c-64-35.8-217.6-125.4-217.6-125.4-7.7-5.1-20.5-5.1-30.7 0L217.6 389.1s-17.9 10.2-17.9 23v297c0 5.1 5.1 12.8 7.7 17.9 7.7 5.1 256 148.5 256 148.5 7.7 5.1 17.9 5.1 25.6 0 15.4-7.7 250.9-145.9 250.9-145.9s12.8-5.1 12.8-30.7v-74.2l-276.5 169v-64c0-17.9 7.7-30.7 20.5-46.1L745 535c5.1-7.7 10.2-20.5 10.2-30.7v-66.6l-279 169v-69.1c0-15.4 5.1-30.7 17.9-38.4zM919 135.7c0-5.1-5.1-7.7-7.7-7.7h-58.9V66.6c0-5.1-5.1-5.1-10.2-5.1l-30.7 5.1c-5.1 0-5.1 2.6-5.1 5.1V128h-56.3c-5.1 0-5.1 5.1-7.7 5.1v38.4h69.1v64c0 5.1 5.1 5.1 10.2 5.1l30.7-5.1c5.1 0 5.1-2.6 5.1-5.1v-56.3h64z"})]))}}),Re=Am;import{defineComponent as Sm}from"vue";import{createElementVNode as qm,openBlock as Fm,createElementBlock as Dm}from"vue";var bm=Sm({name:"Expand",__name:"expand",setup(t){return(e,o)=>(Fm(),Dm("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[qm("path",{fill:"currentColor",d:"M128 192h768v128H128zm0 256h512v128H128zm0 256h768v128H128zm576-352 192 160-192 128z"})]))}}),Te=bm;import{defineComponent as ym}from"vue";import{createElementVNode as Pm,openBlock as Rm,createElementBlock as Tm}from"vue";var Om=ym({name:"Failed",__name:"failed",setup(t){return(e,o)=>(Rm(),Tm("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Pm("path",{fill:"currentColor",d:"m557.248 608 135.744-135.744-45.248-45.248-135.68 135.744-135.808-135.68-45.248 45.184L466.752 608l-135.68 135.68 45.184 45.312L512 653.248l135.744 135.744 45.248-45.248L557.312 608zM704 192h160v736H160V192h160v64h384zm-320 0V96h256v96z"})]))}}),Oe=Om;import{defineComponent as Im}from"vue";import{createElementVNode as u,openBlock as Gm,createElementBlock as Um}from"vue";var Wm=Im({name:"Female",__name:"female",setup(t){return(e,o)=>(Gm(),Um("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[u("path",{fill:"currentColor",d:"M512 640a256 256 0 1 0 0-512 256 256 0 0 0 0 512m0 64a320 320 0 1 1 0-640 320 320 0 0 1 0 640"}),u("path",{fill:"currentColor",d:"M512 640q32 0 32 32v256q0 32-32 32t-32-32V672q0-32 32-32"}),u("path",{fill:"currentColor",d:"M352 800h320q32 0 32 32t-32 32H352q-32 0-32-32t32-32"})]))}}),Ie=Wm;import{defineComponent as Km}from"vue";import{createElementVNode as Qm,openBlock as Zm,createElementBlock as jm}from"vue";var Jm=Km({name:"Files",__name:"files",setup(t){return(e,o)=>(Zm(),jm("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Qm("path",{fill:"currentColor",d:"M128 384v448h768V384zm-32-64h832a32 32 0 0 1 32 32v512a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V352a32 32 0 0 1 32-32m64-128h704v64H160zm96-128h512v64H256z"})]))}}),Ge=Jm;import{defineComponent as Xm}from"vue";import{createElementVNode as Ue,openBlock as Ym,createElementBlock as $m}from"vue";var ec=Xm({name:"Film",__name:"film",setup(t){return(e,o)=>(Ym(),$m("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Ue("path",{fill:"currentColor",d:"M160 160v704h704V160zm-32-64h768a32 32 0 0 1 32 32v768a32 32 0 0 1-32 32H128a32 32 0 0 1-32-32V128a32 32 0 0 1 32-32"}),Ue("path",{fill:"currentColor",d:"M320 288V128h64v352h256V128h64v160h160v64H704v128h160v64H704v128h160v64H704v160h-64V544H384v352h-64V736H128v-64h192V544H128v-64h192V352H128v-64z"})]))}}),We=ec;import{defineComponent as oc}from"vue";import{createElementVNode as tc,openBlock as ac,createElementBlock as rc}from"vue";var nc=oc({name:"Filter",__name:"filter",setup(t){return(e,o)=>(ac(),rc("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[tc("path",{fill:"currentColor",d:"M384 523.392V928a32 32 0 0 0 46.336 28.608l192-96A32 32 0 0 0 640 832V523.392l280.768-343.104a32 32 0 1 0-49.536-40.576l-288 352A32 32 0 0 0 576 512v300.224l-128 64V512a32 32 0 0 0-7.232-20.288L195.52 192H704a32 32 0 1 0 0-64H128a32 32 0 0 0-24.768 52.288z"})]))}}),Ke=nc;import{defineComponent as lc}from"vue";import{createElementVNode as mc,openBlock as cc,createElementBlock as pc}from"vue";var sc=lc({name:"Finished",__name:"finished",setup(t){return(e,o)=>(cc(),pc("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[mc("path",{fill:"currentColor",d:"M280.768 753.728 691.456 167.04a32 32 0 1 1 52.416 36.672L314.24 817.472a32 32 0 0 1-45.44 7.296l-230.4-172.8a32 32 0 0 1 38.4-51.2zM736 448a32 32 0 1 1 0-64h192a32 32 0 1 1 0 64zM608 640a32 32 0 0 1 0-64h319.936a32 32 0 1 1 0 64zM480 832a32 32 0 1 1 0-64h447.936a32 32 0 1 1 0 64z"})]))}}),Qe=sc;import{defineComponent as _c}from"vue";import{createElementVNode as Ze,openBlock as fc,createElementBlock as ic}from"vue";var dc=_c({name:"FirstAidKit",__name:"first-aid-kit",setup(t){return(e,o)=>(fc(),ic("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Ze("path",{fill:"currentColor",d:"M192 256a64 64 0 0 0-64 64v448a64 64 0 0 0 64 64h640a64 64 0 0 0 64-64V320a64 64 0 0 0-64-64zm0-64h640a128 128 0 0 1 128 128v448a128 128 0 0 1-128 128H192A128 128 0 0 1 64 768V320a128 128 0 0 1 128-128"}),Ze("path",{fill:"currentColor",d:"M544 512h96a32 32 0 0 1 0 64h-96v96a32 32 0 0 1-64 0v-96h-96a32 32 0 0 1 0-64h96v-96a32 32 0 0 1 64 0zM352 128v64h320v-64zm-32-64h384a32 32 0 0 1 32 32v128a32 32 0 0 1-32 32H320a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32"})]))}}),je=dc;import{defineComponent as uc}from"vue";import{createElementVNode as hc,openBlock as vc,createElementBlock as wc}from"vue";var Bc=uc({name:"Flag",__name:"flag",setup(t){return(e,o)=>(vc(),wc("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[hc("path",{fill:"currentColor",d:"M288 128h608L736 384l160 256H288v320h-96V64h96z"})]))}}),Je=Bc;import{defineComponent as xc}from"vue";import{createElementVNode as kc,openBlock as Cc,createElementBlock as Ec}from"vue";var Vc=xc({name:"Fold",__name:"fold",setup(t){return(e,o)=>(Cc(),Ec("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[kc("path",{fill:"currentColor",d:"M896 192H128v128h768zm0 256H384v128h512zm0 256H128v128h768zM320 384 128 512l192 128z"})]))}}),Xe=Vc;import{defineComponent as gc}from"vue";import{createElementVNode as Mc,openBlock as Nc,createElementBlock as zc}from"vue";var Hc=gc({name:"FolderAdd",__name:"folder-add",setup(t){return(e,o)=>(Nc(),zc("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Mc("path",{fill:"currentColor",d:"M128 192v640h768V320H485.76L357.504 192zm-32-64h287.872l128.384 128H928a32 32 0 0 1 32 32v576a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32m384 416V416h64v128h128v64H544v128h-64V608H352v-64z"})]))}}),Ye=Hc;import{defineComponent as Lc}from"vue";import{createElementVNode as Ac,openBlock as Sc,createElementBlock as qc}from"vue";var Fc=Lc({name:"FolderChecked",__name:"folder-checked",setup(t){return(e,o)=>(Sc(),qc("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Ac("path",{fill:"currentColor",d:"M128 192v640h768V320H485.76L357.504 192zm-32-64h287.872l128.384 128H928a32 32 0 0 1 32 32v576a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32m414.08 502.144 180.992-180.992L736.32 494.4 510.08 720.64l-158.4-158.336 45.248-45.312z"})]))}}),$e=Fc;import{defineComponent as Dc}from"vue";import{createElementVNode as bc,openBlock as yc,createElementBlock as Pc}from"vue";var Rc=Dc({name:"FolderDelete",__name:"folder-delete",setup(t){return(e,o)=>(yc(),Pc("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[bc("path",{fill:"currentColor",d:"M128 192v640h768V320H485.76L357.504 192zm-32-64h287.872l128.384 128H928a32 32 0 0 1 32 32v576a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32m370.752 448-90.496-90.496 45.248-45.248L512 530.752l90.496-90.496 45.248 45.248L557.248 576l90.496 90.496-45.248 45.248L512 621.248l-90.496 90.496-45.248-45.248z"})]))}}),e0=Rc;import{defineComponent as Tc}from"vue";import{createElementVNode as Oc,openBlock as Ic,createElementBlock as Gc}from"vue";var Uc=Tc({name:"FolderOpened",__name:"folder-opened",setup(t){return(e,o)=>(Ic(),Gc("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Oc("path",{fill:"currentColor",d:"M878.08 448H241.92l-96 384h636.16zM832 384v-64H485.76L357.504 192H128v448l57.92-231.744A32 32 0 0 1 216.96 384zm-24.96 512H96a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h287.872l128.384 128H864a32 32 0 0 1 32 32v96h23.04a32 32 0 0 1 31.04 39.744l-112 448A32 32 0 0 1 807.04 896"})]))}}),o0=Uc;import{defineComponent as Wc}from"vue";import{createElementVNode as Kc,openBlock as Qc,createElementBlock as Zc}from"vue";var jc=Wc({name:"FolderRemove",__name:"folder-remove",setup(t){return(e,o)=>(Qc(),Zc("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Kc("path",{fill:"currentColor",d:"M128 192v640h768V320H485.76L357.504 192zm-32-64h287.872l128.384 128H928a32 32 0 0 1 32 32v576a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32m256 416h320v64H352z"})]))}}),t0=jc;import{defineComponent as Jc}from"vue";import{createElementVNode as Xc,openBlock as Yc,createElementBlock as $c}from"vue";var e5=Jc({name:"Folder",__name:"folder",setup(t){return(e,o)=>(Yc(),$c("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Xc("path",{fill:"currentColor",d:"M128 192v640h768V320H485.76L357.504 192zm-32-64h287.872l128.384 128H928a32 32 0 0 1 32 32v576a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32"})]))}}),a0=e5;import{defineComponent as o5}from"vue";import{createElementVNode as t5,openBlock as a5,createElementBlock as r5}from"vue";var n5=o5({name:"Food",__name:"food",setup(t){return(e,o)=>(a5(),r5("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[t5("path",{fill:"currentColor",d:"M128 352.576V352a288 288 0 0 1 491.072-204.224 192 192 0 0 1 274.24 204.48 64 64 0 0 1 57.216 74.24C921.6 600.512 850.048 710.656 736 756.992V800a96 96 0 0 1-96 96H384a96 96 0 0 1-96-96v-43.008c-114.048-46.336-185.6-156.48-214.528-330.496A64 64 0 0 1 128 352.64zm64-.576h64a160 160 0 0 1 320 0h64a224 224 0 0 0-448 0m128 0h192a96 96 0 0 0-192 0m439.424 0h68.544A128.256 128.256 0 0 0 704 192c-15.36 0-29.952 2.688-43.52 7.616 11.328 18.176 20.672 37.76 27.84 58.304A64.128 64.128 0 0 1 759.424 352M672 768H352v32a32 32 0 0 0 32 32h256a32 32 0 0 0 32-32zm-342.528-64h365.056c101.504-32.64 165.76-124.928 192.896-288H136.576c27.136 163.072 91.392 255.36 192.896 288"})]))}}),r0=n5;import{defineComponent as l5}from"vue";import{createElementVNode as n0,openBlock as m5,createElementBlock as c5}from"vue";var p5=l5({name:"Football",__name:"football",setup(t){return(e,o)=>(m5(),c5("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[n0("path",{fill:"currentColor",d:"M512 960a448 448 0 1 1 0-896 448 448 0 0 1 0 896m0-64a384 384 0 1 0 0-768 384 384 0 0 0 0 768"}),n0("path",{fill:"currentColor",d:"M186.816 268.288c16-16.384 31.616-31.744 46.976-46.08 17.472 30.656 39.808 58.112 65.984 81.28l-32.512 56.448a386 386 0 0 1-80.448-91.648m653.696-5.312a385.9 385.9 0 0 1-83.776 96.96l-32.512-56.384a322.9 322.9 0 0 0 68.48-85.76c15.552 14.08 31.488 29.12 47.808 45.184M465.984 445.248l11.136-63.104a323.6 323.6 0 0 0 69.76 0l11.136 63.104a388 388 0 0 1-92.032 0m-62.72-12.8A381.8 381.8 0 0 1 320 396.544l32-55.424a320 320 0 0 0 62.464 27.712l-11.2 63.488zm300.8-35.84a381.8 381.8 0 0 1-83.328 35.84l-11.2-63.552A320 320 0 0 0 672 341.184l32 55.424zm-520.768 364.8a385.9 385.9 0 0 1 83.968-97.28l32.512 56.32c-26.88 23.936-49.856 52.352-67.52 84.032-16-13.44-32.32-27.712-48.96-43.072m657.536.128a1443 1443 0 0 1-49.024 43.072 321.4 321.4 0 0 0-67.584-84.16l32.512-56.32c33.216 27.456 61.696 60.352 84.096 97.408M465.92 578.752a388 388 0 0 1 92.032 0l-11.136 63.104a323.6 323.6 0 0 0-69.76 0zm-62.72 12.8 11.2 63.552a320 320 0 0 0-62.464 27.712L320 627.392a381.8 381.8 0 0 1 83.264-35.84zm300.8 35.84-32 55.424a318.3 318.3 0 0 0-62.528-27.712l11.2-63.488c29.44 8.64 57.28 20.736 83.264 35.776z"})]))}}),l0=p5;import{defineComponent as s5}from"vue";import{createElementVNode as _5,openBlock as f5,createElementBlock as i5}from"vue";var d5=s5({name:"ForkSpoon",__name:"fork-spoon",setup(t){return(e,o)=>(f5(),i5("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[_5("path",{fill:"currentColor",d:"M256 410.304V96a32 32 0 0 1 64 0v314.304a96 96 0 0 0 64-90.56V96a32 32 0 0 1 64 0v223.744a160 160 0 0 1-128 156.8V928a32 32 0 1 1-64 0V476.544a160 160 0 0 1-128-156.8V96a32 32 0 0 1 64 0v223.744a96 96 0 0 0 64 90.56M672 572.48C581.184 552.128 512 446.848 512 320c0-141.44 85.952-256 192-256s192 114.56 192 256c0 126.848-69.184 232.128-160 252.48V928a32 32 0 1 1-64 0zM704 512c66.048 0 128-82.56 128-192s-61.952-192-128-192-128 82.56-128 192 61.952 192 128 192"})]))}}),m0=d5;import{defineComponent as u5}from"vue";import{createElementVNode as h5,openBlock as v5,createElementBlock as w5}from"vue";var B5=u5({name:"Fries",__name:"fries",setup(t){return(e,o)=>(v5(),w5("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[h5("path",{fill:"currentColor",d:"M608 224v-64a32 32 0 0 0-64 0v336h26.88A64 64 0 0 0 608 484.096zm101.12 160A64 64 0 0 0 672 395.904V384h64V224a32 32 0 1 0-64 0v160zm74.88 0a92.928 92.928 0 0 1 91.328 110.08l-60.672 323.584A96 96 0 0 1 720.32 896H303.68a96 96 0 0 1-94.336-78.336L148.672 494.08A92.928 92.928 0 0 1 240 384h-16V224a96 96 0 0 1 188.608-25.28A95.74 95.74 0 0 1 480 197.44V160a96 96 0 0 1 188.608-25.28A96 96 0 0 1 800 224v160zM670.784 512a128 128 0 0 1-99.904 48H453.12a128 128 0 0 1-99.84-48H352v-1.536a128 128 0 0 1-9.984-14.976L314.88 448H240a28.928 28.928 0 0 0-28.48 34.304L241.088 640h541.824l29.568-157.696A28.928 28.928 0 0 0 784 448h-74.88l-27.136 47.488A132 132 0 0 1 672 510.464V512zM480 288a32 32 0 0 0-64 0v196.096A64 64 0 0 0 453.12 496H480zm-128 96V224a32 32 0 0 0-64 0v160zh-37.12A64 64 0 0 1 352 395.904zm-98.88 320 19.072 101.888A32 32 0 0 0 303.68 832h416.64a32 32 0 0 0 31.488-26.112L770.88 704z"})]))}}),c0=B5;import{defineComponent as x5}from"vue";import{createElementVNode as k5,openBlock as C5,createElementBlock as E5}from"vue";var V5=x5({name:"FullScreen",__name:"full-screen",setup(t){return(e,o)=>(C5(),E5("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[k5("path",{fill:"currentColor",d:"m160 96.064 192 .192a32 32 0 0 1 0 64l-192-.192V352a32 32 0 0 1-64 0V96h64zm0 831.872V928H96V672a32 32 0 1 1 64 0v191.936l192-.192a32 32 0 1 1 0 64zM864 96.064V96h64v256a32 32 0 1 1-64 0V160.064l-192 .192a32 32 0 1 1 0-64zm0 831.872-192-.192a32 32 0 0 1 0-64l192 .192V672a32 32 0 1 1 64 0v256h-64z"})]))}}),p0=V5;import{defineComponent as g5}from"vue";import{createElementVNode as M5,openBlock as N5,createElementBlock as z5}from"vue";var H5=g5({name:"GobletFull",__name:"goblet-full",setup(t){return(e,o)=>(N5(),z5("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[M5("path",{fill:"currentColor",d:"M256 320h512c0-78.592-12.608-142.4-36.928-192h-434.24C269.504 192.384 256 256.256 256 320m503.936 64H264.064a256.128 256.128 0 0 0 495.872 0M544 638.4V896h96a32 32 0 1 1 0 64H384a32 32 0 1 1 0-64h96V638.4A320 320 0 0 1 192 320c0-85.632 21.312-170.944 64-256h512c42.688 64.32 64 149.632 64 256a320 320 0 0 1-288 318.4"})]))}}),s0=H5;import{defineComponent as L5}from"vue";import{createElementVNode as A5,openBlock as S5,createElementBlock as q5}from"vue";var F5=L5({name:"GobletSquareFull",__name:"goblet-square-full",setup(t){return(e,o)=>(S5(),q5("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[A5("path",{fill:"currentColor",d:"M256 270.912c10.048 6.72 22.464 14.912 28.992 18.624a220.16 220.16 0 0 0 114.752 30.72c30.592 0 49.408-9.472 91.072-41.152l.64-.448c52.928-40.32 82.368-55.04 132.288-54.656 55.552.448 99.584 20.8 142.72 57.408l1.536 1.28V128H256zm.96 76.288C266.368 482.176 346.88 575.872 512 576c157.44.064 237.952-85.056 253.248-209.984a952 952 0 0 1-40.192-35.712c-32.704-27.776-63.36-41.92-101.888-42.24-31.552-.256-50.624 9.28-93.12 41.6l-.576.448c-52.096 39.616-81.024 54.208-129.792 54.208-54.784 0-100.48-13.376-142.784-37.056zM480 638.848C250.624 623.424 192 442.496 192 319.68V96a32 32 0 0 1 32-32h576a32 32 0 0 1 32 32v224c0 122.816-58.624 303.68-288 318.912V896h96a32 32 0 1 1 0 64H384a32 32 0 1 1 0-64h96z"})]))}}),_0=F5;import{defineComponent as D5}from"vue";import{createElementVNode as b5,openBlock as y5,createElementBlock as P5}from"vue";var R5=D5({name:"GobletSquare",__name:"goblet-square",setup(t){return(e,o)=>(y5(),P5("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[b5("path",{fill:"currentColor",d:"M544 638.912V896h96a32 32 0 1 1 0 64H384a32 32 0 1 1 0-64h96V638.848C250.624 623.424 192 442.496 192 319.68V96a32 32 0 0 1 32-32h576a32 32 0 0 1 32 32v224c0 122.816-58.624 303.68-288 318.912M256 319.68c0 149.568 80 256.192 256 256.256C688.128 576 768 469.568 768 320V128H256z"})]))}}),f0=R5;import{defineComponent as T5}from"vue";import{createElementVNode as O5,openBlock as I5,createElementBlock as G5}from"vue";var U5=T5({name:"Goblet",__name:"goblet",setup(t){return(e,o)=>(I5(),G5("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[O5("path",{fill:"currentColor",d:"M544 638.4V896h96a32 32 0 1 1 0 64H384a32 32 0 1 1 0-64h96V638.4A320 320 0 0 1 192 320c0-85.632 21.312-170.944 64-256h512c42.688 64.32 64 149.632 64 256a320 320 0 0 1-288 318.4M256 320a256 256 0 1 0 512 0c0-78.592-12.608-142.4-36.928-192h-434.24C269.504 192.384 256 256.256 256 320"})]))}}),i0=U5;import{defineComponent as W5}from"vue";import{createElementVNode as d0,openBlock as K5,createElementBlock as Q5}from"vue";var Z5=W5({name:"GoldMedal",__name:"gold-medal",setup(t){return(e,o)=>(K5(),Q5("svg",{xmlns:"http://www.w3.org/2000/svg","xml:space":"preserve",viewBox:"0 0 1024 1024"},[d0("path",{fill:"currentColor",d:"m772.13 452.84 53.86-351.81c1.32-10.01-1.17-18.68-7.49-26.02S804.35 64 795.01 64H228.99v-.01h-.06c-9.33 0-17.15 3.67-23.49 11.01s-8.83 16.01-7.49 26.02l53.87 351.89C213.54 505.73 193.59 568.09 192 640c2 90.67 33.17 166.17 93.5 226.5S421.33 957.99 512 960c90.67-2 166.17-33.17 226.5-93.5 60.33-60.34 91.49-135.83 93.5-226.5-1.59-71.94-21.56-134.32-59.87-187.16M640.01 128h117.02l-39.01 254.02c-20.75-10.64-40.74-19.73-59.94-27.28-5.92-3-11.95-5.8-18.08-8.41V128zM576 128v198.76c-13.18-2.58-26.74-4.43-40.67-5.55-8.07-.8-15.85-1.2-23.33-1.2-10.54 0-21.09.66-31.64 1.96a360 360 0 0 0-32.36 4.79V128zm-192 0h.04v218.3c-6.22 2.66-12.34 5.5-18.36 8.56-19.13 7.54-39.02 16.6-59.66 27.16L267.01 128zm308.99 692.99c-48 48-108.33 73-180.99 75.01-72.66-2.01-132.99-27.01-180.99-75.01S258.01 712.66 256 640c2.01-72.66 27.01-132.99 75.01-180.99 19.67-19.67 41.41-35.47 65.22-47.41 38.33-15.04 71.15-23.92 98.44-26.65 5.07-.41 10.2-.7 15.39-.88.63-.01 1.28-.03 1.91-.03.66 0 1.35.03 2.02.04 5.11.17 10.15.46 15.13.86 27.4 2.71 60.37 11.65 98.91 26.79 23.71 11.93 45.36 27.69 64.96 47.29 48 48 73 108.33 75.01 180.99-2.01 72.65-27.01 132.98-75.01 180.98"}),d0("path",{fill:"currentColor",d:"M544 480H416v64h64v192h-64v64h192v-64h-64z"})]))}}),u0=Z5;import{defineComponent as j5}from"vue";import{createElementVNode as J5,openBlock as X5,createElementBlock as Y5}from"vue";var $5=j5({name:"GoodsFilled",__name:"goods-filled",setup(t){return(e,o)=>(X5(),Y5("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[J5("path",{fill:"currentColor",d:"M192 352h640l64 544H128zm128 224h64V448h-64zm320 0h64V448h-64zM384 288h-64a192 192 0 1 1 384 0h-64a128 128 0 1 0-256 0"})]))}}),h0=$5;import{defineComponent as e9}from"vue";import{createElementVNode as o9,openBlock as t9,createElementBlock as a9}from"vue";var r9=e9({name:"Goods",__name:"goods",setup(t){return(e,o)=>(t9(),a9("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[o9("path",{fill:"currentColor",d:"M320 288v-22.336C320 154.688 405.504 64 512 64s192 90.688 192 201.664v22.4h131.072a32 32 0 0 1 31.808 28.8l57.6 576a32 32 0 0 1-31.808 35.2H131.328a32 32 0 0 1-31.808-35.2l57.6-576a32 32 0 0 1 31.808-28.8H320zm64 0h256v-22.336C640 189.248 582.272 128 512 128s-128 61.248-128 137.664v22.4zm-64 64H217.92l-51.2 512h690.56l-51.264-512H704v96a32 32 0 1 1-64 0v-96H384v96a32 32 0 0 1-64 0z"})]))}}),v0=r9;import{defineComponent as n9}from"vue";import{createElementVNode as l9,openBlock as m9,createElementBlock as c9}from"vue";var p9=n9({name:"Grape",__name:"grape",setup(t){return(e,o)=>(m9(),c9("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[l9("path",{fill:"currentColor",d:"M544 195.2a160 160 0 0 1 96 60.8 160 160 0 1 1 146.24 254.976 160 160 0 0 1-128 224 160 160 0 1 1-292.48 0 160 160 0 0 1-128-224A160 160 0 1 1 384 256a160 160 0 0 1 96-60.8V128h-64a32 32 0 0 1 0-64h192a32 32 0 0 1 0 64h-64zM512 448a96 96 0 1 0 0-192 96 96 0 0 0 0 192m-256 0a96 96 0 1 0 0-192 96 96 0 0 0 0 192m128 224a96 96 0 1 0 0-192 96 96 0 0 0 0 192m128 224a96 96 0 1 0 0-192 96 96 0 0 0 0 192m128-224a96 96 0 1 0 0-192 96 96 0 0 0 0 192m128-224a96 96 0 1 0 0-192 96 96 0 0 0 0 192"})]))}}),w0=p9;import{defineComponent as s9}from"vue";import{createElementVNode as _9,openBlock as f9,createElementBlock as i9}from"vue";var d9=s9({name:"Grid",__name:"grid",setup(t){return(e,o)=>(f9(),i9("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[_9("path",{fill:"currentColor",d:"M640 384v256H384V384zm64 0h192v256H704zm-64 512H384V704h256zm64 0V704h192v192zm-64-768v192H384V128zm64 0h192v192H704zM320 384v256H128V384zm0 512H128V704h192zm0-768v192H128V128z"})]))}}),B0=d9;import{defineComponent as u9}from"vue";import{createElementVNode as x0,openBlock as h9,createElementBlock as v9}from"vue";var w9=u9({name:"Guide",__name:"guide",setup(t){return(e,o)=>(h9(),v9("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[x0("path",{fill:"currentColor",d:"M640 608h-64V416h64zm0 160v160a32 32 0 0 1-32 32H416a32 32 0 0 1-32-32V768h64v128h128V768zM384 608V416h64v192zm256-352h-64V128H448v128h-64V96a32 32 0 0 1 32-32h192a32 32 0 0 1 32 32z"}),x0("path",{fill:"currentColor",d:"m220.8 256-71.232 80 71.168 80H768V256zm-14.4-64H800a32 32 0 0 1 32 32v224a32 32 0 0 1-32 32H206.4a32 32 0 0 1-23.936-10.752l-99.584-112a32 32 0 0 1 0-42.496l99.584-112A32 32 0 0 1 206.4 192m678.784 496-71.104 80H266.816V608h547.2zm-56.768-144H234.88a32 32 0 0 0-32 32v224a32 32 0 0 0 32 32h593.6a32 32 0 0 0 23.936-10.752l99.584-112a32 32 0 0 0 0-42.496l-99.584-112A32 32 0 0 0 828.48 544z"})]))}}),k0=w9;import{defineComponent as B9}from"vue";import{createElementVNode as x9,openBlock as k9,createElementBlock as C9}from"vue";var E9=B9({name:"Handbag",__name:"handbag",setup(t){return(e,o)=>(k9(),C9("svg",{xmlns:"http://www.w3.org/2000/svg","xml:space":"preserve",viewBox:"0 0 1024 1024"},[x9("path",{fill:"currentColor",d:"M887.01 264.99c-6-5.99-13.67-8.99-23.01-8.99H704c-1.34-54.68-20.01-100.01-56-136s-81.32-54.66-136-56c-54.68 1.34-100.01 20.01-136 56s-54.66 81.32-56 136H160c-9.35 0-17.02 3-23.01 8.99-5.99 6-8.99 13.67-8.99 23.01v640c0 9.35 2.99 17.02 8.99 23.01S150.66 960 160 960h704c9.35 0 17.02-2.99 23.01-8.99S896 937.34 896 928V288c0-9.35-2.99-17.02-8.99-23.01M421.5 165.5c24.32-24.34 54.49-36.84 90.5-37.5 35.99.68 66.16 13.18 90.5 37.5s36.84 54.49 37.5 90.5H384c.68-35.99 13.18-66.16 37.5-90.5M832 896H192V320h128v128h64V320h256v128h64V320h128z"})]))}}),C0=E9;import{defineComponent as V9}from"vue";import{createElementVNode as g9,openBlock as M9,createElementBlock as N9}from"vue";var z9=V9({name:"Headset",__name:"headset",setup(t){return(e,o)=>(M9(),N9("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[g9("path",{fill:"currentColor",d:"M896 529.152V512a384 384 0 1 0-768 0v17.152A128 128 0 0 1 320 640v128a128 128 0 1 1-256 0V512a448 448 0 1 1 896 0v256a128 128 0 1 1-256 0V640a128 128 0 0 1 192-110.848M896 640a64 64 0 0 0-128 0v128a64 64 0 0 0 128 0zm-768 0v128a64 64 0 0 0 128 0V640a64 64 0 1 0-128 0"})]))}}),E0=z9;import{defineComponent as H9}from"vue";import{createElementVNode as L9,openBlock as A9,createElementBlock as S9}from"vue";var q9=H9({name:"HelpFilled",__name:"help-filled",setup(t){return(e,o)=>(A9(),S9("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[L9("path",{fill:"currentColor",d:"M926.784 480H701.312A192.51 192.51 0 0 0 544 322.688V97.216A416.064 416.064 0 0 1 926.784 480m0 64A416.064 416.064 0 0 1 544 926.784V701.312A192.51 192.51 0 0 0 701.312 544zM97.28 544h225.472A192.51 192.51 0 0 0 480 701.312v225.472A416.064 416.064 0 0 1 97.216 544zm0-64A416.064 416.064 0 0 1 480 97.216v225.472A192.51 192.51 0 0 0 322.688 480H97.216z"})]))}}),V0=q9;import{defineComponent as F9}from"vue";import{createElementVNode as D9,openBlock as b9,createElementBlock as y9}from"vue";var P9=F9({name:"Help",__name:"help",setup(t){return(e,o)=>(b9(),y9("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[D9("path",{fill:"currentColor",d:"m759.936 805.248-90.944-91.008A254.9 254.9 0 0 1 512 768a254.9 254.9 0 0 1-156.992-53.76l-90.944 91.008A382.46 382.46 0 0 0 512 896c94.528 0 181.12-34.176 247.936-90.752m45.312-45.312A382.46 382.46 0 0 0 896 512c0-94.528-34.176-181.12-90.752-247.936l-91.008 90.944C747.904 398.4 768 452.864 768 512s-20.096 113.6-53.76 156.992zm-45.312-541.184A382.46 382.46 0 0 0 512 128c-94.528 0-181.12 34.176-247.936 90.752l90.944 91.008A254.9 254.9 0 0 1 512 256c59.136 0 113.6 20.096 156.992 53.76zm-541.184 45.312A382.46 382.46 0 0 0 128 512c0 94.528 34.176 181.12 90.752 247.936l91.008-90.944A254.9 254.9 0 0 1 256 512c0-59.136 20.096-113.6 53.76-156.992zm417.28 394.496a194.6 194.6 0 0 0 22.528-22.528C686.912 602.56 704 559.232 704 512a191.23 191.23 0 0 0-67.968-146.56A191.3 191.3 0 0 0 512 320a191.23 191.23 0 0 0-146.56 67.968C337.088 421.44 320 464.768 320 512a191.23 191.23 0 0 0 67.968 146.56C421.44 686.912 464.768 704 512 704c47.296 0 90.56-17.088 124.032-45.44M512 960a448 448 0 1 1 0-896 448 448 0 0 1 0 896"})]))}}),g0=P9;import{defineComponent as R9}from"vue";import{createElementVNode as M0,openBlock as T9,createElementBlock as O9}from"vue";var I9=R9({name:"Hide",__name:"hide",setup(t){return(e,o)=>(T9(),O9("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[M0("path",{fill:"currentColor",d:"M876.8 156.8c0-9.6-3.2-16-9.6-22.4s-12.8-9.6-22.4-9.6-16 3.2-22.4 9.6L736 220.8c-64-32-137.6-51.2-224-60.8-160 16-288 73.6-377.6 176S0 496 0 512s48 73.6 134.4 176c22.4 25.6 44.8 48 73.6 67.2l-86.4 89.6c-6.4 6.4-9.6 12.8-9.6 22.4s3.2 16 9.6 22.4 12.8 9.6 22.4 9.6 16-3.2 22.4-9.6l704-710.4c3.2-6.4 6.4-12.8 6.4-22.4m-646.4 528Q115.2 579.2 76.8 512q43.2-72 153.6-172.8C304 272 400 230.4 512 224c64 3.2 124.8 19.2 176 44.8l-54.4 54.4C598.4 300.8 560 288 512 288c-64 0-115.2 22.4-160 64s-64 96-64 160c0 48 12.8 89.6 35.2 124.8L256 707.2c-9.6-6.4-19.2-16-25.6-22.4m140.8-96Q352 555.2 352 512c0-44.8 16-83.2 48-112s67.2-48 112-48c28.8 0 54.4 6.4 73.6 19.2zM889.599 336c-12.8-16-28.8-28.8-41.6-41.6l-48 48c73.6 67.2 124.8 124.8 150.4 169.6q-43.2 72-153.6 172.8c-73.6 67.2-172.8 108.8-284.8 115.2-51.2-3.2-99.2-12.8-140.8-28.8l-48 48c57.6 22.4 118.4 38.4 188.8 44.8 160-16 288-73.6 377.6-176S1024 528 1024 512s-48.001-73.6-134.401-176"}),M0("path",{fill:"currentColor",d:"M511.998 672c-12.8 0-25.6-3.2-38.4-6.4l-51.2 51.2c28.8 12.8 57.6 19.2 89.6 19.2 64 0 115.2-22.4 160-64 41.6-41.6 64-96 64-160 0-32-6.4-64-19.2-89.6l-51.2 51.2c3.2 12.8 6.4 25.6 6.4 38.4 0 44.8-16 83.2-48 112s-67.2 48-112 48"})]))}}),N0=I9;import{defineComponent as G9}from"vue";import{createElementVNode as U9,openBlock as W9,createElementBlock as K9}from"vue";var Q9=G9({name:"Histogram",__name:"histogram",setup(t){return(e,o)=>(W9(),K9("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[U9("path",{fill:"currentColor",d:"M416 896V128h192v768zm-288 0V448h192v448zm576 0V320h192v576z"})]))}}),z0=Q9;import{defineComponent as Z9}from"vue";import{createElementVNode as j9,openBlock as J9,createElementBlock as X9}from"vue";var Y9=Z9({name:"HomeFilled",__name:"home-filled",setup(t){return(e,o)=>(J9(),X9("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[j9("path",{fill:"currentColor",d:"M512 128 128 447.936V896h255.936V640H640v256h255.936V447.936z"})]))}}),H0=Y9;import{defineComponent as $9}from"vue";import{createElementVNode as ep,openBlock as op,createElementBlock as tp}from"vue";var ap=$9({name:"HotWater",__name:"hot-water",setup(t){return(e,o)=>(op(),tp("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[ep("path",{fill:"currentColor",d:"M273.067 477.867h477.866V409.6H273.067zm0 68.266v51.2A187.733 187.733 0 0 0 460.8 785.067h102.4a187.733 187.733 0 0 0 187.733-187.734v-51.2zm-34.134-204.8h546.134a34.133 34.133 0 0 1 34.133 34.134v221.866a256 256 0 0 1-256 256H460.8a256 256 0 0 1-256-256V375.467a34.133 34.133 0 0 1 34.133-34.134M512 34.133a34.133 34.133 0 0 1 34.133 34.134v170.666a34.133 34.133 0 0 1-68.266 0V68.267A34.133 34.133 0 0 1 512 34.133M375.467 102.4a34.133 34.133 0 0 1 34.133 34.133v102.4a34.133 34.133 0 0 1-68.267 0v-102.4a34.133 34.133 0 0 1 34.134-34.133m273.066 0a34.133 34.133 0 0 1 34.134 34.133v102.4a34.133 34.133 0 1 1-68.267 0v-102.4a34.133 34.133 0 0 1 34.133-34.133M170.667 921.668h682.666a34.133 34.133 0 1 1 0 68.267H170.667a34.133 34.133 0 1 1 0-68.267"})]))}}),L0=ap;import{defineComponent as rp}from"vue";import{createElementVNode as np,openBlock as lp,createElementBlock as mp}from"vue";var cp=rp({name:"House",__name:"house",setup(t){return(e,o)=>(lp(),mp("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[np("path",{fill:"currentColor",d:"M192 413.952V896h640V413.952L512 147.328zM139.52 374.4l352-293.312a32 32 0 0 1 40.96 0l352 293.312A32 32 0 0 1 896 398.976V928a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V398.976a32 32 0 0 1 11.52-24.576"})]))}}),A0=cp;import{defineComponent as pp}from"vue";import{createElementVNode as sp,openBlock as _p,createElementBlock as fp}from"vue";var ip=pp({name:"IceCreamRound",__name:"ice-cream-round",setup(t){return(e,o)=>(_p(),fp("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[sp("path",{fill:"currentColor",d:"m308.352 489.344 226.304 226.304a32 32 0 0 0 45.248 0L783.552 512A192 192 0 1 0 512 240.448L308.352 444.16a32 32 0 0 0 0 45.248zm135.744 226.304L308.352 851.392a96 96 0 0 1-135.744-135.744l135.744-135.744-45.248-45.248a96 96 0 0 1 0-135.808L466.752 195.2A256 256 0 0 1 828.8 557.248L625.152 760.96a96 96 0 0 1-135.808 0l-45.248-45.248zM398.848 670.4 353.6 625.152 217.856 760.896a32 32 0 0 0 45.248 45.248zm248.96-384.64a32 32 0 0 1 0 45.248L466.624 512a32 32 0 1 1-45.184-45.248l180.992-181.056a32 32 0 0 1 45.248 0zm90.496 90.496a32 32 0 0 1 0 45.248L557.248 602.496A32 32 0 1 1 512 557.248l180.992-180.992a32 32 0 0 1 45.312 0"})]))}}),S0=ip;import{defineComponent as dp}from"vue";import{createElementVNode as up,openBlock as hp,createElementBlock as vp}from"vue";var wp=dp({name:"IceCreamSquare",__name:"ice-cream-square",setup(t){return(e,o)=>(hp(),vp("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[up("path",{fill:"currentColor",d:"M416 640h256a32 32 0 0 0 32-32V160a32 32 0 0 0-32-32H352a32 32 0 0 0-32 32v448a32 32 0 0 0 32 32zm192 64v160a96 96 0 0 1-192 0V704h-64a96 96 0 0 1-96-96V160a96 96 0 0 1 96-96h320a96 96 0 0 1 96 96v448a96 96 0 0 1-96 96zm-64 0h-64v160a32 32 0 1 0 64 0z"})]))}}),q0=wp;import{defineComponent as Bp}from"vue";import{createElementVNode as xp,openBlock as kp,createElementBlock as Cp}from"vue";var Ep=Bp({name:"IceCream",__name:"ice-cream",setup(t){return(e,o)=>(kp(),Cp("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[xp("path",{fill:"currentColor",d:"M128.64 448a208 208 0 0 1 193.536-191.552 224 224 0 0 1 445.248 15.488A208.13 208.13 0 0 1 894.784 448H896L548.8 983.68a32 32 0 0 1-53.248.704L128 448zm64.256 0h286.208a144 144 0 0 0-286.208 0m351.36 0h286.272a144 144 0 0 0-286.272 0m-294.848 64 271.808 396.608L778.24 512zM511.68 352.64a207.87 207.87 0 0 1 189.184-96.192 160 160 0 0 0-314.752 5.632c52.608 12.992 97.28 46.08 125.568 90.56"})]))}}),F0=Ep;import{defineComponent as Vp}from"vue";import{createElementVNode as gp,openBlock as Mp,createElementBlock as Np}from"vue";var zp=Vp({name:"IceDrink",__name:"ice-drink",setup(t){return(e,o)=>(Mp(),Np("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[gp("path",{fill:"currentColor",d:"M512 448v128h239.68l16.064-128zm-64 0H256.256l16.064 128H448zm64-255.36V384h247.744A256.13 256.13 0 0 0 512 192.64m-64 8.064A256.45 256.45 0 0 0 264.256 384H448zm64-72.064A320.13 320.13 0 0 1 825.472 384H896a32 32 0 1 1 0 64h-64v1.92l-56.96 454.016A64 64 0 0 1 711.552 960H312.448a64 64 0 0 1-63.488-56.064L192 449.92V448h-64a32 32 0 0 1 0-64h70.528A320.38 320.38 0 0 1 448 135.04V96a96 96 0 0 1 96-96h128a32 32 0 1 1 0 64H544a32 32 0 0 0-32 32zM743.68 640H280.32l32.128 256h399.104z"})]))}}),D0=zp;import{defineComponent as Hp}from"vue";import{createElementVNode as Lp,openBlock as Ap,createElementBlock as Sp}from"vue";var qp=Hp({name:"IceTea",__name:"ice-tea",setup(t){return(e,o)=>(Ap(),Sp("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Lp("path",{fill:"currentColor",d:"M197.696 259.648a320.128 320.128 0 0 1 628.608 0A96 96 0 0 1 896 352v64a96 96 0 0 1-71.616 92.864l-49.408 395.072A64 64 0 0 1 711.488 960H312.512a64 64 0 0 1-63.488-56.064l-49.408-395.072A96 96 0 0 1 128 416v-64a96 96 0 0 1 69.696-92.352M264.064 256h495.872a256.128 256.128 0 0 0-495.872 0m495.424 256H264.512l48 384h398.976zM224 448h576a32 32 0 0 0 32-32v-64a32 32 0 0 0-32-32H224a32 32 0 0 0-32 32v64a32 32 0 0 0 32 32m160 192h64v64h-64zm192 64h64v64h-64zm-128 64h64v64h-64zm64-192h64v64h-64z"})]))}}),b0=qp;import{defineComponent as Fp}from"vue";import{createElementVNode as Dp,openBlock as bp,createElementBlock as yp}from"vue";var Pp=Fp({name:"InfoFilled",__name:"info-filled",setup(t){return(e,o)=>(bp(),yp("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Dp("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896.064A448 448 0 0 1 512 64m67.2 275.072c33.28 0 60.288-23.104 60.288-57.344s-27.072-57.344-60.288-57.344c-33.28 0-60.16 23.104-60.16 57.344s26.88 57.344 60.16 57.344M590.912 699.2c0-6.848 2.368-24.64 1.024-34.752l-52.608 60.544c-10.88 11.456-24.512 19.392-30.912 17.28a12.99 12.99 0 0 1-8.256-14.72l87.68-276.992c7.168-35.136-12.544-67.2-54.336-71.296-44.096 0-108.992 44.736-148.48 101.504 0 6.784-1.28 23.68.064 33.792l52.544-60.608c10.88-11.328 23.552-19.328 29.952-17.152a12.8 12.8 0 0 1 7.808 16.128L388.48 728.576c-10.048 32.256 8.96 63.872 55.04 71.04 67.84 0 107.904-43.648 147.456-100.416z"})]))}}),y0=Pp;import{defineComponent as Rp}from"vue";import{createElementVNode as Tp,openBlock as Op,createElementBlock as Ip}from"vue";var Gp=Rp({name:"Iphone",__name:"iphone",setup(t){return(e,o)=>(Op(),Ip("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Tp("path",{fill:"currentColor",d:"M224 768v96.064a64 64 0 0 0 64 64h448a64 64 0 0 0 64-64V768zm0-64h576V160a64 64 0 0 0-64-64H288a64 64 0 0 0-64 64zm32 288a96 96 0 0 1-96-96V128a96 96 0 0 1 96-96h512a96 96 0 0 1 96 96v768a96 96 0 0 1-96 96zm304-144a48 48 0 1 1-96 0 48 48 0 0 1 96 0"})]))}}),P0=Gp;import{defineComponent as Up}from"vue";import{createElementVNode as Wp,openBlock as Kp,createElementBlock as Qp}from"vue";var Zp=Up({name:"Key",__name:"key",setup(t){return(e,o)=>(Kp(),Qp("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Wp("path",{fill:"currentColor",d:"M448 456.064V96a32 32 0 0 1 32-32.064L672 64a32 32 0 0 1 0 64H512v128h160a32 32 0 0 1 0 64H512v128a256 256 0 1 1-64 8.064M512 896a192 192 0 1 0 0-384 192 192 0 0 0 0 384"})]))}}),R0=Zp;import{defineComponent as jp}from"vue";import{createElementVNode as Jp,openBlock as Xp,createElementBlock as Yp}from"vue";var $p=jp({name:"KnifeFork",__name:"knife-fork",setup(t){return(e,o)=>(Xp(),Yp("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Jp("path",{fill:"currentColor",d:"M256 410.56V96a32 32 0 0 1 64 0v314.56A96 96 0 0 0 384 320V96a32 32 0 0 1 64 0v224a160 160 0 0 1-128 156.8V928a32 32 0 1 1-64 0V476.8A160 160 0 0 1 128 320V96a32 32 0 0 1 64 0v224a96 96 0 0 0 64 90.56m384-250.24V544h126.72c-3.328-78.72-12.928-147.968-28.608-207.744-14.336-54.528-46.848-113.344-98.112-175.872zM640 608v320a32 32 0 1 1-64 0V64h64c85.312 89.472 138.688 174.848 160 256s32 177.152 32 288z"})]))}}),T0=$p;import{defineComponent as es}from"vue";import{createElementVNode as O0,openBlock as os,createElementBlock as ts}from"vue";var as=es({name:"Lightning",__name:"lightning",setup(t){return(e,o)=>(os(),ts("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[O0("path",{fill:"currentColor",d:"M288 671.36v64.128A239.81 239.81 0 0 1 63.744 496.192a240.32 240.32 0 0 1 199.488-236.8 256.128 256.128 0 0 1 487.872-30.976A256.064 256.064 0 0 1 736 734.016v-64.768a192 192 0 0 0 3.328-377.92l-35.2-6.592-12.8-33.408a192.064 192.064 0 0 0-365.952 23.232l-9.92 40.896-41.472 7.04a176.32 176.32 0 0 0-146.24 173.568c0 91.968 70.464 167.36 160.256 175.232z"}),O0("path",{fill:"currentColor",d:"M416 736a32 32 0 0 1-27.776-47.872l128-224a32 32 0 1 1 55.552 31.744L471.168 672H608a32 32 0 0 1 27.776 47.872l-128 224a32 32 0 1 1-55.68-31.744L552.96 736z"})]))}}),I0=as;import{defineComponent as rs}from"vue";import{createElementVNode as ns,openBlock as ls,createElementBlock as ms}from"vue";var cs=rs({name:"Link",__name:"link",setup(t){return(e,o)=>(ls(),ms("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[ns("path",{fill:"currentColor",d:"M715.648 625.152 670.4 579.904l90.496-90.56c75.008-74.944 85.12-186.368 22.656-248.896-62.528-62.464-173.952-52.352-248.96 22.656L444.16 353.6l-45.248-45.248 90.496-90.496c100.032-99.968 251.968-110.08 339.456-22.656 87.488 87.488 77.312 239.424-22.656 339.456l-90.496 90.496zm-90.496 90.496-90.496 90.496C434.624 906.112 282.688 916.224 195.2 828.8c-87.488-87.488-77.312-239.424 22.656-339.456l90.496-90.496 45.248 45.248-90.496 90.56c-75.008 74.944-85.12 186.368-22.656 248.896 62.528 62.464 173.952 52.352 248.96-22.656l90.496-90.496zm0-362.048 45.248 45.248L398.848 670.4 353.6 625.152z"})]))}}),G0=cs;import{defineComponent as ps}from"vue";import{createElementVNode as ss,openBlock as _s,createElementBlock as fs}from"vue";var is=ps({name:"List",__name:"list",setup(t){return(e,o)=>(_s(),fs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[ss("path",{fill:"currentColor",d:"M704 192h160v736H160V192h160v64h384zM288 512h448v-64H288zm0 256h448v-64H288zm96-576V96h256v96z"})]))}}),U0=is;import{defineComponent as ds}from"vue";import{createElementVNode as us,openBlock as hs,createElementBlock as vs}from"vue";var ws=ds({name:"Loading",__name:"loading",setup(t){return(e,o)=>(hs(),vs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[us("path",{fill:"currentColor",d:"M512 64a32 32 0 0 1 32 32v192a32 32 0 0 1-64 0V96a32 32 0 0 1 32-32m0 640a32 32 0 0 1 32 32v192a32 32 0 1 1-64 0V736a32 32 0 0 1 32-32m448-192a32 32 0 0 1-32 32H736a32 32 0 1 1 0-64h192a32 32 0 0 1 32 32m-640 0a32 32 0 0 1-32 32H96a32 32 0 0 1 0-64h192a32 32 0 0 1 32 32M195.2 195.2a32 32 0 0 1 45.248 0L376.32 331.008a32 32 0 0 1-45.248 45.248L195.2 240.448a32 32 0 0 1 0-45.248m452.544 452.544a32 32 0 0 1 45.248 0L828.8 783.552a32 32 0 0 1-45.248 45.248L647.744 692.992a32 32 0 0 1 0-45.248M828.8 195.264a32 32 0 0 1 0 45.184L692.992 376.32a32 32 0 0 1-45.248-45.248l135.808-135.808a32 32 0 0 1 45.248 0m-452.544 452.48a32 32 0 0 1 0 45.248L240.448 828.8a32 32 0 0 1-45.248-45.248l135.808-135.808a32 32 0 0 1 45.248 0"})]))}}),W0=ws;import{defineComponent as Bs}from"vue";import{createElementVNode as xs,openBlock as ks,createElementBlock as Cs}from"vue";var Es=Bs({name:"LocationFilled",__name:"location-filled",setup(t){return(e,o)=>(ks(),Cs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[xs("path",{fill:"currentColor",d:"M512 928c23.936 0 117.504-68.352 192.064-153.152C803.456 661.888 864 535.808 864 416c0-189.632-155.84-320-352-320S160 226.368 160 416c0 120.32 60.544 246.4 159.936 359.232C394.432 859.84 488 928 512 928m0-435.2a64 64 0 1 0 0-128 64 64 0 0 0 0 128m0 140.8a204.8 204.8 0 1 1 0-409.6 204.8 204.8 0 0 1 0 409.6"})]))}}),K0=Es;import{defineComponent as Vs}from"vue";import{createElementVNode as h,openBlock as gs,createElementBlock as Ms}from"vue";var Ns=Vs({name:"LocationInformation",__name:"location-information",setup(t){return(e,o)=>(gs(),Ms("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[h("path",{fill:"currentColor",d:"M288 896h448q32 0 32 32t-32 32H288q-32 0-32-32t32-32"}),h("path",{fill:"currentColor",d:"M800 416a288 288 0 1 0-576 0c0 118.144 94.528 272.128 288 456.576C705.472 688.128 800 534.144 800 416M512 960C277.312 746.688 160 565.312 160 416a352 352 0 0 1 704 0c0 149.312-117.312 330.688-352 544"}),h("path",{fill:"currentColor",d:"M512 512a96 96 0 1 0 0-192 96 96 0 0 0 0 192m0 64a160 160 0 1 1 0-320 160 160 0 0 1 0 320"})]))}}),Q0=Ns;import{defineComponent as zs}from"vue";import{createElementVNode as Z0,openBlock as Hs,createElementBlock as Ls}from"vue";var As=zs({name:"Location",__name:"location",setup(t){return(e,o)=>(Hs(),Ls("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Z0("path",{fill:"currentColor",d:"M800 416a288 288 0 1 0-576 0c0 118.144 94.528 272.128 288 456.576C705.472 688.128 800 534.144 800 416M512 960C277.312 746.688 160 565.312 160 416a352 352 0 0 1 704 0c0 149.312-117.312 330.688-352 544"}),Z0("path",{fill:"currentColor",d:"M512 512a96 96 0 1 0 0-192 96 96 0 0 0 0 192m0 64a160 160 0 1 1 0-320 160 160 0 0 1 0 320"})]))}}),j0=As;import{defineComponent as Ss}from"vue";import{createElementVNode as J0,openBlock as qs,createElementBlock as Fs}from"vue";var Ds=Ss({name:"Lock",__name:"lock",setup(t){return(e,o)=>(qs(),Fs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[J0("path",{fill:"currentColor",d:"M224 448a32 32 0 0 0-32 32v384a32 32 0 0 0 32 32h576a32 32 0 0 0 32-32V480a32 32 0 0 0-32-32zm0-64h576a96 96 0 0 1 96 96v384a96 96 0 0 1-96 96H224a96 96 0 0 1-96-96V480a96 96 0 0 1 96-96"}),J0("path",{fill:"currentColor",d:"M512 544a32 32 0 0 1 32 32v192a32 32 0 1 1-64 0V576a32 32 0 0 1 32-32m192-160v-64a192 192 0 1 0-384 0v64zM512 64a256 256 0 0 1 256 256v128H256V320A256 256 0 0 1 512 64"})]))}}),X0=Ds;import{defineComponent as bs}from"vue";import{createElementVNode as ys,openBlock as Ps,createElementBlock as Rs}from"vue";var Ts=bs({name:"Lollipop",__name:"lollipop",setup(t){return(e,o)=>(Ps(),Rs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[ys("path",{fill:"currentColor",d:"M513.28 448a64 64 0 1 1 76.544 49.728A96 96 0 0 0 768 448h64a160 160 0 0 1-320 0zm-126.976-29.696a256 256 0 1 0 43.52-180.48A256 256 0 0 1 832 448h-64a192 192 0 0 0-381.696-29.696m105.664 249.472L285.696 874.048a96 96 0 0 1-135.68-135.744l206.208-206.272a320 320 0 1 1 135.744 135.744m-54.464-36.032a322 322 0 0 1-45.248-45.248L195.2 783.552a32 32 0 1 0 45.248 45.248l197.056-197.12z"})]))}}),Y0=Ts;import{defineComponent as Os}from"vue";import{createElementVNode as Is,openBlock as Gs,createElementBlock as Us}from"vue";var Ws=Os({name:"MagicStick",__name:"magic-stick",setup(t){return(e,o)=>(Gs(),Us("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Is("path",{fill:"currentColor",d:"M512 64h64v192h-64zm0 576h64v192h-64zM160 480v-64h192v64zm576 0v-64h192v64zM249.856 199.04l45.248-45.184L430.848 289.6 385.6 334.848 249.856 199.104zM657.152 606.4l45.248-45.248 135.744 135.744-45.248 45.248zM114.048 923.2 68.8 877.952l316.8-316.8 45.248 45.248zM702.4 334.848 657.152 289.6l135.744-135.744 45.248 45.248z"})]))}}),$0=Ws;import{defineComponent as Ks}from"vue";import{createElementVNode as Qs,openBlock as Zs,createElementBlock as js}from"vue";var Js=Ks({name:"Magnet",__name:"magnet",setup(t){return(e,o)=>(Zs(),js("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Qs("path",{fill:"currentColor",d:"M832 320V192H704v320a192 192 0 1 1-384 0V192H192v128h128v64H192v128a320 320 0 0 0 640 0V384H704v-64zM640 512V128h256v384a384 384 0 1 1-768 0V128h256v384a128 128 0 1 0 256 0"})]))}}),e1=Js;import{defineComponent as Xs}from"vue";import{createElementVNode as v,openBlock as Ys,createElementBlock as $s}from"vue";var e_=Xs({name:"Male",__name:"male",setup(t){return(e,o)=>(Ys(),$s("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[v("path",{fill:"currentColor",d:"M399.5 849.5a225 225 0 1 0 0-450 225 225 0 0 0 0 450m0 56.25a281.25 281.25 0 1 1 0-562.5 281.25 281.25 0 0 1 0 562.5m253.125-787.5h225q28.125 0 28.125 28.125T877.625 174.5h-225q-28.125 0-28.125-28.125t28.125-28.125"}),v("path",{fill:"currentColor",d:"M877.625 118.25q28.125 0 28.125 28.125v225q0 28.125-28.125 28.125T849.5 371.375v-225q0-28.125 28.125-28.125"}),v("path",{fill:"currentColor",d:"M604.813 458.9 565.1 419.131l292.613-292.668 39.825 39.824z"})]))}}),o1=e_;import{defineComponent as o_}from"vue";import{createElementVNode as t_,openBlock as a_,createElementBlock as r_}from"vue";var n_=o_({name:"Management",__name:"management",setup(t){return(e,o)=>(a_(),r_("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[t_("path",{fill:"currentColor",d:"M576 128v288l96-96 96 96V128h128v768H320V128zm-448 0h128v768H128z"})]))}}),t1=n_;import{defineComponent as l_}from"vue";import{createElementVNode as a1,openBlock as m_,createElementBlock as c_}from"vue";var p_=l_({name:"MapLocation",__name:"map-location",setup(t){return(e,o)=>(m_(),c_("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[a1("path",{fill:"currentColor",d:"M800 416a288 288 0 1 0-576 0c0 118.144 94.528 272.128 288 456.576C705.472 688.128 800 534.144 800 416M512 960C277.312 746.688 160 565.312 160 416a352 352 0 0 1 704 0c0 149.312-117.312 330.688-352 544"}),a1("path",{fill:"currentColor",d:"M512 448a64 64 0 1 0 0-128 64 64 0 0 0 0 128m0 64a128 128 0 1 1 0-256 128 128 0 0 1 0 256m345.6 192L960 960H672v-64H352v64H64l102.4-256zm-68.928 0H235.328l-76.8 192h706.944z"})]))}}),r1=p_;import{defineComponent as s_}from"vue";import{createElementVNode as n1,openBlock as __,createElementBlock as f_}from"vue";var i_=s_({name:"Medal",__name:"medal",setup(t){return(e,o)=>(__(),f_("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[n1("path",{fill:"currentColor",d:"M512 896a256 256 0 1 0 0-512 256 256 0 0 0 0 512m0 64a320 320 0 1 1 0-640 320 320 0 0 1 0 640"}),n1("path",{fill:"currentColor",d:"M576 128H448v200a286.7 286.7 0 0 1 64-8c19.52 0 40.832 2.688 64 8zm64 0v219.648c24.448 9.088 50.56 20.416 78.4 33.92L757.44 128zm-256 0H266.624l39.04 253.568c27.84-13.504 53.888-24.832 78.336-33.92zM229.312 64h565.376a32 32 0 0 1 31.616 36.864L768 480c-113.792-64-199.104-96-256-96s-142.208 32-256 96l-58.304-379.136A32 32 0 0 1 229.312 64"})]))}}),l1=i_;import{defineComponent as d_}from"vue";import{createElementVNode as w,openBlock as u_,createElementBlock as h_}from"vue";var v_=d_({name:"Memo",__name:"memo",setup(t){return(e,o)=>(u_(),h_("svg",{xmlns:"http://www.w3.org/2000/svg","xml:space":"preserve",viewBox:"0 0 1024 1024"},[w("path",{fill:"currentColor",d:"M480 320h192c21.33 0 32-10.67 32-32s-10.67-32-32-32H480c-21.33 0-32 10.67-32 32s10.67 32 32 32"}),w("path",{fill:"currentColor",d:"M887.01 72.99C881.01 67 873.34 64 864 64H160c-9.35 0-17.02 3-23.01 8.99C131 78.99 128 86.66 128 96v832c0 9.35 2.99 17.02 8.99 23.01S150.66 960 160 960h704c9.35 0 17.02-2.99 23.01-8.99S896 937.34 896 928V96c0-9.35-3-17.02-8.99-23.01M192 896V128h96v768zm640 0H352V128h480z"}),w("path",{fill:"currentColor",d:"M480 512h192c21.33 0 32-10.67 32-32s-10.67-32-32-32H480c-21.33 0-32 10.67-32 32s10.67 32 32 32m0 192h192c21.33 0 32-10.67 32-32s-10.67-32-32-32H480c-21.33 0-32 10.67-32 32s10.67 32 32 32"})]))}}),m1=v_;import{defineComponent as w_}from"vue";import{createElementVNode as B_,openBlock as x_,createElementBlock as k_}from"vue";var C_=w_({name:"Menu",__name:"menu",setup(t){return(e,o)=>(x_(),k_("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[B_("path",{fill:"currentColor",d:"M160 448a32 32 0 0 1-32-32V160.064a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32V416a32 32 0 0 1-32 32zm448 0a32 32 0 0 1-32-32V160.064a32 32 0 0 1 32-32h255.936a32 32 0 0 1 32 32V416a32 32 0 0 1-32 32zM160 896a32 32 0 0 1-32-32V608a32 32 0 0 1 32-32h256a32 32 0 0 1 32 32v256a32 32 0 0 1-32 32zm448 0a32 32 0 0 1-32-32V608a32 32 0 0 1 32-32h255.936a32 32 0 0 1 32 32v256a32 32 0 0 1-32 32z"})]))}}),c1=C_;import{defineComponent as E_}from"vue";import{createElementVNode as V_,openBlock as g_,createElementBlock as M_}from"vue";var N_=E_({name:"MessageBox",__name:"message-box",setup(t){return(e,o)=>(g_(),M_("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[V_("path",{fill:"currentColor",d:"M288 384h448v64H288zm96-128h256v64H384zM131.456 512H384v128h256V512h252.544L721.856 192H302.144zM896 576H704v128H320V576H128v256h768zM275.776 128h472.448a32 32 0 0 1 28.608 17.664l179.84 359.552A32 32 0 0 1 960 519.552V864a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V519.552a32 32 0 0 1 3.392-14.336l179.776-359.552A32 32 0 0 1 275.776 128"})]))}}),p1=N_;import{defineComponent as z_}from"vue";import{createElementVNode as s1,openBlock as H_,createElementBlock as L_}from"vue";var A_=z_({name:"Message",__name:"message",setup(t){return(e,o)=>(H_(),L_("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[s1("path",{fill:"currentColor",d:"M128 224v512a64 64 0 0 0 64 64h640a64 64 0 0 0 64-64V224zm0-64h768a64 64 0 0 1 64 64v512a128 128 0 0 1-128 128H192A128 128 0 0 1 64 736V224a64 64 0 0 1 64-64"}),s1("path",{fill:"currentColor",d:"M904 224 656.512 506.88a192 192 0 0 1-289.024 0L120 224zm-698.944 0 210.56 240.704a128 128 0 0 0 192.704 0L818.944 224z"})]))}}),_1=A_;import{defineComponent as S_}from"vue";import{createElementVNode as q_,openBlock as F_,createElementBlock as D_}from"vue";var b_=S_({name:"Mic",__name:"mic",setup(t){return(e,o)=>(F_(),D_("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[q_("path",{fill:"currentColor",d:"M480 704h160a64 64 0 0 0 64-64v-32h-96a32 32 0 0 1 0-64h96v-96h-96a32 32 0 0 1 0-64h96v-96h-96a32 32 0 0 1 0-64h96v-32a64 64 0 0 0-64-64H384a64 64 0 0 0-64 64v32h96a32 32 0 0 1 0 64h-96v96h96a32 32 0 0 1 0 64h-96v96h96a32 32 0 0 1 0 64h-96v32a64 64 0 0 0 64 64zm64 64v128h192a32 32 0 1 1 0 64H288a32 32 0 1 1 0-64h192V768h-96a128 128 0 0 1-128-128V192A128 128 0 0 1 384 64h256a128 128 0 0 1 128 128v448a128 128 0 0 1-128 128z"})]))}}),f1=b_;import{defineComponent as y_}from"vue";import{createElementVNode as P_,openBlock as R_,createElementBlock as T_}from"vue";var O_=y_({name:"Microphone",__name:"microphone",setup(t){return(e,o)=>(R_(),T_("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[P_("path",{fill:"currentColor",d:"M512 128a128 128 0 0 0-128 128v256a128 128 0 1 0 256 0V256a128 128 0 0 0-128-128m0-64a192 192 0 0 1 192 192v256a192 192 0 1 1-384 0V256A192 192 0 0 1 512 64m-32 832v-64a288 288 0 0 1-288-288v-32a32 32 0 0 1 64 0v32a224 224 0 0 0 224 224h64a224 224 0 0 0 224-224v-32a32 32 0 1 1 64 0v32a288 288 0 0 1-288 288v64h64a32 32 0 1 1 0 64H416a32 32 0 1 1 0-64z"})]))}}),i1=O_;import{defineComponent as I_}from"vue";import{createElementVNode as G_,openBlock as U_,createElementBlock as W_}from"vue";var K_=I_({name:"MilkTea",__name:"milk-tea",setup(t){return(e,o)=>(U_(),W_("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[G_("path",{fill:"currentColor",d:"M416 128V96a96 96 0 0 1 96-96h128a32 32 0 1 1 0 64H512a32 32 0 0 0-32 32v32h320a96 96 0 0 1 11.712 191.296l-39.68 581.056A64 64 0 0 1 708.224 960H315.776a64 64 0 0 1-63.872-59.648l-39.616-581.056A96 96 0 0 1 224 128zM276.48 320l39.296 576h392.448l4.8-70.784a224.064 224.064 0 0 1 30.016-439.808L747.52 320zM224 256h576a32 32 0 1 0 0-64H224a32 32 0 0 0 0 64m493.44 503.872 21.12-309.12a160 160 0 0 0-21.12 309.12"})]))}}),d1=K_;import{defineComponent as Q_}from"vue";import{createElementVNode as Z_,openBlock as j_,createElementBlock as J_}from"vue";var X_=Q_({name:"Minus",__name:"minus",setup(t){return(e,o)=>(j_(),J_("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Z_("path",{fill:"currentColor",d:"M128 544h768a32 32 0 1 0 0-64H128a32 32 0 0 0 0 64"})]))}}),u1=X_;import{defineComponent as Y_}from"vue";import{createElementVNode as B,openBlock as $_,createElementBlock as e7}from"vue";var o7=Y_({name:"Money",__name:"money",setup(t){return(e,o)=>($_(),e7("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[B("path",{fill:"currentColor",d:"M256 640v192h640V384H768v-64h150.976c14.272 0 19.456 1.472 24.64 4.288a29.06 29.06 0 0 1 12.16 12.096c2.752 5.184 4.224 10.368 4.224 24.64v493.952c0 14.272-1.472 19.456-4.288 24.64a29.06 29.06 0 0 1-12.096 12.16c-5.184 2.752-10.368 4.224-24.64 4.224H233.024c-14.272 0-19.456-1.472-24.64-4.288a29.06 29.06 0 0 1-12.16-12.096c-2.688-5.184-4.224-10.368-4.224-24.576V640z"}),B("path",{fill:"currentColor",d:"M768 192H128v448h640zm64-22.976v493.952c0 14.272-1.472 19.456-4.288 24.64a29.06 29.06 0 0 1-12.096 12.16c-5.184 2.752-10.368 4.224-24.64 4.224H105.024c-14.272 0-19.456-1.472-24.64-4.288a29.06 29.06 0 0 1-12.16-12.096C65.536 682.432 64 677.248 64 663.04V169.024c0-14.272 1.472-19.456 4.288-24.64a29.06 29.06 0 0 1 12.096-12.16C85.568 129.536 90.752 128 104.96 128h685.952c14.272 0 19.456 1.472 24.64 4.288a29.06 29.06 0 0 1 12.16 12.096c2.752 5.184 4.224 10.368 4.224 24.64z"}),B("path",{fill:"currentColor",d:"M448 576a160 160 0 1 1 0-320 160 160 0 0 1 0 320m0-64a96 96 0 1 0 0-192 96 96 0 0 0 0 192"})]))}}),h1=o7;import{defineComponent as t7}from"vue";import{createElementVNode as a7,openBlock as r7,createElementBlock as n7}from"vue";var l7=t7({name:"Monitor",__name:"monitor",setup(t){return(e,o)=>(r7(),n7("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[a7("path",{fill:"currentColor",d:"M544 768v128h192a32 32 0 1 1 0 64H288a32 32 0 1 1 0-64h192V768H192A128 128 0 0 1 64 640V256a128 128 0 0 1 128-128h640a128 128 0 0 1 128 128v384a128 128 0 0 1-128 128zM192 192a64 64 0 0 0-64 64v384a64 64 0 0 0 64 64h640a64 64 0 0 0 64-64V256a64 64 0 0 0-64-64z"})]))}}),v1=l7;import{defineComponent as m7}from"vue";import{createElementVNode as w1,openBlock as c7,createElementBlock as p7}from"vue";var s7=m7({name:"MoonNight",__name:"moon-night",setup(t){return(e,o)=>(c7(),p7("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[w1("path",{fill:"currentColor",d:"M384 512a448 448 0 0 1 215.872-383.296A384 384 0 0 0 213.76 640h188.8A448.3 448.3 0 0 1 384 512M171.136 704a448 448 0 0 1 636.992-575.296A384 384 0 0 0 499.328 704h-328.32z"}),w1("path",{fill:"currentColor",d:"M32 640h960q32 0 32 32t-32 32H32q-32 0-32-32t32-32m128 128h384a32 32 0 1 1 0 64H160a32 32 0 1 1 0-64m160 127.68 224 .256a32 32 0 0 1 32 32V928a32 32 0 0 1-32 32l-224-.384a32 32 0 0 1-32-32v-.064a32 32 0 0 1 32-32z"})]))}}),B1=s7;import{defineComponent as _7}from"vue";import{createElementVNode as f7,openBlock as i7,createElementBlock as d7}from"vue";var u7=_7({name:"Moon",__name:"moon",setup(t){return(e,o)=>(i7(),d7("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[f7("path",{fill:"currentColor",d:"M240.448 240.448a384 384 0 1 0 559.424 525.696 448 448 0 0 1-542.016-542.08 391 391 0 0 0-17.408 16.384m181.056 362.048a384 384 0 0 0 525.632 16.384A448 448 0 1 1 405.056 76.8a384 384 0 0 0 16.448 525.696"})]))}}),x1=u7;import{defineComponent as h7}from"vue";import{createElementVNode as v7,openBlock as w7,createElementBlock as B7}from"vue";var x7=h7({name:"MoreFilled",__name:"more-filled",setup(t){return(e,o)=>(w7(),B7("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[v7("path",{fill:"currentColor",d:"M176 416a112 112 0 1 1 0 224 112 112 0 0 1 0-224m336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224m336 0a112 112 0 1 1 0 224 112 112 0 0 1 0-224"})]))}}),k1=x7;import{defineComponent as k7}from"vue";import{createElementVNode as C7,openBlock as E7,createElementBlock as V7}from"vue";var g7=k7({name:"More",__name:"more",setup(t){return(e,o)=>(E7(),V7("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[C7("path",{fill:"currentColor",d:"M176 416a112 112 0 1 0 0 224 112 112 0 0 0 0-224m0 64a48 48 0 1 1 0 96 48 48 0 0 1 0-96m336-64a112 112 0 1 1 0 224 112 112 0 0 1 0-224m0 64a48 48 0 1 0 0 96 48 48 0 0 0 0-96m336-64a112 112 0 1 1 0 224 112 112 0 0 1 0-224m0 64a48 48 0 1 0 0 96 48 48 0 0 0 0-96"})]))}}),C1=g7;import{defineComponent as M7}from"vue";import{createElementVNode as N7,openBlock as z7,createElementBlock as H7}from"vue";var L7=M7({name:"MostlyCloudy",__name:"mostly-cloudy",setup(t){return(e,o)=>(z7(),H7("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[N7("path",{fill:"currentColor",d:"M737.216 357.952 704 349.824l-11.776-32a192.064 192.064 0 0 0-367.424 23.04l-8.96 39.04-39.04 8.96A192.064 192.064 0 0 0 320 768h368a207.81 207.81 0 0 0 207.808-208 208.32 208.32 0 0 0-158.592-202.048m15.168-62.208A272.32 272.32 0 0 1 959.744 560a271.81 271.81 0 0 1-271.552 272H320a256 256 0 0 1-57.536-505.536 256.128 256.128 0 0 1 489.92-30.72"})]))}}),E1=L7;import{defineComponent as A7}from"vue";import{createElementVNode as V1,openBlock as S7,createElementBlock as q7}from"vue";var F7=A7({name:"Mouse",__name:"mouse",setup(t){return(e,o)=>(S7(),q7("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[V1("path",{fill:"currentColor",d:"M438.144 256c-68.352 0-92.736 4.672-117.76 18.112q-30.144 16.128-46.272 46.272C260.672 345.408 256 369.792 256 438.144v275.712c0 68.352 4.672 92.736 18.112 117.76q16.128 30.144 46.272 46.272C345.408 891.328 369.792 896 438.144 896h147.712c68.352 0 92.736-4.672 117.76-18.112q30.144-16.128 46.272-46.272C763.328 806.592 768 782.208 768 713.856V438.144c0-68.352-4.672-92.736-18.112-117.76a110.46 110.46 0 0 0-46.272-46.272C678.592 260.672 654.208 256 585.856 256zm0-64h147.712c85.568 0 116.608 8.96 147.904 25.6 31.36 16.768 55.872 41.344 72.576 72.64C823.104 321.536 832 352.576 832 438.08v275.84c0 85.504-8.96 116.544-25.6 147.84a174.46 174.46 0 0 1-72.64 72.576C702.464 951.104 671.424 960 585.92 960H438.08c-85.504 0-116.544-8.96-147.84-25.6a174.46 174.46 0 0 1-72.64-72.704c-16.768-31.296-25.664-62.336-25.664-147.84v-275.84c0-85.504 8.96-116.544 25.6-147.84a174.46 174.46 0 0 1 72.768-72.576c31.232-16.704 62.272-25.6 147.776-25.6z"}),V1("path",{fill:"currentColor",d:"M512 320q32 0 32 32v128q0 32-32 32t-32-32V352q0-32 32-32m32-96a32 32 0 0 1-64 0v-64a32 32 0 0 0-32-32h-96a32 32 0 0 1 0-64h96a96 96 0 0 1 96 96z"})]))}}),g1=F7;import{defineComponent as D7}from"vue";import{createElementVNode as b7,openBlock as y7,createElementBlock as P7}from"vue";var R7=D7({name:"Mug",__name:"mug",setup(t){return(e,o)=>(y7(),P7("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[b7("path",{fill:"currentColor",d:"M736 800V160H160v640a64 64 0 0 0 64 64h448a64 64 0 0 0 64-64m64-544h63.552a96 96 0 0 1 96 96v224a96 96 0 0 1-96 96H800v128a128 128 0 0 1-128 128H224A128 128 0 0 1 96 800V128a32 32 0 0 1 32-32h640a32 32 0 0 1 32 32zm0 64v288h63.552a32 32 0 0 0 32-32V352a32 32 0 0 0-32-32z"})]))}}),M1=R7;import{defineComponent as T7}from"vue";import{createElementVNode as N1,openBlock as O7,createElementBlock as I7}from"vue";var G7=T7({name:"MuteNotification",__name:"mute-notification",setup(t){return(e,o)=>(O7(),I7("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[N1("path",{fill:"currentColor",d:"m241.216 832 63.616-64H768V448c0-42.368-10.24-82.304-28.48-117.504l46.912-47.232C815.36 331.392 832 387.84 832 448v320h96a32 32 0 1 1 0 64zm-90.24 0H96a32 32 0 1 1 0-64h96V448a320.13 320.13 0 0 1 256-313.6V128a64 64 0 1 1 128 0v6.4a319.55 319.55 0 0 1 171.648 97.088l-45.184 45.44A256 256 0 0 0 256 448v278.336L151.04 832zM448 896h128a64 64 0 0 1-128 0"}),N1("path",{fill:"currentColor",d:"M150.72 859.072a32 32 0 0 1-45.44-45.056l704-708.544a32 32 0 0 1 45.44 45.056z"})]))}}),z1=G7;import{defineComponent as U7}from"vue";import{createElementVNode as H1,openBlock as W7,createElementBlock as K7}from"vue";var Q7=U7({name:"Mute",__name:"mute",setup(t){return(e,o)=>(W7(),K7("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[H1("path",{fill:"currentColor",d:"m412.16 592.128-45.44 45.44A191.23 191.23 0 0 1 320 512V256a192 192 0 1 1 384 0v44.352l-64 64V256a128 128 0 1 0-256 0v256c0 30.336 10.56 58.24 28.16 80.128m51.968 38.592A128 128 0 0 0 640 512v-57.152l64-64V512a192 192 0 0 1-287.68 166.528zM314.88 779.968l46.144-46.08A223 223 0 0 0 480 768h64a224 224 0 0 0 224-224v-32a32 32 0 1 1 64 0v32a288 288 0 0 1-288 288v64h64a32 32 0 1 1 0 64H416a32 32 0 1 1 0-64h64v-64c-61.44 0-118.4-19.2-165.12-52.032M266.752 737.6A286.98 286.98 0 0 1 192 544v-32a32 32 0 0 1 64 0v32c0 56.832 21.184 108.8 56.064 148.288z"}),H1("path",{fill:"currentColor",d:"M150.72 859.072a32 32 0 0 1-45.44-45.056l704-708.544a32 32 0 0 1 45.44 45.056z"})]))}}),L1=Q7;import{defineComponent as Z7}from"vue";import{createElementVNode as j7,openBlock as J7,createElementBlock as X7}from"vue";var Y7=Z7({name:"NoSmoking",__name:"no-smoking",setup(t){return(e,o)=>(J7(),X7("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[j7("path",{fill:"currentColor",d:"M440.256 576H256v128h56.256l-64 64H224a32 32 0 0 1-32-32V544a32 32 0 0 1 32-32h280.256zm143.488 128H704V583.744L775.744 512H928a32 32 0 0 1 32 32v192a32 32 0 0 1-32 32H519.744zM768 576v128h128V576zm-29.696-207.552 45.248 45.248-497.856 497.856-45.248-45.248zM256 64h64v320h-64zM128 192h64v192h-64zM64 512h64v256H64z"})]))}}),A1=Y7;import{defineComponent as $7}from"vue";import{createElementVNode as S1,openBlock as ef,createElementBlock as of}from"vue";var tf=$7({name:"Notebook",__name:"notebook",setup(t){return(e,o)=>(ef(),of("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[S1("path",{fill:"currentColor",d:"M192 128v768h640V128zm-32-64h704a32 32 0 0 1 32 32v832a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32"}),S1("path",{fill:"currentColor",d:"M672 128h64v768h-64zM96 192h128q32 0 32 32t-32 32H96q-32 0-32-32t32-32m0 192h128q32 0 32 32t-32 32H96q-32 0-32-32t32-32m0 192h128q32 0 32 32t-32 32H96q-32 0-32-32t32-32m0 192h128q32 0 32 32t-32 32H96q-32 0-32-32t32-32"})]))}}),q1=tf;import{defineComponent as af}from"vue";import{createElementVNode as F1,openBlock as rf,createElementBlock as nf}from"vue";var lf=af({name:"Notification",__name:"notification",setup(t){return(e,o)=>(rf(),nf("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[F1("path",{fill:"currentColor",d:"M512 128v64H256a64 64 0 0 0-64 64v512a64 64 0 0 0 64 64h512a64 64 0 0 0 64-64V512h64v256a128 128 0 0 1-128 128H256a128 128 0 0 1-128-128V256a128 128 0 0 1 128-128z"}),F1("path",{fill:"currentColor",d:"M768 384a128 128 0 1 0 0-256 128 128 0 0 0 0 256m0 64a192 192 0 1 1 0-384 192 192 0 0 1 0 384"})]))}}),D1=lf;import{defineComponent as mf}from"vue";import{createElementVNode as x,openBlock as cf,createElementBlock as pf}from"vue";var sf=mf({name:"Odometer",__name:"odometer",setup(t){return(e,o)=>(cf(),pf("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[x("path",{fill:"currentColor",d:"M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768m0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896"}),x("path",{fill:"currentColor",d:"M192 512a320 320 0 1 1 640 0 32 32 0 1 1-64 0 256 256 0 1 0-512 0 32 32 0 0 1-64 0"}),x("path",{fill:"currentColor",d:"M570.432 627.84A96 96 0 1 1 509.568 608l60.992-187.776A32 32 0 1 1 631.424 440l-60.992 187.776zM502.08 734.464a32 32 0 1 0 19.84-60.928 32 32 0 0 0-19.84 60.928"})]))}}),b1=sf;import{defineComponent as _f}from"vue";import{createElementVNode as k,openBlock as ff,createElementBlock as df}from"vue";var uf=_f({name:"OfficeBuilding",__name:"office-building",setup(t){return(e,o)=>(ff(),df("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[k("path",{fill:"currentColor",d:"M192 128v704h384V128zm-32-64h448a32 32 0 0 1 32 32v768a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32"}),k("path",{fill:"currentColor",d:"M256 256h256v64H256zm0 192h256v64H256zm0 192h256v64H256zm384-128h128v64H640zm0 128h128v64H640zM64 832h896v64H64z"}),k("path",{fill:"currentColor",d:"M640 384v448h192V384zm-32-64h256a32 32 0 0 1 32 32v512a32 32 0 0 1-32 32H608a32 32 0 0 1-32-32V352a32 32 0 0 1 32-32"})]))}}),y1=uf;import{defineComponent as hf}from"vue";import{createElementVNode as P1,openBlock as vf,createElementBlock as wf}from"vue";var Bf=hf({name:"Open",__name:"open",setup(t){return(e,o)=>(vf(),wf("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[P1("path",{fill:"currentColor",d:"M329.956 257.138a254.862 254.862 0 0 0 0 509.724h364.088a254.862 254.862 0 0 0 0-509.724zm0-72.818h364.088a327.68 327.68 0 1 1 0 655.36H329.956a327.68 327.68 0 1 1 0-655.36"}),P1("path",{fill:"currentColor",d:"M694.044 621.227a109.227 109.227 0 1 0 0-218.454 109.227 109.227 0 0 0 0 218.454m0 72.817a182.044 182.044 0 1 1 0-364.088 182.044 182.044 0 0 1 0 364.088"})]))}}),R1=Bf;import{defineComponent as xf}from"vue";import{createElementVNode as kf,openBlock as Cf,createElementBlock as Ef}from"vue";var Vf=xf({name:"Operation",__name:"operation",setup(t){return(e,o)=>(Cf(),Ef("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[kf("path",{fill:"currentColor",d:"M389.44 768a96.064 96.064 0 0 1 181.12 0H896v64H570.56a96.064 96.064 0 0 1-181.12 0H128v-64zm192-288a96.064 96.064 0 0 1 181.12 0H896v64H762.56a96.064 96.064 0 0 1-181.12 0H128v-64zm-320-288a96.064 96.064 0 0 1 181.12 0H896v64H442.56a96.064 96.064 0 0 1-181.12 0H128v-64z"})]))}}),T1=Vf;import{defineComponent as gf}from"vue";import{createElementVNode as Mf,openBlock as Nf,createElementBlock as zf}from"vue";var Hf=gf({name:"Opportunity",__name:"opportunity",setup(t){return(e,o)=>(Nf(),zf("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Mf("path",{fill:"currentColor",d:"M384 960v-64h192.064v64zm448-544a350.66 350.66 0 0 1-128.32 271.424C665.344 719.04 640 763.776 640 813.504V832H320v-14.336c0-48-19.392-95.36-57.216-124.992a351.55 351.55 0 0 1-128.448-344.256c25.344-136.448 133.888-248.128 269.76-276.48A352.384 352.384 0 0 1 832 416m-544 32c0-132.288 75.904-224 192-224v-64c-154.432 0-256 122.752-256 288z"})]))}}),O1=Hf;import{defineComponent as Lf}from"vue";import{createElementVNode as Af,openBlock as Sf,createElementBlock as qf}from"vue";var Ff=Lf({name:"Orange",__name:"orange",setup(t){return(e,o)=>(Sf(),qf("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Af("path",{fill:"currentColor",d:"M544 894.72a382.34 382.34 0 0 0 215.936-89.472L577.024 622.272c-10.24 6.016-21.248 10.688-33.024 13.696v258.688zm261.248-134.784A382.34 382.34 0 0 0 894.656 544H635.968c-3.008 11.776-7.68 22.848-13.696 33.024zM894.656 480a382.34 382.34 0 0 0-89.408-215.936L622.272 446.976c6.016 10.24 10.688 21.248 13.696 33.024zm-134.72-261.248A382.34 382.34 0 0 0 544 129.344v258.688c11.776 3.008 22.848 7.68 33.024 13.696zM480 129.344a382.34 382.34 0 0 0-215.936 89.408l182.912 182.976c10.24-6.016 21.248-10.688 33.024-13.696zm-261.248 134.72A382.34 382.34 0 0 0 129.344 480h258.688c3.008-11.776 7.68-22.848 13.696-33.024zM129.344 544a382.34 382.34 0 0 0 89.408 215.936l182.976-182.912A127.2 127.2 0 0 1 388.032 544zm134.72 261.248A382.34 382.34 0 0 0 480 894.656V635.968a127.2 127.2 0 0 1-33.024-13.696zM512 960a448 448 0 1 1 0-896 448 448 0 0 1 0 896m0-384a64 64 0 1 0 0-128 64 64 0 0 0 0 128"})]))}}),I1=Ff;import{defineComponent as Df}from"vue";import{createElementVNode as bf,openBlock as yf,createElementBlock as Pf}from"vue";var Rf=Df({name:"Paperclip",__name:"paperclip",setup(t){return(e,o)=>(yf(),Pf("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[bf("path",{fill:"currentColor",d:"M602.496 240.448A192 192 0 1 1 874.048 512l-316.8 316.8A256 256 0 0 1 195.2 466.752L602.496 59.456l45.248 45.248L240.448 512A192 192 0 0 0 512 783.552l316.8-316.8a128 128 0 1 0-181.056-181.056L353.6 579.904a32 32 0 1 0 45.248 45.248l294.144-294.144 45.312 45.248L444.096 670.4a96 96 0 1 1-135.744-135.744z"})]))}}),G1=Rf;import{defineComponent as Tf}from"vue";import{createElementVNode as U1,openBlock as Of,createElementBlock as If}from"vue";var Gf=Tf({name:"PartlyCloudy",__name:"partly-cloudy",setup(t){return(e,o)=>(Of(),If("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[U1("path",{fill:"currentColor",d:"M598.4 895.872H328.192a256 256 0 0 1-34.496-510.528A352 352 0 1 1 598.4 895.872m-271.36-64h272.256a288 288 0 1 0-248.512-417.664L335.04 445.44l-34.816 3.584a192 192 0 0 0 26.88 382.848z"}),U1("path",{fill:"currentColor",d:"M139.84 501.888a256 256 0 1 1 417.856-277.12c-17.728 2.176-38.208 8.448-61.504 18.816A192 192 0 1 0 189.12 460.48a6004 6004 0 0 0-49.28 41.408"})]))}}),W1=Gf;import{defineComponent as Uf}from"vue";import{createElementVNode as Wf,openBlock as Kf,createElementBlock as Qf}from"vue";var Zf=Uf({name:"Pear",__name:"pear",setup(t){return(e,o)=>(Kf(),Qf("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Wf("path",{fill:"currentColor",d:"M542.336 258.816a443 443 0 0 0-9.024 25.088 32 32 0 1 1-60.8-20.032l1.088-3.328a162.69 162.69 0 0 0-122.048 131.392l-17.088 102.72-20.736 15.36C256.192 552.704 224 610.88 224 672c0 120.576 126.4 224 288 224s288-103.424 288-224c0-61.12-32.192-119.296-89.728-161.92l-20.736-15.424-17.088-102.72a162.69 162.69 0 0 0-130.112-133.12m-40.128-66.56c7.936-15.552 16.576-30.08 25.92-43.776 23.296-33.92 49.408-59.776 78.528-77.12a32 32 0 1 1 32.704 55.04c-20.544 12.224-40.064 31.552-58.432 58.304a317 317 0 0 0-9.792 15.104 226.69 226.69 0 0 1 164.48 181.568l12.8 77.248C819.456 511.36 864 587.392 864 672c0 159.04-157.568 288-352 288S160 831.04 160 672c0-84.608 44.608-160.64 115.584-213.376l12.8-77.248a226.624 226.624 0 0 1 213.76-189.184z"})]))}}),K1=Zf;import{defineComponent as jf}from"vue";import{createElementVNode as Jf,openBlock as Xf,createElementBlock as Yf}from"vue";var $f=jf({name:"PhoneFilled",__name:"phone-filled",setup(t){return(e,o)=>(Xf(),Yf("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Jf("path",{fill:"currentColor",d:"M199.232 125.568 90.624 379.008a32 32 0 0 0 6.784 35.2l512.384 512.384a32 32 0 0 0 35.2 6.784l253.44-108.608a32 32 0 0 0 10.048-52.032L769.6 633.92a32 32 0 0 0-36.928-5.952l-130.176 65.088-271.488-271.552 65.024-130.176a32 32 0 0 0-5.952-36.928L251.2 115.52a32 32 0 0 0-51.968 10.048"})]))}}),Q1=$f;import{defineComponent as ei}from"vue";import{createElementVNode as oi,openBlock as ti,createElementBlock as ai}from"vue";var ri=ei({name:"Phone",__name:"phone",setup(t){return(e,o)=>(ti(),ai("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[oi("path",{fill:"currentColor",d:"M79.36 432.256 591.744 944.64a32 32 0 0 0 35.2 6.784l253.44-108.544a32 32 0 0 0 9.984-52.032l-153.856-153.92a32 32 0 0 0-36.928-6.016l-69.888 34.944L358.08 394.24l35.008-69.888a32 32 0 0 0-5.952-36.928L233.152 133.568a32 32 0 0 0-52.032 10.048L72.512 397.056a32 32 0 0 0 6.784 35.2zm60.48-29.952 81.536-190.08L325.568 316.48l-24.64 49.216-20.608 41.216 32.576 32.64 271.552 271.552 32.64 32.64 41.216-20.672 49.28-24.576 104.192 104.128-190.08 81.472zM512 320v-64a256 256 0 0 1 256 256h-64a192 192 0 0 0-192-192m0-192V64a448 448 0 0 1 448 448h-64a384 384 0 0 0-384-384"})]))}}),Z1=ri;import{defineComponent as ni}from"vue";import{createElementVNode as li,openBlock as mi,createElementBlock as ci}from"vue";var pi=ni({name:"PictureFilled",__name:"picture-filled",setup(t){return(e,o)=>(mi(),ci("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[li("path",{fill:"currentColor",d:"M96 896a32 32 0 0 1-32-32V160a32 32 0 0 1 32-32h832a32 32 0 0 1 32 32v704a32 32 0 0 1-32 32zm315.52-228.48-68.928-68.928a32 32 0 0 0-45.248 0L128 768.064h778.688l-242.112-290.56a32 32 0 0 0-49.216 0L458.752 665.408a32 32 0 0 1-47.232 2.112M256 384a96 96 0 1 0 192.064-.064A96 96 0 0 0 256 384"})]))}}),j1=pi;import{defineComponent as si}from"vue";import{createElementVNode as J1,openBlock as _i,createElementBlock as fi}from"vue";var ii=si({name:"PictureRounded",__name:"picture-rounded",setup(t){return(e,o)=>(_i(),fi("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[J1("path",{fill:"currentColor",d:"M512 128a384 384 0 1 0 0 768 384 384 0 0 0 0-768m0-64a448 448 0 1 1 0 896 448 448 0 0 1 0-896"}),J1("path",{fill:"currentColor",d:"M640 288q64 0 64 64t-64 64-64-64 64-64M214.656 790.656l-45.312-45.312 185.664-185.6a96 96 0 0 1 123.712-10.24l138.24 98.688a32 32 0 0 0 39.872-2.176L906.688 422.4l42.624 47.744L699.52 693.696a96 96 0 0 1-119.808 6.592l-138.24-98.752a32 32 0 0 0-41.152 3.456l-185.664 185.6z"})]))}}),X1=ii;import{defineComponent as di}from"vue";import{createElementVNode as Y1,openBlock as ui,createElementBlock as hi}from"vue";var vi=di({name:"Picture",__name:"picture",setup(t){return(e,o)=>(ui(),hi("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Y1("path",{fill:"currentColor",d:"M160 160v704h704V160zm-32-64h768a32 32 0 0 1 32 32v768a32 32 0 0 1-32 32H128a32 32 0 0 1-32-32V128a32 32 0 0 1 32-32"}),Y1("path",{fill:"currentColor",d:"M384 288q64 0 64 64t-64 64-64-64 64-64M185.408 876.992l-50.816-38.912L350.72 556.032a96 96 0 0 1 134.592-17.856l1.856 1.472 122.88 99.136a32 32 0 0 0 44.992-4.864l216-269.888 49.92 39.936-215.808 269.824-.256.32a96 96 0 0 1-135.04 14.464l-122.88-99.072-.64-.512a32 32 0 0 0-44.8 5.952z"})]))}}),$1=vi;import{defineComponent as wi}from"vue";import{createElementVNode as e4,openBlock as Bi,createElementBlock as xi}from"vue";var ki=wi({name:"PieChart",__name:"pie-chart",setup(t){return(e,o)=>(Bi(),xi("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[e4("path",{fill:"currentColor",d:"M448 68.48v64.832A384.128 384.128 0 0 0 512 896a384.13 384.13 0 0 0 378.688-320h64.768A448.128 448.128 0 0 1 64 512 448.13 448.13 0 0 1 448 68.48"}),e4("path",{fill:"currentColor",d:"M576 97.28V448h350.72A384.064 384.064 0 0 0 576 97.28M512 64V33.152A448 448 0 0 1 990.848 512H512z"})]))}}),o4=ki;import{defineComponent as Ci}from"vue";import{createElementVNode as C,openBlock as Ei,createElementBlock as Vi}from"vue";var gi=Ci({name:"Place",__name:"place",setup(t){return(e,o)=>(Ei(),Vi("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[C("path",{fill:"currentColor",d:"M512 512a192 192 0 1 0 0-384 192 192 0 0 0 0 384m0 64a256 256 0 1 1 0-512 256 256 0 0 1 0 512"}),C("path",{fill:"currentColor",d:"M512 512a32 32 0 0 1 32 32v256a32 32 0 1 1-64 0V544a32 32 0 0 1 32-32"}),C("path",{fill:"currentColor",d:"M384 649.088v64.96C269.76 732.352 192 771.904 192 800c0 37.696 139.904 96 320 96s320-58.304 320-96c0-28.16-77.76-67.648-192-85.952v-64.96C789.12 671.04 896 730.368 896 800c0 88.32-171.904 160-384 160s-384-71.68-384-160c0-69.696 106.88-128.96 256-150.912"})]))}}),t4=gi;import{defineComponent as Mi}from"vue";import{createElementVNode as Ni,openBlock as zi,createElementBlock as Hi}from"vue";var Li=Mi({name:"Platform",__name:"platform",setup(t){return(e,o)=>(zi(),Hi("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Ni("path",{fill:"currentColor",d:"M448 832v-64h128v64h192v64H256v-64zM128 704V128h768v576z"})]))}}),a4=Li;import{defineComponent as Ai}from"vue";import{createElementVNode as Si,openBlock as qi,createElementBlock as Fi}from"vue";var Di=Ai({name:"Plus",__name:"plus",setup(t){return(e,o)=>(qi(),Fi("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Si("path",{fill:"currentColor",d:"M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64z"})]))}}),r4=Di;import{defineComponent as bi}from"vue";import{createElementVNode as yi,openBlock as Pi,createElementBlock as Ri}from"vue";var Ti=bi({name:"Pointer",__name:"pointer",setup(t){return(e,o)=>(Pi(),Ri("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[yi("path",{fill:"currentColor",d:"M511.552 128c-35.584 0-64.384 28.8-64.384 64.448v516.48L274.048 570.88a94.27 94.27 0 0 0-112.896-3.456 44.416 44.416 0 0 0-8.96 62.208L332.8 870.4A64 64 0 0 0 384 896h512V575.232a64 64 0 0 0-45.632-61.312l-205.952-61.76A96 96 0 0 1 576 360.192V192.448C576 156.8 547.2 128 511.552 128M359.04 556.8l24.128 19.2V192.448a128.448 128.448 0 1 1 256.832 0v167.744a32 32 0 0 0 22.784 30.656l206.016 61.76A128 128 0 0 1 960 575.232V896a64 64 0 0 1-64 64H384a128 128 0 0 1-102.4-51.2L101.056 668.032A108.416 108.416 0 0 1 128 512.512a158.27 158.27 0 0 1 185.984 8.32z"})]))}}),n4=Ti;import{defineComponent as Oi}from"vue";import{createElementVNode as Ii,openBlock as Gi,createElementBlock as Ui}from"vue";var Wi=Oi({name:"Position",__name:"position",setup(t){return(e,o)=>(Gi(),Ui("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Ii("path",{fill:"currentColor",d:"m249.6 417.088 319.744 43.072 39.168 310.272L845.12 178.88zm-129.024 47.168a32 32 0 0 1-7.68-61.44l777.792-311.04a32 32 0 0 1 41.6 41.6l-310.336 775.68a32 32 0 0 1-61.44-7.808L512 516.992z"})]))}}),l4=Wi;import{defineComponent as Ki}from"vue";import{createElementVNode as m4,openBlock as Qi,createElementBlock as Zi}from"vue";var ji=Ki({name:"Postcard",__name:"postcard",setup(t){return(e,o)=>(Qi(),Zi("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[m4("path",{fill:"currentColor",d:"M160 224a32 32 0 0 0-32 32v512a32 32 0 0 0 32 32h704a32 32 0 0 0 32-32V256a32 32 0 0 0-32-32zm0-64h704a96 96 0 0 1 96 96v512a96 96 0 0 1-96 96H160a96 96 0 0 1-96-96V256a96 96 0 0 1 96-96"}),m4("path",{fill:"currentColor",d:"M704 320a64 64 0 1 1 0 128 64 64 0 0 1 0-128M288 448h256q32 0 32 32t-32 32H288q-32 0-32-32t32-32m0 128h256q32 0 32 32t-32 32H288q-32 0-32-32t32-32"})]))}}),c4=ji;import{defineComponent as Ji}from"vue";import{createElementVNode as Xi,openBlock as Yi,createElementBlock as $i}from"vue";var ed=Ji({name:"Pouring",__name:"pouring",setup(t){return(e,o)=>(Yi(),$i("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Xi("path",{fill:"currentColor",d:"m739.328 291.328-35.2-6.592-12.8-33.408a192.064 192.064 0 0 0-365.952 23.232l-9.92 40.896-41.472 7.04a176.32 176.32 0 0 0-146.24 173.568c0 97.28 78.72 175.936 175.808 175.936h400a192 192 0 0 0 35.776-380.672M959.552 480a256 256 0 0 1-256 256h-400A239.81 239.81 0 0 1 63.744 496.192a240.32 240.32 0 0 1 199.488-236.8 256.128 256.128 0 0 1 487.872-30.976A256.064 256.064 0 0 1 959.552 480M224 800a32 32 0 0 1 32 32v96a32 32 0 1 1-64 0v-96a32 32 0 0 1 32-32m192 0a32 32 0 0 1 32 32v96a32 32 0 1 1-64 0v-96a32 32 0 0 1 32-32m192 0a32 32 0 0 1 32 32v96a32 32 0 1 1-64 0v-96a32 32 0 0 1 32-32m192 0a32 32 0 0 1 32 32v96a32 32 0 1 1-64 0v-96a32 32 0 0 1 32-32"})]))}}),p4=ed;import{defineComponent as od}from"vue";import{createElementVNode as r,openBlock as td,createElementBlock as ad}from"vue";var rd=od({name:"Present",__name:"present",setup(t){return(e,o)=>(td(),ad("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[r("path",{fill:"currentColor",d:"M480 896V640H192v-64h288V320H192v576zm64 0h288V320H544v256h288v64H544zM128 256h768v672a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32z"}),r("path",{fill:"currentColor",d:"M96 256h832q32 0 32 32t-32 32H96q-32 0-32-32t32-32"}),r("path",{fill:"currentColor",d:"M416 256a64 64 0 1 0 0-128 64 64 0 0 0 0 128m0 64a128 128 0 1 1 0-256 128 128 0 0 1 0 256"}),r("path",{fill:"currentColor",d:"M608 256a64 64 0 1 0 0-128 64 64 0 0 0 0 128m0 64a128 128 0 1 1 0-256 128 128 0 0 1 0 256"})]))}}),s4=rd;import{defineComponent as nd}from"vue";import{createElementVNode as _4,openBlock as ld,createElementBlock as md}from"vue";var cd=nd({name:"PriceTag",__name:"price-tag",setup(t){return(e,o)=>(ld(),md("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[_4("path",{fill:"currentColor",d:"M224 318.336V896h576V318.336L552.512 115.84a64 64 0 0 0-81.024 0zM593.024 66.304l259.2 212.096A32 32 0 0 1 864 303.168V928a32 32 0 0 1-32 32H192a32 32 0 0 1-32-32V303.168a32 32 0 0 1 11.712-24.768l259.2-212.096a128 128 0 0 1 162.112 0"}),_4("path",{fill:"currentColor",d:"M512 448a64 64 0 1 0 0-128 64 64 0 0 0 0 128m0 64a128 128 0 1 1 0-256 128 128 0 0 1 0 256"})]))}}),f4=cd;import{defineComponent as pd}from"vue";import{createElementVNode as sd,openBlock as _d,createElementBlock as fd}from"vue";var id=pd({name:"Printer",__name:"printer",setup(t){return(e,o)=>(_d(),fd("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[sd("path",{fill:"currentColor",d:"M256 768H105.024c-14.272 0-19.456-1.472-24.64-4.288a29.06 29.06 0 0 1-12.16-12.096C65.536 746.432 64 741.248 64 727.04V379.072c0-42.816 4.48-58.304 12.8-73.984 8.384-15.616 20.672-27.904 36.288-36.288 15.68-8.32 31.168-12.8 73.984-12.8H256V64h512v192h68.928c42.816 0 58.304 4.48 73.984 12.8 15.616 8.384 27.904 20.672 36.288 36.288 8.32 15.68 12.8 31.168 12.8 73.984v347.904c0 14.272-1.472 19.456-4.288 24.64a29.06 29.06 0 0 1-12.096 12.16c-5.184 2.752-10.368 4.224-24.64 4.224H768v192H256zm64-192v320h384V576zm-64 128V512h512v192h128V379.072c0-29.376-1.408-36.48-5.248-43.776a23.3 23.3 0 0 0-10.048-10.048c-7.232-3.84-14.4-5.248-43.776-5.248H187.072c-29.376 0-36.48 1.408-43.776 5.248a23.3 23.3 0 0 0-10.048 10.048c-3.84 7.232-5.248 14.4-5.248 43.776V704zm64-448h384V128H320zm-64 128h64v64h-64zm128 0h64v64h-64z"})]))}}),i4=id;import{defineComponent as dd}from"vue";import{createElementVNode as ud,openBlock as hd,createElementBlock as vd}from"vue";var wd=dd({name:"Promotion",__name:"promotion",setup(t){return(e,o)=>(hd(),vd("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[ud("path",{fill:"currentColor",d:"m64 448 832-320-128 704-446.08-243.328L832 192 242.816 545.472zm256 512V657.024L512 768z"})]))}}),d4=wd;import{defineComponent as Bd}from"vue";import{createElementVNode as E,openBlock as xd,createElementBlock as kd}from"vue";var Cd=Bd({name:"QuartzWatch",__name:"quartz-watch",setup(t){return(e,o)=>(xd(),kd("svg",{xmlns:"http://www.w3.org/2000/svg","xml:space":"preserve",viewBox:"0 0 1024 1024"},[E("path",{fill:"currentColor",d:"M422.02 602.01v-.03c-6.68-5.99-14.35-8.83-23.01-8.51q-13.005.48-22.5 10.02c-6.33 6.36-9.5 13.7-9.5 22.02s3 15.82 8.99 22.5c8.68 8.68 19.02 11.35 31.01 8s19.49-10.85 22.5-22.5.51-22.15-7.49-31.49zM384 512c0-9.35-3-17.02-8.99-23.01-6-5.99-13.66-8.99-23.01-8.99s-17.02 3-23.01 8.99c-5.99 6-8.99 13.66-8.99 23.01s3 17.02 8.99 23.01c6 5.99 13.66 8.99 23.01 8.99s17.02-3 23.01-8.99c5.99-6 8.99-13.67 8.99-23.01m6.53-82.49c11.65 3.01 22.15.51 31.49-7.49h.04c5.99-6.68 8.83-14.34 8.51-23.01s-3.66-16.16-10.02-22.5c-6.36-6.33-13.7-9.5-22.02-9.5s-15.82 3-22.5 8.99c-8.68 8.69-11.35 19.02-8 31.01q5.025 17.985 22.5 22.5m242.94 0q17.505-4.545 22.02-22.02c3.01-11.65.51-22.15-7.49-31.49h.01c-6.68-5.99-14.18-8.99-22.5-8.99s-15.66 3.16-22.02 9.5q-9.54 9.51-10.02 22.5c-.32 8.66 2.52 16.33 8.51 23.01 9.32 8.02 19.82 10.52 31.49 7.49M512 640c-9.35 0-17.02 3-23.01 8.99-5.99 6-8.99 13.66-8.99 23.01s3 17.02 8.99 23.01c6 5.99 13.67 8.99 23.01 8.99 9.35 0 17.02-3 23.01-8.99 5.99-6 8.99-13.66 8.99-23.01s-3-17.02-8.99-23.01c-6-5.99-13.66-8.99-23.01-8.99m183.01-151.01c-6-5.99-13.66-8.99-23.01-8.99s-17.02 3-23.01 8.99c-5.99 6-8.99 13.66-8.99 23.01s3 17.02 8.99 23.01c6 5.99 13.66 8.99 23.01 8.99s17.02-3 23.01-8.99c5.99-6 8.99-13.67 8.99-23.01 0-9.35-3-17.02-8.99-23.01"}),E("path",{fill:"currentColor",d:"M832 512c-2-90.67-33.17-166.17-93.5-226.5-20.43-20.42-42.6-37.49-66.5-51.23V64H352v170.26c-23.9 13.74-46.07 30.81-66.5 51.24-60.33 60.33-91.49 135.83-93.5 226.5 2 90.67 33.17 166.17 93.5 226.5 20.43 20.43 42.6 37.5 66.5 51.24V960h320V789.74c23.9-13.74 46.07-30.81 66.5-51.24 60.33-60.34 91.49-135.83 93.5-226.5M416 128h192v78.69c-29.85-9.03-61.85-13.93-96-14.69-34.15.75-66.15 5.65-96 14.68zm192 768H416v-78.68c29.85 9.03 61.85 13.93 96 14.68 34.15-.75 66.15-5.65 96-14.68zm-96-128c-72.66-2.01-132.99-27.01-180.99-75.01S258.01 584.66 256 512c2.01-72.66 27.01-132.99 75.01-180.99S439.34 258.01 512 256c72.66 2.01 132.99 27.01 180.99 75.01S765.99 439.34 768 512c-2.01 72.66-27.01 132.99-75.01 180.99S584.66 765.99 512 768"}),E("path",{fill:"currentColor",d:"M512 320c-9.35 0-17.02 3-23.01 8.99-5.99 6-8.99 13.66-8.99 23.01s3 17.02 8.99 23.01c6 5.99 13.67 8.99 23.01 8.99 9.35 0 17.02-3 23.01-8.99 5.99-6 8.99-13.66 8.99-23.01s-3-17.02-8.99-23.01c-6-5.99-13.66-8.99-23.01-8.99m112.99 273.5c-8.66-.32-16.33 2.52-23.01 8.51-7.98 9.32-10.48 19.82-7.49 31.49s10.49 19.17 22.5 22.5 22.35.66 31.01-8v.04c5.99-6.68 8.99-14.18 8.99-22.5s-3.16-15.66-9.5-22.02-13.84-9.7-22.5-10.02"})]))}}),u4=Cd;import{defineComponent as Ed}from"vue";import{createElementVNode as Vd,openBlock as gd,createElementBlock as Md}from"vue";var Nd=Ed({name:"QuestionFilled",__name:"question-filled",setup(t){return(e,o)=>(gd(),Md("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Vd("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896m23.744 191.488c-52.096 0-92.928 14.784-123.2 44.352-30.976 29.568-45.76 70.4-45.76 122.496h80.256c0-29.568 5.632-52.8 17.6-68.992 13.376-19.712 35.2-28.864 66.176-28.864 23.936 0 42.944 6.336 56.32 19.712 12.672 13.376 19.712 31.68 19.712 54.912 0 17.6-6.336 34.496-19.008 49.984l-8.448 9.856c-45.76 40.832-73.216 70.4-82.368 89.408-9.856 19.008-14.08 42.24-14.08 68.992v9.856h80.96v-9.856c0-16.896 3.52-31.68 10.56-45.76 6.336-12.672 15.488-24.64 28.16-35.2 33.792-29.568 54.208-48.576 60.544-55.616 16.896-22.528 26.048-51.392 26.048-86.592q0-64.416-42.24-101.376c-28.16-25.344-65.472-37.312-111.232-37.312m-12.672 406.208a54.27 54.27 0 0 0-38.72 14.784 49.4 49.4 0 0 0-15.488 38.016c0 15.488 4.928 28.16 15.488 38.016A54.85 54.85 0 0 0 523.072 768c15.488 0 28.16-4.928 38.72-14.784a51.52 51.52 0 0 0 16.192-38.72 51.97 51.97 0 0 0-15.488-38.016 55.94 55.94 0 0 0-39.424-14.784"})]))}}),h4=Nd;import{defineComponent as zd}from"vue";import{createElementVNode as Hd,openBlock as Ld,createElementBlock as Ad}from"vue";var Sd=zd({name:"Rank",__name:"rank",setup(t){return(e,o)=>(Ld(),Ad("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Hd("path",{fill:"currentColor",d:"m186.496 544 41.408 41.344a32 32 0 1 1-45.248 45.312l-96-96a32 32 0 0 1 0-45.312l96-96a32 32 0 1 1 45.248 45.312L186.496 480h290.816V186.432l-41.472 41.472a32 32 0 1 1-45.248-45.184l96-96.128a32 32 0 0 1 45.312 0l96 96.064a32 32 0 0 1-45.248 45.184l-41.344-41.28V480H832l-41.344-41.344a32 32 0 0 1 45.248-45.312l96 96a32 32 0 0 1 0 45.312l-96 96a32 32 0 0 1-45.248-45.312L832 544H541.312v293.44l41.344-41.28a32 32 0 1 1 45.248 45.248l-96 96a32 32 0 0 1-45.312 0l-96-96a32 32 0 1 1 45.312-45.248l41.408 41.408V544z"})]))}}),v4=Sd;import{defineComponent as qd}from"vue";import{createElementVNode as w4,openBlock as Fd,createElementBlock as Dd}from"vue";var bd=qd({name:"ReadingLamp",__name:"reading-lamp",setup(t){return(e,o)=>(Fd(),Dd("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[w4("path",{fill:"currentColor",d:"M352 896h320q32 0 32 32t-32 32H352q-32 0-32-32t32-32m-44.672-768-99.52 448h608.384l-99.52-448zm-25.6-64h460.608a32 32 0 0 1 31.232 25.088l113.792 512A32 32 0 0 1 856.128 640H167.872a32 32 0 0 1-31.232-38.912l113.792-512A32 32 0 0 1 281.664 64z"}),w4("path",{fill:"currentColor",d:"M672 576q32 0 32 32v128q0 32-32 32t-32-32V608q0-32 32-32m-192-.064h64V960h-64z"})]))}}),B4=bd;import{defineComponent as yd}from"vue";import{createElementVNode as x4,openBlock as Pd,createElementBlock as Rd}from"vue";var Td=yd({name:"Reading",__name:"reading",setup(t){return(e,o)=>(Pd(),Rd("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[x4("path",{fill:"currentColor",d:"m512 863.36 384-54.848v-638.72L525.568 222.72a96 96 0 0 1-27.136 0L128 169.792v638.72zM137.024 106.432l370.432 52.928a32 32 0 0 0 9.088 0l370.432-52.928A64 64 0 0 1 960 169.792v638.72a64 64 0 0 1-54.976 63.36l-388.48 55.488a32 32 0 0 1-9.088 0l-388.48-55.488A64 64 0 0 1 64 808.512v-638.72a64 64 0 0 1 73.024-63.36"}),x4("path",{fill:"currentColor",d:"M480 192h64v704h-64z"})]))}}),k4=Td;import{defineComponent as Od}from"vue";import{createElementVNode as Id,openBlock as Gd,createElementBlock as Ud}from"vue";var Wd=Od({name:"RefreshLeft",__name:"refresh-left",setup(t){return(e,o)=>(Gd(),Ud("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Id("path",{fill:"currentColor",d:"M289.088 296.704h92.992a32 32 0 0 1 0 64H232.96a32 32 0 0 1-32-32V179.712a32 32 0 0 1 64 0v50.56a384 384 0 0 1 643.84 282.88 384 384 0 0 1-383.936 384 384 384 0 0 1-384-384h64a320 320 0 1 0 640 0 320 320 0 0 0-555.712-216.448z"})]))}}),C4=Wd;import{defineComponent as Kd}from"vue";import{createElementVNode as Qd,openBlock as Zd,createElementBlock as jd}from"vue";var Jd=Kd({name:"RefreshRight",__name:"refresh-right",setup(t){return(e,o)=>(Zd(),jd("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Qd("path",{fill:"currentColor",d:"M784.512 230.272v-50.56a32 32 0 1 1 64 0v149.056a32 32 0 0 1-32 32H667.52a32 32 0 1 1 0-64h92.992A320 320 0 1 0 524.8 833.152a320 320 0 0 0 320-320h64a384 384 0 0 1-384 384 384 384 0 0 1-384-384 384 384 0 0 1 643.712-282.88"})]))}}),E4=Jd;import{defineComponent as Xd}from"vue";import{createElementVNode as Yd,openBlock as $d,createElementBlock as eu}from"vue";var ou=Xd({name:"Refresh",__name:"refresh",setup(t){return(e,o)=>($d(),eu("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Yd("path",{fill:"currentColor",d:"M771.776 794.88A384 384 0 0 1 128 512h64a320 320 0 0 0 555.712 216.448H654.72a32 32 0 1 1 0-64h149.056a32 32 0 0 1 32 32v148.928a32 32 0 1 1-64 0v-50.56zM276.288 295.616h92.992a32 32 0 0 1 0 64H220.16a32 32 0 0 1-32-32V178.56a32 32 0 0 1 64 0v50.56A384 384 0 0 1 896.128 512h-64a320 320 0 0 0-555.776-216.384z"})]))}}),V4=ou;import{defineComponent as tu}from"vue";import{createElementVNode as au,openBlock as ru,createElementBlock as nu}from"vue";var lu=tu({name:"Refrigerator",__name:"refrigerator",setup(t){return(e,o)=>(ru(),nu("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[au("path",{fill:"currentColor",d:"M256 448h512V160a32 32 0 0 0-32-32H288a32 32 0 0 0-32 32zm0 64v352a32 32 0 0 0 32 32h448a32 32 0 0 0 32-32V512zm32-448h448a96 96 0 0 1 96 96v704a96 96 0 0 1-96 96H288a96 96 0 0 1-96-96V160a96 96 0 0 1 96-96m32 224h64v96h-64zm0 288h64v96h-64z"})]))}}),g4=lu;import{defineComponent as mu}from"vue";import{createElementVNode as cu,openBlock as pu,createElementBlock as su}from"vue";var _u=mu({name:"RemoveFilled",__name:"remove-filled",setup(t){return(e,o)=>(pu(),su("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[cu("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896M288 512a38.4 38.4 0 0 0 38.4 38.4h371.2a38.4 38.4 0 0 0 0-76.8H326.4A38.4 38.4 0 0 0 288 512"})]))}}),M4=_u;import{defineComponent as fu}from"vue";import{createElementVNode as N4,openBlock as iu,createElementBlock as du}from"vue";var uu=fu({name:"Remove",__name:"remove",setup(t){return(e,o)=>(iu(),du("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[N4("path",{fill:"currentColor",d:"M352 480h320a32 32 0 1 1 0 64H352a32 32 0 0 1 0-64"}),N4("path",{fill:"currentColor",d:"M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768m0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896"})]))}}),z4=uu;import{defineComponent as hu}from"vue";import{createElementVNode as vu,openBlock as wu,createElementBlock as Bu}from"vue";var xu=hu({name:"Right",__name:"right",setup(t){return(e,o)=>(wu(),Bu("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[vu("path",{fill:"currentColor",d:"M754.752 480H160a32 32 0 1 0 0 64h594.752L521.344 777.344a32 32 0 0 0 45.312 45.312l288-288a32 32 0 0 0 0-45.312l-288-288a32 32 0 1 0-45.312 45.312z"})]))}}),H4=xu;import{defineComponent as ku}from"vue";import{createElementVNode as Cu,openBlock as Eu,createElementBlock as Vu}from"vue";var gu=ku({name:"ScaleToOriginal",__name:"scale-to-original",setup(t){return(e,o)=>(Eu(),Vu("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Cu("path",{fill:"currentColor",d:"M813.176 180.706a60.235 60.235 0 0 1 60.236 60.235v481.883a60.235 60.235 0 0 1-60.236 60.235H210.824a60.235 60.235 0 0 1-60.236-60.235V240.94a60.235 60.235 0 0 1 60.236-60.235h602.352zm0-60.235H210.824A120.47 120.47 0 0 0 90.353 240.94v481.883a120.47 120.47 0 0 0 120.47 120.47h602.353a120.47 120.47 0 0 0 120.471-120.47V240.94a120.47 120.47 0 0 0-120.47-120.47zm-120.47 180.705a30.12 30.12 0 0 0-30.118 30.118v301.177a30.118 30.118 0 0 0 60.236 0V331.294a30.12 30.12 0 0 0-30.118-30.118m-361.412 0a30.12 30.12 0 0 0-30.118 30.118v301.177a30.118 30.118 0 1 0 60.236 0V331.294a30.12 30.12 0 0 0-30.118-30.118M512 361.412a30.12 30.12 0 0 0-30.118 30.117v30.118a30.118 30.118 0 0 0 60.236 0V391.53A30.12 30.12 0 0 0 512 361.412M512 512a30.12 30.12 0 0 0-30.118 30.118v30.117a30.118 30.118 0 0 0 60.236 0v-30.117A30.12 30.12 0 0 0 512 512"})]))}}),L4=gu;import{defineComponent as Mu}from"vue";import{createElementVNode as V,openBlock as Nu,createElementBlock as zu}from"vue";var Hu=Mu({name:"School",__name:"school",setup(t){return(e,o)=>(Nu(),zu("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[V("path",{fill:"currentColor",d:"M224 128v704h576V128zm-32-64h640a32 32 0 0 1 32 32v768a32 32 0 0 1-32 32H192a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32"}),V("path",{fill:"currentColor",d:"M64 832h896v64H64zm256-640h128v96H320z"}),V("path",{fill:"currentColor",d:"M384 832h256v-64a128 128 0 1 0-256 0zm128-256a192 192 0 0 1 192 192v128H320V768a192 192 0 0 1 192-192M320 384h128v96H320zm256-192h128v96H576zm0 192h128v96H576z"})]))}}),A4=Hu;import{defineComponent as Lu}from"vue";import{createElementVNode as Au,openBlock as Su,createElementBlock as qu}from"vue";var Fu=Lu({name:"Scissor",__name:"scissor",setup(t){return(e,o)=>(Su(),qu("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Au("path",{fill:"currentColor",d:"m512.064 578.368-106.88 152.768a160 160 0 1 1-23.36-78.208L472.96 522.56 196.864 128.256a32 32 0 1 1 52.48-36.736l393.024 561.344a160 160 0 1 1-23.36 78.208l-106.88-152.704zm54.4-189.248 208.384-297.6a32 32 0 0 1 52.48 36.736l-221.76 316.672-39.04-55.808zm-376.32 425.856a96 96 0 1 0 110.144-157.248 96 96 0 0 0-110.08 157.248zm643.84 0a96 96 0 1 0-110.08-157.248 96 96 0 0 0 110.08 157.248"})]))}}),S4=Fu;import{defineComponent as Du}from"vue";import{createElementVNode as bu,openBlock as yu,createElementBlock as Pu}from"vue";var Ru=Du({name:"Search",__name:"search",setup(t){return(e,o)=>(yu(),Pu("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[bu("path",{fill:"currentColor",d:"m795.904 750.72 124.992 124.928a32 32 0 0 1-45.248 45.248L750.656 795.904a416 416 0 1 1 45.248-45.248zM480 832a352 352 0 1 0 0-704 352 352 0 0 0 0 704"})]))}}),q4=Ru;import{defineComponent as Tu}from"vue";import{createElementVNode as Ou,openBlock as Iu,createElementBlock as Gu}from"vue";var Uu=Tu({name:"Select",__name:"select",setup(t){return(e,o)=>(Iu(),Gu("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Ou("path",{fill:"currentColor",d:"M77.248 415.04a64 64 0 0 1 90.496 0l226.304 226.304L846.528 188.8a64 64 0 1 1 90.56 90.496l-543.04 543.04-316.8-316.8a64 64 0 0 1 0-90.496"})]))}}),F4=Uu;import{defineComponent as Wu}from"vue";import{createElementVNode as Ku,openBlock as Qu,createElementBlock as Zu}from"vue";var ju=Wu({name:"Sell",__name:"sell",setup(t){return(e,o)=>(Qu(),Zu("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Ku("path",{fill:"currentColor",d:"M704 288h131.072a32 32 0 0 1 31.808 28.8L886.4 512h-64.384l-16-160H704v96a32 32 0 1 1-64 0v-96H384v96a32 32 0 0 1-64 0v-96H217.92l-51.2 512H512v64H131.328a32 32 0 0 1-31.808-35.2l57.6-576a32 32 0 0 1 31.808-28.8H320v-22.336C320 154.688 405.504 64 512 64s192 90.688 192 201.664v22.4zm-64 0v-22.336C640 189.248 582.272 128 512 128s-128 61.248-128 137.664v22.4h256zm201.408 483.84L768 698.496V928a32 32 0 1 1-64 0V698.496l-73.344 73.344a32 32 0 1 1-45.248-45.248l128-128a32 32 0 0 1 45.248 0l128 128a32 32 0 1 1-45.248 45.248"})]))}}),D4=ju;import{defineComponent as Ju}from"vue";import{createElementVNode as Xu,openBlock as Yu,createElementBlock as $u}from"vue";var eh=Ju({name:"SemiSelect",__name:"semi-select",setup(t){return(e,o)=>(Yu(),$u("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Xu("path",{fill:"currentColor",d:"M128 448h768q64 0 64 64t-64 64H128q-64 0-64-64t64-64"})]))}}),b4=eh;import{defineComponent as oh}from"vue";import{createElementVNode as th,openBlock as ah,createElementBlock as rh}from"vue";var nh=oh({name:"Service",__name:"service",setup(t){return(e,o)=>(ah(),rh("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[th("path",{fill:"currentColor",d:"M864 409.6a192 192 0 0 1-37.888 349.44A256.064 256.064 0 0 1 576 960h-96a32 32 0 1 1 0-64h96a192.06 192.06 0 0 0 181.12-128H736a32 32 0 0 1-32-32V416a32 32 0 0 1 32-32h32c10.368 0 20.544.832 30.528 2.432a288 288 0 0 0-573.056 0A193 193 0 0 1 256 384h32a32 32 0 0 1 32 32v320a32 32 0 0 1-32 32h-32a192 192 0 0 1-96-358.4 352 352 0 0 1 704 0M256 448a128 128 0 1 0 0 256zm640 128a128 128 0 0 0-128-128v256a128 128 0 0 0 128-128"})]))}}),y4=nh;import{defineComponent as lh}from"vue";import{createElementVNode as n,openBlock as mh,createElementBlock as ch}from"vue";var ph=lh({name:"SetUp",__name:"set-up",setup(t){return(e,o)=>(mh(),ch("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[n("path",{fill:"currentColor",d:"M224 160a64 64 0 0 0-64 64v576a64 64 0 0 0 64 64h576a64 64 0 0 0 64-64V224a64 64 0 0 0-64-64zm0-64h576a128 128 0 0 1 128 128v576a128 128 0 0 1-128 128H224A128 128 0 0 1 96 800V224A128 128 0 0 1 224 96"}),n("path",{fill:"currentColor",d:"M384 416a64 64 0 1 0 0-128 64 64 0 0 0 0 128m0 64a128 128 0 1 1 0-256 128 128 0 0 1 0 256"}),n("path",{fill:"currentColor",d:"M480 320h256q32 0 32 32t-32 32H480q-32 0-32-32t32-32m160 416a64 64 0 1 0 0-128 64 64 0 0 0 0 128m0 64a128 128 0 1 1 0-256 128 128 0 0 1 0 256"}),n("path",{fill:"currentColor",d:"M288 640h256q32 0 32 32t-32 32H288q-32 0-32-32t32-32"})]))}}),P4=ph;import{defineComponent as sh}from"vue";import{createElementVNode as _h,openBlock as fh,createElementBlock as ih}from"vue";var dh=sh({name:"Setting",__name:"setting",setup(t){return(e,o)=>(fh(),ih("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[_h("path",{fill:"currentColor",d:"M600.704 64a32 32 0 0 1 30.464 22.208l35.2 109.376c14.784 7.232 28.928 15.36 42.432 24.512l112.384-24.192a32 32 0 0 1 34.432 15.36L944.32 364.8a32 32 0 0 1-4.032 37.504l-77.12 85.12a357 357 0 0 1 0 49.024l77.12 85.248a32 32 0 0 1 4.032 37.504l-88.704 153.6a32 32 0 0 1-34.432 15.296L708.8 803.904c-13.44 9.088-27.648 17.28-42.368 24.512l-35.264 109.376A32 32 0 0 1 600.704 960H423.296a32 32 0 0 1-30.464-22.208L357.696 828.48a352 352 0 0 1-42.56-24.64l-112.32 24.256a32 32 0 0 1-34.432-15.36L79.68 659.2a32 32 0 0 1 4.032-37.504l77.12-85.248a357 357 0 0 1 0-48.896l-77.12-85.248A32 32 0 0 1 79.68 364.8l88.704-153.6a32 32 0 0 1 34.432-15.296l112.32 24.256c13.568-9.152 27.776-17.408 42.56-24.64l35.2-109.312A32 32 0 0 1 423.232 64H600.64zm-23.424 64H446.72l-36.352 113.088-24.512 11.968a294 294 0 0 0-34.816 20.096l-22.656 15.36-116.224-25.088-65.28 113.152 79.68 88.192-1.92 27.136a293 293 0 0 0 0 40.192l1.92 27.136-79.808 88.192 65.344 113.152 116.224-25.024 22.656 15.296a294 294 0 0 0 34.816 20.096l24.512 11.968L446.72 896h130.688l36.48-113.152 24.448-11.904a288 288 0 0 0 34.752-20.096l22.592-15.296 116.288 25.024 65.28-113.152-79.744-88.192 1.92-27.136a293 293 0 0 0 0-40.256l-1.92-27.136 79.808-88.128-65.344-113.152-116.288 24.96-22.592-15.232a288 288 0 0 0-34.752-20.096l-24.448-11.904L577.344 128zM512 320a192 192 0 1 1 0 384 192 192 0 0 1 0-384m0 64a128 128 0 1 0 0 256 128 128 0 0 0 0-256"})]))}}),R4=dh;import{defineComponent as uh}from"vue";import{createElementVNode as hh,openBlock as vh,createElementBlock as wh}from"vue";var Bh=uh({name:"Share",__name:"share",setup(t){return(e,o)=>(vh(),wh("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[hh("path",{fill:"currentColor",d:"m679.872 348.8-301.76 188.608a127.8 127.8 0 0 1 5.12 52.16l279.936 104.96a128 128 0 1 1-22.464 59.904l-279.872-104.96a128 128 0 1 1-16.64-166.272l301.696-188.608a128 128 0 1 1 33.92 54.272z"})]))}}),T4=Bh;import{defineComponent as xh}from"vue";import{createElementVNode as kh,openBlock as Ch,createElementBlock as Eh}from"vue";var Vh=xh({name:"Ship",__name:"ship",setup(t){return(e,o)=>(Ch(),Eh("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[kh("path",{fill:"currentColor",d:"M512 386.88V448h405.568a32 32 0 0 1 30.72 40.768l-76.48 267.968A192 192 0 0 1 687.168 896H336.832a192 192 0 0 1-184.64-139.264L75.648 488.768A32 32 0 0 1 106.368 448H448V117.888a32 32 0 0 1 47.36-28.096l13.888 7.616L512 96v2.88l231.68 126.4a32 32 0 0 1-2.048 57.216zm0-70.272 144.768-65.792L512 171.84zM512 512H148.864l18.24 64H856.96l18.24-64zM185.408 640l28.352 99.2A128 128 0 0 0 336.832 832h350.336a128 128 0 0 0 123.072-92.8l28.352-99.2z"})]))}}),O4=Vh;import{defineComponent as gh}from"vue";import{createElementVNode as Mh,openBlock as Nh,createElementBlock as zh}from"vue";var Hh=gh({name:"Shop",__name:"shop",setup(t){return(e,o)=>(Nh(),zh("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Mh("path",{fill:"currentColor",d:"M704 704h64v192H256V704h64v64h384zm188.544-152.192C894.528 559.616 896 567.616 896 576a96 96 0 1 1-192 0 96 96 0 1 1-192 0 96 96 0 1 1-192 0 96 96 0 1 1-192 0c0-8.384 1.408-16.384 3.392-24.192L192 128h640z"})]))}}),I4=Hh;import{defineComponent as Lh}from"vue";import{createElementVNode as G4,openBlock as Ah,createElementBlock as Sh}from"vue";var qh=Lh({name:"ShoppingBag",__name:"shopping-bag",setup(t){return(e,o)=>(Ah(),Sh("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[G4("path",{fill:"currentColor",d:"M704 320v96a32 32 0 0 1-32 32h-32V320H384v128h-32a32 32 0 0 1-32-32v-96H192v576h640V320zm-384-64a192 192 0 1 1 384 0h160a32 32 0 0 1 32 32v640a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V288a32 32 0 0 1 32-32zm64 0h256a128 128 0 1 0-256 0"}),G4("path",{fill:"currentColor",d:"M192 704h640v64H192z"})]))}}),U4=qh;import{defineComponent as Fh}from"vue";import{createElementVNode as W4,openBlock as Dh,createElementBlock as bh}from"vue";var yh=Fh({name:"ShoppingCartFull",__name:"shopping-cart-full",setup(t){return(e,o)=>(Dh(),bh("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[W4("path",{fill:"currentColor",d:"M432 928a48 48 0 1 1 0-96 48 48 0 0 1 0 96m320 0a48 48 0 1 1 0-96 48 48 0 0 1 0 96M96 128a32 32 0 0 1 0-64h160a32 32 0 0 1 31.36 25.728L320.64 256H928a32 32 0 0 1 31.296 38.72l-96 448A32 32 0 0 1 832 768H384a32 32 0 0 1-31.36-25.728L229.76 128zm314.24 576h395.904l82.304-384H333.44z"}),W4("path",{fill:"currentColor",d:"M699.648 256 608 145.984 516.352 256zm-140.8-151.04a64 64 0 0 1 98.304 0L836.352 320H379.648z"})]))}}),K4=yh;import{defineComponent as Ph}from"vue";import{createElementVNode as Rh,openBlock as Th,createElementBlock as Oh}from"vue";var Ih=Ph({name:"ShoppingCart",__name:"shopping-cart",setup(t){return(e,o)=>(Th(),Oh("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Rh("path",{fill:"currentColor",d:"M432 928a48 48 0 1 1 0-96 48 48 0 0 1 0 96m320 0a48 48 0 1 1 0-96 48 48 0 0 1 0 96M96 128a32 32 0 0 1 0-64h160a32 32 0 0 1 31.36 25.728L320.64 256H928a32 32 0 0 1 31.296 38.72l-96 448A32 32 0 0 1 832 768H384a32 32 0 0 1-31.36-25.728L229.76 128zm314.24 576h395.904l82.304-384H333.44z"})]))}}),Q4=Ih;import{defineComponent as Gh}from"vue";import{createElementVNode as Uh,openBlock as Wh,createElementBlock as Kh}from"vue";var Qh=Gh({name:"ShoppingTrolley",__name:"shopping-trolley",setup(t){return(e,o)=>(Wh(),Kh("svg",{xmlns:"http://www.w3.org/2000/svg","xml:space":"preserve",viewBox:"0 0 1024 1024"},[Uh("path",{fill:"currentColor",d:"M368 833c-13.3 0-24.5 4.5-33.5 13.5S321 866.7 321 880s4.5 24.5 13.5 33.5 20.2 13.8 33.5 14.5c13.3-.7 24.5-5.5 33.5-14.5S415 893.3 415 880s-4.5-24.5-13.5-33.5S381.3 833 368 833m439-193c7.4 0 13.8-2.2 19.5-6.5S836 623.3 838 616l112-448c2-10-.2-19.2-6.5-27.5S929 128 919 128H96c-9.3 0-17 3-23 9s-9 13.7-9 23 3 17 9 23 13.7 9 23 9h96v576h672c9.3 0 17-3 23-9s9-13.7 9-23-3-17-9-23-13.7-9-23-9H256v-64zM256 192h622l-96 384H256zm432 641c-13.3 0-24.5 4.5-33.5 13.5S641 866.7 641 880s4.5 24.5 13.5 33.5 20.2 13.8 33.5 14.5c13.3-.7 24.5-5.5 33.5-14.5S735 893.3 735 880s-4.5-24.5-13.5-33.5S701.3 833 688 833"})]))}}),Z4=Qh;import{defineComponent as Zh}from"vue";import{createElementVNode as j4,openBlock as jh,createElementBlock as Jh}from"vue";var Xh=Zh({name:"Smoking",__name:"smoking",setup(t){return(e,o)=>(jh(),Jh("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[j4("path",{fill:"currentColor",d:"M256 576v128h640V576zm-32-64h704a32 32 0 0 1 32 32v192a32 32 0 0 1-32 32H224a32 32 0 0 1-32-32V544a32 32 0 0 1 32-32"}),j4("path",{fill:"currentColor",d:"M704 576h64v128h-64zM256 64h64v320h-64zM128 192h64v192h-64zM64 512h64v256H64z"})]))}}),J4=Xh;import{defineComponent as Yh}from"vue";import{createElementVNode as $h,openBlock as ev,createElementBlock as ov}from"vue";var tv=Yh({name:"Soccer",__name:"soccer",setup(t){return(e,o)=>(ev(),ov("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[$h("path",{fill:"currentColor",d:"M418.496 871.04 152.256 604.8c-16.512 94.016-2.368 178.624 42.944 224 44.928 44.928 129.344 58.752 223.296 42.24m72.32-18.176a573.06 573.06 0 0 0 224.832-137.216 573.1 573.1 0 0 0 137.216-224.832L533.888 171.84a578.56 578.56 0 0 0-227.52 138.496A567.7 567.7 0 0 0 170.432 532.48zM871.04 418.496c16.512-93.952 2.688-178.368-42.24-223.296-44.544-44.544-128.704-58.048-222.592-41.536zM149.952 874.048c-112.96-112.96-88.832-408.96 111.168-608.96C461.056 65.152 760.96 36.928 874.048 149.952c113.024 113.024 86.784 411.008-113.152 610.944s-497.92 226.112-610.944 113.152m452.544-497.792 22.656-22.656a32 32 0 0 1 45.248 45.248l-22.656 22.656 45.248 45.248A32 32 0 1 1 647.744 512l-45.248-45.248L557.248 512l45.248 45.248a32 32 0 1 1-45.248 45.248L512 557.248l-45.248 45.248L512 647.744a32 32 0 1 1-45.248 45.248l-45.248-45.248-22.656 22.656a32 32 0 1 1-45.248-45.248l22.656-22.656-45.248-45.248A32 32 0 1 1 376.256 512l45.248 45.248L466.752 512l-45.248-45.248a32 32 0 1 1 45.248-45.248L512 466.752l45.248-45.248L512 376.256a32 32 0 0 1 45.248-45.248z"})]))}}),X4=tv;import{defineComponent as av}from"vue";import{createElementVNode as rv,openBlock as nv,createElementBlock as lv}from"vue";var mv=av({name:"SoldOut",__name:"sold-out",setup(t){return(e,o)=>(nv(),lv("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[rv("path",{fill:"currentColor",d:"M704 288h131.072a32 32 0 0 1 31.808 28.8L886.4 512h-64.384l-16-160H704v96a32 32 0 1 1-64 0v-96H384v96a32 32 0 0 1-64 0v-96H217.92l-51.2 512H512v64H131.328a32 32 0 0 1-31.808-35.2l57.6-576a32 32 0 0 1 31.808-28.8H320v-22.336C320 154.688 405.504 64 512 64s192 90.688 192 201.664v22.4zm-64 0v-22.336C640 189.248 582.272 128 512 128s-128 61.248-128 137.664v22.4h256zm201.408 476.16a32 32 0 1 1 45.248 45.184l-128 128a32 32 0 0 1-45.248 0l-128-128a32 32 0 1 1 45.248-45.248L704 837.504V608a32 32 0 1 1 64 0v229.504l73.408-73.408z"})]))}}),Y4=mv;import{defineComponent as cv}from"vue";import{createElementVNode as pv,openBlock as sv,createElementBlock as _v}from"vue";var fv=cv({name:"SortDown",__name:"sort-down",setup(t){return(e,o)=>(sv(),_v("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[pv("path",{fill:"currentColor",d:"M576 96v709.568L333.312 562.816A32 32 0 1 0 288 608l297.408 297.344A32 32 0 0 0 640 882.688V96a32 32 0 0 0-64 0"})]))}}),$4=fv;import{defineComponent as iv}from"vue";import{createElementVNode as dv,openBlock as uv,createElementBlock as hv}from"vue";var vv=iv({name:"SortUp",__name:"sort-up",setup(t){return(e,o)=>(uv(),hv("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[dv("path",{fill:"currentColor",d:"M384 141.248V928a32 32 0 1 0 64 0V218.56l242.688 242.688A32 32 0 1 0 736 416L438.592 118.656A32 32 0 0 0 384 141.248"})]))}}),eo=vv;import{defineComponent as wv}from"vue";import{createElementVNode as Bv,openBlock as xv,createElementBlock as kv}from"vue";var Cv=wv({name:"Sort",__name:"sort",setup(t){return(e,o)=>(xv(),kv("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Bv("path",{fill:"currentColor",d:"M384 96a32 32 0 0 1 64 0v786.752a32 32 0 0 1-54.592 22.656L95.936 608a32 32 0 0 1 0-45.312h.128a32 32 0 0 1 45.184 0L384 805.632zm192 45.248a32 32 0 0 1 54.592-22.592L928.064 416a32 32 0 0 1 0 45.312h-.128a32 32 0 0 1-45.184 0L640 218.496V928a32 32 0 1 1-64 0z"})]))}}),oo=Cv;import{defineComponent as Ev}from"vue";import{createElementVNode as Vv,openBlock as gv,createElementBlock as Mv}from"vue";var Nv=Ev({name:"Stamp",__name:"stamp",setup(t){return(e,o)=>(gv(),Mv("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Vv("path",{fill:"currentColor",d:"M624 475.968V640h144a128 128 0 0 1 128 128H128a128 128 0 0 1 128-128h144V475.968a192 192 0 1 1 224 0M128 896v-64h768v64z"})]))}}),to=Nv;import{defineComponent as zv}from"vue";import{createElementVNode as Hv,openBlock as Lv,createElementBlock as Av}from"vue";var Sv=zv({name:"StarFilled",__name:"star-filled",setup(t){return(e,o)=>(Lv(),Av("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Hv("path",{fill:"currentColor",d:"M313.6 924.48a70.4 70.4 0 0 1-74.152-5.365 70.4 70.4 0 0 1-27.992-68.875l37.888-220.928L88.96 472.96a70.4 70.4 0 0 1 3.788-104.225A70.4 70.4 0 0 1 128 352.896l221.76-32.256 99.2-200.96a70.4 70.4 0 0 1 100.246-28.595 70.4 70.4 0 0 1 25.962 28.595l99.2 200.96 221.824 32.256a70.4 70.4 0 0 1 39.04 120.064L774.72 629.376l37.888 220.928a70.4 70.4 0 0 1-102.144 74.24L512 820.096l-198.4 104.32z"})]))}}),ao=Sv;import{defineComponent as qv}from"vue";import{createElementVNode as Fv,openBlock as Dv,createElementBlock as bv}from"vue";var yv=qv({name:"Star",__name:"star",setup(t){return(e,o)=>(Dv(),bv("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Fv("path",{fill:"currentColor",d:"m512 747.84 228.16 119.936a6.4 6.4 0 0 0 9.28-6.72l-43.52-254.08 184.512-179.904a6.4 6.4 0 0 0-3.52-10.88l-255.104-37.12L517.76 147.904a6.4 6.4 0 0 0-11.52 0L392.192 379.072l-255.104 37.12a6.4 6.4 0 0 0-3.52 10.88L318.08 606.976l-43.584 254.08a6.4 6.4 0 0 0 9.28 6.72zM313.6 924.48a70.4 70.4 0 0 1-102.144-74.24l37.888-220.928L88.96 472.96A70.4 70.4 0 0 1 128 352.896l221.76-32.256 99.2-200.96a70.4 70.4 0 0 1 126.208 0l99.2 200.96 221.824 32.256a70.4 70.4 0 0 1 39.04 120.064L774.72 629.376l37.888 220.928a70.4 70.4 0 0 1-102.144 74.24L512 820.096l-198.4 104.32z"})]))}}),ro=yv;import{defineComponent as Pv}from"vue";import{createElementVNode as no,openBlock as Rv,createElementBlock as Tv}from"vue";var Ov=Pv({name:"Stopwatch",__name:"stopwatch",setup(t){return(e,o)=>(Rv(),Tv("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[no("path",{fill:"currentColor",d:"M512 896a384 384 0 1 0 0-768 384 384 0 0 0 0 768m0 64a448 448 0 1 1 0-896 448 448 0 0 1 0 896"}),no("path",{fill:"currentColor",d:"M672 234.88c-39.168 174.464-80 298.624-122.688 372.48-64 110.848-202.624 30.848-138.624-80C453.376 453.44 540.48 355.968 672 234.816z"})]))}}),lo=Ov;import{defineComponent as Iv}from"vue";import{createElementVNode as Gv,openBlock as Uv,createElementBlock as Wv}from"vue";var Kv=Iv({name:"SuccessFilled",__name:"success-filled",setup(t){return(e,o)=>(Uv(),Wv("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Gv("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896m-55.808 536.384-99.52-99.584a38.4 38.4 0 1 0-54.336 54.336l126.72 126.72a38.27 38.27 0 0 0 54.336 0l262.4-262.464a38.4 38.4 0 1 0-54.272-54.336z"})]))}}),mo=Kv;import{defineComponent as Qv}from"vue";import{createElementVNode as Zv,openBlock as jv,createElementBlock as Jv}from"vue";var Xv=Qv({name:"Sugar",__name:"sugar",setup(t){return(e,o)=>(jv(),Jv("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Zv("path",{fill:"currentColor",d:"m801.728 349.184 4.48 4.48a128 128 0 0 1 0 180.992L534.656 806.144a128 128 0 0 1-181.056 0l-4.48-4.48-19.392 109.696a64 64 0 0 1-108.288 34.176L78.464 802.56a64 64 0 0 1 34.176-108.288l109.76-19.328-4.544-4.544a128 128 0 0 1 0-181.056l271.488-271.488a128 128 0 0 1 181.056 0l4.48 4.48 19.392-109.504a64 64 0 0 1 108.352-34.048l142.592 143.04a64 64 0 0 1-34.24 108.16zm-548.8 198.72h447.168v2.24l60.8-60.8a63.8 63.8 0 0 0 18.752-44.416h-426.88l-89.664 89.728a64 64 0 0 0-10.24 13.248zm0 64q4.128 7.104 10.176 13.248l135.744 135.744a64 64 0 0 0 90.496 0L638.4 611.904zm490.048-230.976L625.152 263.104a64 64 0 0 0-90.496 0L416.768 380.928zM123.712 757.312l142.976 142.976 24.32-137.6a25.6 25.6 0 0 0-29.696-29.632zm633.6-633.344-24.32 137.472a25.6 25.6 0 0 0 29.632 29.632l137.28-24.064-142.656-143.04z"})]))}}),co=Xv;import{defineComponent as Yv}from"vue";import{createElementVNode as $v,openBlock as ew,createElementBlock as ow}from"vue";var tw=Yv({name:"SuitcaseLine",__name:"suitcase-line",setup(t){return(e,o)=>(ew(),ow("svg",{xmlns:"http://www.w3.org/2000/svg","xml:space":"preserve",viewBox:"0 0 1024 1024"},[$v("path",{fill:"currentColor",d:"M922.5 229.5c-24.32-24.34-54.49-36.84-90.5-37.5H704v-64c-.68-17.98-7.02-32.98-19.01-44.99S658.01 64.66 640 64H384c-17.98.68-32.98 7.02-44.99 19.01S320.66 110 320 128v64H192c-35.99.68-66.16 13.18-90.5 37.5S64.66 283.99 64 320v448c.68 35.99 13.18 66.16 37.5 90.5s54.49 36.84 90.5 37.5h640c35.99-.68 66.16-13.18 90.5-37.5s36.84-54.49 37.5-90.5V320c-.68-35.99-13.18-66.16-37.5-90.5M384 128h256v64H384zM256 832h-64c-17.98-.68-32.98-7.02-44.99-19.01S128.66 786.01 128 768V448h128zm448 0H320V448h384zm192-64c-.68 17.98-7.02 32.98-19.01 44.99S850.01 831.34 832 832h-64V448h128zm0-384H128v-64c.69-17.98 7.02-32.98 19.01-44.99S173.99 256.66 192 256h640c17.98.69 32.98 7.02 44.99 19.01S895.34 301.99 896 320z"})]))}}),po=tw;import{defineComponent as aw}from"vue";import{createElementVNode as so,openBlock as rw,createElementBlock as nw}from"vue";var lw=aw({name:"Suitcase",__name:"suitcase",setup(t){return(e,o)=>(rw(),nw("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[so("path",{fill:"currentColor",d:"M128 384h768v-64a64 64 0 0 0-64-64H192a64 64 0 0 0-64 64zm0 64v320a64 64 0 0 0 64 64h640a64 64 0 0 0 64-64V448zm64-256h640a128 128 0 0 1 128 128v448a128 128 0 0 1-128 128H192A128 128 0 0 1 64 768V320a128 128 0 0 1 128-128"}),so("path",{fill:"currentColor",d:"M384 128v64h256v-64zm0-64h256a64 64 0 0 1 64 64v64a64 64 0 0 1-64 64H384a64 64 0 0 1-64-64v-64a64 64 0 0 1 64-64"})]))}}),_o=lw;import{defineComponent as mw}from"vue";import{createElementVNode as cw,openBlock as pw,createElementBlock as sw}from"vue";var _w=mw({name:"Sunny",__name:"sunny",setup(t){return(e,o)=>(pw(),sw("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[cw("path",{fill:"currentColor",d:"M512 704a192 192 0 1 0 0-384 192 192 0 0 0 0 384m0 64a256 256 0 1 1 0-512 256 256 0 0 1 0 512m0-704a32 32 0 0 1 32 32v64a32 32 0 0 1-64 0V96a32 32 0 0 1 32-32m0 768a32 32 0 0 1 32 32v64a32 32 0 1 1-64 0v-64a32 32 0 0 1 32-32M195.2 195.2a32 32 0 0 1 45.248 0l45.248 45.248a32 32 0 1 1-45.248 45.248L195.2 240.448a32 32 0 0 1 0-45.248m543.104 543.104a32 32 0 0 1 45.248 0l45.248 45.248a32 32 0 0 1-45.248 45.248l-45.248-45.248a32 32 0 0 1 0-45.248M64 512a32 32 0 0 1 32-32h64a32 32 0 0 1 0 64H96a32 32 0 0 1-32-32m768 0a32 32 0 0 1 32-32h64a32 32 0 1 1 0 64h-64a32 32 0 0 1-32-32M195.2 828.8a32 32 0 0 1 0-45.248l45.248-45.248a32 32 0 0 1 45.248 45.248L240.448 828.8a32 32 0 0 1-45.248 0m543.104-543.104a32 32 0 0 1 0-45.248l45.248-45.248a32 32 0 0 1 45.248 45.248l-45.248 45.248a32 32 0 0 1-45.248 0"})]))}}),fo=_w;import{defineComponent as fw}from"vue";import{createElementVNode as iw,openBlock as dw,createElementBlock as uw}from"vue";var hw=fw({name:"Sunrise",__name:"sunrise",setup(t){return(e,o)=>(dw(),uw("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[iw("path",{fill:"currentColor",d:"M32 768h960a32 32 0 1 1 0 64H32a32 32 0 1 1 0-64m129.408-96a352 352 0 0 1 701.184 0h-64.32a288 288 0 0 0-572.544 0zM512 128a32 32 0 0 1 32 32v96a32 32 0 0 1-64 0v-96a32 32 0 0 1 32-32m407.296 168.704a32 32 0 0 1 0 45.248l-67.84 67.84a32 32 0 1 1-45.248-45.248l67.84-67.84a32 32 0 0 1 45.248 0m-814.592 0a32 32 0 0 1 45.248 0l67.84 67.84a32 32 0 1 1-45.248 45.248l-67.84-67.84a32 32 0 0 1 0-45.248"})]))}}),io=hw;import{defineComponent as vw}from"vue";import{createElementVNode as ww,openBlock as Bw,createElementBlock as xw}from"vue";var kw=vw({name:"Sunset",__name:"sunset",setup(t){return(e,o)=>(Bw(),xw("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[ww("path",{fill:"currentColor",d:"M82.56 640a448 448 0 1 1 858.88 0h-67.2a384 384 0 1 0-724.288 0zM32 704h960q32 0 32 32t-32 32H32q-32 0-32-32t32-32m256 128h448q32 0 32 32t-32 32H288q-32 0-32-32t32-32"})]))}}),uo=kw;import{defineComponent as Cw}from"vue";import{createElementVNode as ho,openBlock as Ew,createElementBlock as Vw}from"vue";var gw=Cw({name:"SwitchButton",__name:"switch-button",setup(t){return(e,o)=>(Ew(),Vw("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[ho("path",{fill:"currentColor",d:"M352 159.872V230.4a352 352 0 1 0 320 0v-70.528A416.128 416.128 0 0 1 512 960a416 416 0 0 1-160-800.128"}),ho("path",{fill:"currentColor",d:"M512 64q32 0 32 32v320q0 32-32 32t-32-32V96q0-32 32-32"})]))}}),vo=gw;import{defineComponent as Mw}from"vue";import{createElementVNode as wo,openBlock as Nw,createElementBlock as zw}from"vue";var Hw=Mw({name:"SwitchFilled",__name:"switch-filled",setup(t){return(e,o)=>(Nw(),zw("svg",{xmlns:"http://www.w3.org/2000/svg","xml:space":"preserve",viewBox:"0 0 1024 1024"},[wo("path",{fill:"currentColor",d:"M247.47 358.4v.04c.07 19.17 7.72 37.53 21.27 51.09s31.92 21.2 51.09 21.27c39.86 0 72.41-32.6 72.41-72.4s-32.6-72.36-72.41-72.36-72.36 32.55-72.36 72.36"}),wo("path",{fill:"currentColor",d:"M492.38 128H324.7c-52.16 0-102.19 20.73-139.08 57.61a196.66 196.66 0 0 0-57.61 139.08V698.7c-.01 25.84 5.08 51.42 14.96 75.29s24.36 45.56 42.63 63.83 39.95 32.76 63.82 42.65a196.7 196.7 0 0 0 75.28 14.98h167.68c3.03 0 5.46-2.43 5.46-5.42V133.42c.6-2.99-1.83-5.42-5.46-5.42m-56.11 705.88H324.7c-17.76.13-35.36-3.33-51.75-10.18s-31.22-16.94-43.61-29.67c-25.3-25.35-39.81-59.1-39.81-95.32V324.69c-.13-17.75 3.33-35.35 10.17-51.74a131.7 131.7 0 0 1 29.64-43.62c25.39-25.3 59.14-39.81 95.36-39.81h111.57zm402.12-647.67a196.66 196.66 0 0 0-139.08-57.61H580.48c-3.03 0-4.82 2.43-4.82 4.82v757.16c-.6 2.99 1.79 5.42 5.42 5.42h118.23a196.7 196.7 0 0 0 139.08-57.61A196.66 196.66 0 0 0 896 699.31V325.29a196.7 196.7 0 0 0-57.61-139.08m-111.3 441.92c-42.83 0-77.82-34.99-77.82-77.82s34.98-77.82 77.82-77.82c42.83 0 77.82 34.99 77.82 77.82s-34.99 77.82-77.82 77.82"})]))}}),Bo=Hw;import{defineComponent as Lw}from"vue";import{createElementVNode as Aw,openBlock as Sw,createElementBlock as qw}from"vue";var Fw=Lw({name:"Switch",__name:"switch",setup(t){return(e,o)=>(Sw(),qw("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Aw("path",{fill:"currentColor",d:"M118.656 438.656a32 32 0 0 1 0-45.248L416 96l4.48-3.776A32 32 0 0 1 461.248 96l3.712 4.48a32.064 32.064 0 0 1-3.712 40.832L218.56 384H928a32 32 0 1 1 0 64H141.248a32 32 0 0 1-22.592-9.344M64 608a32 32 0 0 1 32-32h786.752a32 32 0 0 1 22.656 54.592L608 928l-4.48 3.776a32.064 32.064 0 0 1-40.832-49.024L805.632 640H96a32 32 0 0 1-32-32"})]))}}),xo=Fw;import{defineComponent as Dw}from"vue";import{createElementVNode as bw,openBlock as yw,createElementBlock as Pw}from"vue";var Rw=Dw({name:"TakeawayBox",__name:"takeaway-box",setup(t){return(e,o)=>(yw(),Pw("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[bw("path",{fill:"currentColor",d:"M832 384H192v448h640zM96 320h832V128H96zm800 64v480a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V384H64a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32h896a32 32 0 0 1 32 32v256a32 32 0 0 1-32 32zM416 512h192a32 32 0 0 1 0 64H416a32 32 0 0 1 0-64"})]))}}),ko=Rw;import{defineComponent as Tw}from"vue";import{createElementVNode as Ow,openBlock as Iw,createElementBlock as Gw}from"vue";var Uw=Tw({name:"Ticket",__name:"ticket",setup(t){return(e,o)=>(Iw(),Gw("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Ow("path",{fill:"currentColor",d:"M640 832H64V640a128 128 0 1 0 0-256V192h576v160h64V192h256v192a128 128 0 1 0 0 256v192H704V672h-64zm0-416v192h64V416z"})]))}}),Co=Uw;import{defineComponent as Ww}from"vue";import{createElementVNode as Kw,openBlock as Qw,createElementBlock as Zw}from"vue";var jw=Ww({name:"Tickets",__name:"tickets",setup(t){return(e,o)=>(Qw(),Zw("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Kw("path",{fill:"currentColor",d:"M192 128v768h640V128zm-32-64h704a32 32 0 0 1 32 32v832a32 32 0 0 1-32 32H160a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32m160 448h384v64H320zm0-192h192v64H320zm0 384h384v64H320z"})]))}}),Eo=jw;import{defineComponent as Jw}from"vue";import{createElementVNode as g,openBlock as Xw,createElementBlock as Yw}from"vue";var $w=Jw({name:"Timer",__name:"timer",setup(t){return(e,o)=>(Xw(),Yw("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[g("path",{fill:"currentColor",d:"M512 896a320 320 0 1 0 0-640 320 320 0 0 0 0 640m0 64a384 384 0 1 1 0-768 384 384 0 0 1 0 768"}),g("path",{fill:"currentColor",d:"M512 320a32 32 0 0 1 32 32l-.512 224a32 32 0 1 1-64 0L480 352a32 32 0 0 1 32-32"}),g("path",{fill:"currentColor",d:"M448 576a64 64 0 1 0 128 0 64 64 0 1 0-128 0m96-448v128h-64V128h-96a32 32 0 0 1 0-64h256a32 32 0 1 1 0 64z"})]))}}),Vo=$w;import{defineComponent as eB}from"vue";import{createElementVNode as go,openBlock as oB,createElementBlock as tB}from"vue";var aB=eB({name:"ToiletPaper",__name:"toilet-paper",setup(t){return(e,o)=>(oB(),tB("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[go("path",{fill:"currentColor",d:"M595.2 128H320a192 192 0 0 0-192 192v576h384V352c0-90.496 32.448-171.2 83.2-224M736 64c123.712 0 224 128.96 224 288S859.712 640 736 640H576v320H64V320A256 256 0 0 1 320 64zM576 352v224h160c84.352 0 160-97.28 160-224s-75.648-224-160-224-160 97.28-160 224"}),go("path",{fill:"currentColor",d:"M736 448c-35.328 0-64-43.008-64-96s28.672-96 64-96 64 43.008 64 96-28.672 96-64 96"})]))}}),Mo=aB;import{defineComponent as rB}from"vue";import{createElementVNode as nB,openBlock as lB,createElementBlock as mB}from"vue";var cB=rB({name:"Tools",__name:"tools",setup(t){return(e,o)=>(lB(),mB("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[nB("path",{fill:"currentColor",d:"M764.416 254.72a351.7 351.7 0 0 1 86.336 149.184H960v192.064H850.752a351.7 351.7 0 0 1-86.336 149.312l54.72 94.72-166.272 96-54.592-94.72a352.64 352.64 0 0 1-172.48 0L371.136 936l-166.272-96 54.72-94.72a351.7 351.7 0 0 1-86.336-149.312H64v-192h109.248a351.7 351.7 0 0 1 86.336-149.312L204.8 160l166.208-96h.192l54.656 94.592a352.64 352.64 0 0 1 172.48 0L652.8 64h.128L819.2 160l-54.72 94.72zM704 499.968a192 192 0 1 0-384 0 192 192 0 0 0 384 0"})]))}}),No=cB;import{defineComponent as pB}from"vue";import{createElementVNode as zo,openBlock as sB,createElementBlock as _B}from"vue";var fB=pB({name:"TopLeft",__name:"top-left",setup(t){return(e,o)=>(sB(),_B("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[zo("path",{fill:"currentColor",d:"M256 256h416a32 32 0 1 0 0-64H224a32 32 0 0 0-32 32v448a32 32 0 0 0 64 0z"}),zo("path",{fill:"currentColor",d:"M246.656 201.344a32 32 0 0 0-45.312 45.312l544 544a32 32 0 0 0 45.312-45.312z"})]))}}),Ho=fB;import{defineComponent as iB}from"vue";import{createElementVNode as Lo,openBlock as dB,createElementBlock as uB}from"vue";var hB=iB({name:"TopRight",__name:"top-right",setup(t){return(e,o)=>(dB(),uB("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Lo("path",{fill:"currentColor",d:"M768 256H353.6a32 32 0 1 1 0-64H800a32 32 0 0 1 32 32v448a32 32 0 0 1-64 0z"}),Lo("path",{fill:"currentColor",d:"M777.344 201.344a32 32 0 0 1 45.312 45.312l-544 544a32 32 0 0 1-45.312-45.312z"})]))}}),Ao=hB;import{defineComponent as vB}from"vue";import{createElementVNode as wB,openBlock as BB,createElementBlock as xB}from"vue";var kB=vB({name:"Top",__name:"top",setup(t){return(e,o)=>(BB(),xB("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[wB("path",{fill:"currentColor",d:"M572.235 205.282v600.365a30.118 30.118 0 1 1-60.235 0V205.282L292.382 438.633a28.913 28.913 0 0 1-42.646 0 33.43 33.43 0 0 1 0-45.236l271.058-288.045a28.913 28.913 0 0 1 42.647 0L834.5 393.397a33.43 33.43 0 0 1 0 45.176 28.913 28.913 0 0 1-42.647 0l-219.618-233.23z"})]))}}),So=kB;import{defineComponent as CB}from"vue";import{createElementVNode as EB,openBlock as VB,createElementBlock as gB}from"vue";var MB=CB({name:"TrendCharts",__name:"trend-charts",setup(t){return(e,o)=>(VB(),gB("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[EB("path",{fill:"currentColor",d:"M128 896V128h768v768zm291.712-327.296 128 102.4 180.16-201.792-47.744-42.624-139.84 156.608-128-102.4-180.16 201.792 47.744 42.624zM816 352a48 48 0 1 0-96 0 48 48 0 0 0 96 0"})]))}}),qo=MB;import{defineComponent as NB}from"vue";import{createElementVNode as zB,openBlock as HB,createElementBlock as LB}from"vue";var AB=NB({name:"TrophyBase",__name:"trophy-base",setup(t){return(e,o)=>(HB(),LB("svg",{xmlns:"http://www.w3.org/2000/svg","xml:space":"preserve",viewBox:"0 0 1024 1024"},[zB("path",{fill:"currentColor",d:"M918.4 201.6c-6.4-6.4-12.8-9.6-22.4-9.6H768V96c0-9.6-3.2-16-9.6-22.4S745.6 64 736 64H288c-9.6 0-16 3.2-22.4 9.6S256 86.4 256 96v96H128c-9.6 0-16 3.2-22.4 9.6S96 217.6 96 224c3.2 108.8 25.6 185.6 64 224 34.4 34.4 77.56 55.65 127.65 61.99 10.91 20.44 24.78 39.25 41.95 56.41 40.86 40.86 91 65.47 150.4 71.9V768h-96c-9.6 0-16 3.2-22.4 9.6S352 790.4 352 800s3.2 16 9.6 22.4 12.8 9.6 22.4 9.6h256c9.6 0 16-3.2 22.4-9.6s9.6-12.8 9.6-22.4-3.2-16-9.6-22.4-12.8-9.6-22.4-9.6h-96V637.26c59.4-7.71 109.54-30.01 150.4-70.86 17.2-17.2 31.51-36.06 42.81-56.55 48.93-6.51 90.02-27.7 126.79-61.85 38.4-38.4 60.8-112 64-224 0-6.4-3.2-16-9.6-22.4M256 438.4c-19.2-6.4-35.2-19.2-51.2-35.2-22.4-22.4-35.2-70.4-41.6-147.2H256zm390.4 80C608 553.6 566.4 576 512 576s-99.2-19.2-134.4-57.6S320 438.4 320 384V128h384v256q0 81.6-57.6 134.4m172.8-115.2c-16 16-32 25.6-51.2 35.2V256h92.8c-6.4 76.8-19.2 124.8-41.6 147.2M768 896H256c-9.6 0-16 3.2-22.4 9.6S224 918.4 224 928s3.2 16 9.6 22.4 12.8 9.6 22.4 9.6h512c9.6 0 16-3.2 22.4-9.6s9.6-12.8 9.6-22.4-3.2-16-9.6-22.4-12.8-9.6-22.4-9.6"})]))}}),Fo=AB;import{defineComponent as SB}from"vue";import{createElementVNode as qB,openBlock as FB,createElementBlock as DB}from"vue";var bB=SB({name:"Trophy",__name:"trophy",setup(t){return(e,o)=>(FB(),DB("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[qB("path",{fill:"currentColor",d:"M480 896V702.08A256.26 256.26 0 0 1 264.064 512h-32.64a96 96 0 0 1-91.968-68.416L93.632 290.88a76.8 76.8 0 0 1 73.6-98.88H256V96a32 32 0 0 1 32-32h448a32 32 0 0 1 32 32v96h88.768a76.8 76.8 0 0 1 73.6 98.88L884.48 443.52A96 96 0 0 1 792.576 512h-32.64A256.26 256.26 0 0 1 544 702.08V896h128a32 32 0 1 1 0 64H352a32 32 0 1 1 0-64zm224-448V128H320v320a192 192 0 1 0 384 0m64 0h24.576a32 32 0 0 0 30.656-22.784l45.824-152.768A12.8 12.8 0 0 0 856.768 256H768zm-512 0V256h-88.768a12.8 12.8 0 0 0-12.288 16.448l45.824 152.768A32 32 0 0 0 231.424 448z"})]))}}),Do=bB;import{defineComponent as yB}from"vue";import{createElementVNode as bo,openBlock as PB,createElementBlock as RB}from"vue";var TB=yB({name:"TurnOff",__name:"turn-off",setup(t){return(e,o)=>(PB(),RB("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[bo("path",{fill:"currentColor",d:"M329.956 257.138a254.862 254.862 0 0 0 0 509.724h364.088a254.862 254.862 0 0 0 0-509.724zm0-72.818h364.088a327.68 327.68 0 1 1 0 655.36H329.956a327.68 327.68 0 1 1 0-655.36"}),bo("path",{fill:"currentColor",d:"M329.956 621.227a109.227 109.227 0 1 0 0-218.454 109.227 109.227 0 0 0 0 218.454m0 72.817a182.044 182.044 0 1 1 0-364.088 182.044 182.044 0 0 1 0 364.088"})]))}}),yo=TB;import{defineComponent as OB}from"vue";import{createElementVNode as IB,openBlock as GB,createElementBlock as UB}from"vue";var WB=OB({name:"Umbrella",__name:"umbrella",setup(t){return(e,o)=>(GB(),UB("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[IB("path",{fill:"currentColor",d:"M320 768a32 32 0 1 1 64 0 64 64 0 0 0 128 0V512H64a448 448 0 1 1 896 0H576v256a128 128 0 1 1-256 0m570.688-320a384.128 384.128 0 0 0-757.376 0z"})]))}}),Po=WB;import{defineComponent as KB}from"vue";import{createElementVNode as Ro,openBlock as QB,createElementBlock as ZB}from"vue";var jB=KB({name:"Unlock",__name:"unlock",setup(t){return(e,o)=>(QB(),ZB("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Ro("path",{fill:"currentColor",d:"M224 448a32 32 0 0 0-32 32v384a32 32 0 0 0 32 32h576a32 32 0 0 0 32-32V480a32 32 0 0 0-32-32zm0-64h576a96 96 0 0 1 96 96v384a96 96 0 0 1-96 96H224a96 96 0 0 1-96-96V480a96 96 0 0 1 96-96"}),Ro("path",{fill:"currentColor",d:"M512 544a32 32 0 0 1 32 32v192a32 32 0 1 1-64 0V576a32 32 0 0 1 32-32m178.304-295.296A192.064 192.064 0 0 0 320 320v64h352l96 38.4V448H256V320a256 256 0 0 1 493.76-95.104z"})]))}}),To=jB;import{defineComponent as JB}from"vue";import{createElementVNode as XB,openBlock as YB,createElementBlock as $B}from"vue";var ex=JB({name:"UploadFilled",__name:"upload-filled",setup(t){return(e,o)=>(YB(),$B("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[XB("path",{fill:"currentColor",d:"M544 864V672h128L512 480 352 672h128v192H320v-1.6c-5.376.32-10.496 1.6-16 1.6A240 240 0 0 1 64 624c0-123.136 93.12-223.488 212.608-237.248A239.81 239.81 0 0 1 512 192a239.87 239.87 0 0 1 235.456 194.752c119.488 13.76 212.48 114.112 212.48 237.248a240 240 0 0 1-240 240c-5.376 0-10.56-1.28-16-1.6v1.6z"})]))}}),Oo=ex;import{defineComponent as ox}from"vue";import{createElementVNode as tx,openBlock as ax,createElementBlock as rx}from"vue";var nx=ox({name:"Upload",__name:"upload",setup(t){return(e,o)=>(ax(),rx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[tx("path",{fill:"currentColor",d:"M160 832h704a32 32 0 1 1 0 64H160a32 32 0 1 1 0-64m384-578.304V704h-64V247.296L237.248 490.048 192 444.8 508.8 128l316.8 316.8-45.312 45.248z"})]))}}),Io=nx;import{defineComponent as lx}from"vue";import{createElementVNode as mx,openBlock as cx,createElementBlock as px}from"vue";var sx=lx({name:"UserFilled",__name:"user-filled",setup(t){return(e,o)=>(cx(),px("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[mx("path",{fill:"currentColor",d:"M288 320a224 224 0 1 0 448 0 224 224 0 1 0-448 0m544 608H160a32 32 0 0 1-32-32v-96a160 160 0 0 1 160-160h448a160 160 0 0 1 160 160v96a32 32 0 0 1-32 32z"})]))}}),Go=sx;import{defineComponent as _x}from"vue";import{createElementVNode as fx,openBlock as ix,createElementBlock as dx}from"vue";var ux=_x({name:"User",__name:"user",setup(t){return(e,o)=>(ix(),dx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[fx("path",{fill:"currentColor",d:"M512 512a192 192 0 1 0 0-384 192 192 0 0 0 0 384m0 64a256 256 0 1 1 0-512 256 256 0 0 1 0 512m320 320v-96a96 96 0 0 0-96-96H288a96 96 0 0 0-96 96v96a32 32 0 1 1-64 0v-96a160 160 0 0 1 160-160h448a160 160 0 0 1 160 160v96a32 32 0 1 1-64 0"})]))}}),Uo=ux;import{defineComponent as hx}from"vue";import{createElementVNode as vx,openBlock as wx,createElementBlock as Bx}from"vue";var xx=hx({name:"Van",__name:"van",setup(t){return(e,o)=>(wx(),Bx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[vx("path",{fill:"currentColor",d:"M128.896 736H96a32 32 0 0 1-32-32V224a32 32 0 0 1 32-32h576a32 32 0 0 1 32 32v96h164.544a32 32 0 0 1 31.616 27.136l54.144 352A32 32 0 0 1 922.688 736h-91.52a144 144 0 1 1-286.272 0H415.104a144 144 0 1 1-286.272 0zm23.36-64a143.872 143.872 0 0 1 239.488 0H568.32c17.088-25.6 42.24-45.376 71.744-55.808V256H128v416zm655.488 0h77.632l-19.648-128H704v64.896A144 144 0 0 1 807.744 672m48.128-192-14.72-96H704v96zM688 832a80 80 0 1 0 0-160 80 80 0 0 0 0 160m-416 0a80 80 0 1 0 0-160 80 80 0 0 0 0 160"})]))}}),Wo=xx;import{defineComponent as kx}from"vue";import{createElementVNode as Cx,openBlock as Ex,createElementBlock as Vx}from"vue";var gx=kx({name:"VideoCameraFilled",__name:"video-camera-filled",setup(t){return(e,o)=>(Ex(),Vx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Cx("path",{fill:"currentColor",d:"m768 576 192-64v320l-192-64v96a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V480a32 32 0 0 1 32-32h640a32 32 0 0 1 32 32zM192 768v64h384v-64zm192-480a160 160 0 0 1 320 0 160 160 0 0 1-320 0m64 0a96 96 0 1 0 192.064-.064A96 96 0 0 0 448 288m-320 32a128 128 0 1 1 256.064.064A128 128 0 0 1 128 320m64 0a64 64 0 1 0 128 0 64 64 0 0 0-128 0"})]))}}),Ko=gx;import{defineComponent as Mx}from"vue";import{createElementVNode as Nx,openBlock as zx,createElementBlock as Hx}from"vue";var Lx=Mx({name:"VideoCamera",__name:"video-camera",setup(t){return(e,o)=>(zx(),Hx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Nx("path",{fill:"currentColor",d:"M704 768V256H128v512zm64-416 192-96v512l-192-96v128a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V224a32 32 0 0 1 32-32h640a32 32 0 0 1 32 32zm0 71.552v176.896l128 64V359.552zM192 320h192v64H192z"})]))}}),Qo=Lx;import{defineComponent as Ax}from"vue";import{createElementVNode as Sx,openBlock as qx,createElementBlock as Fx}from"vue";var Dx=Ax({name:"VideoPause",__name:"video-pause",setup(t){return(e,o)=>(qx(),Fx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Sx("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896m0 832a384 384 0 0 0 0-768 384 384 0 0 0 0 768m-96-544q32 0 32 32v256q0 32-32 32t-32-32V384q0-32 32-32m192 0q32 0 32 32v256q0 32-32 32t-32-32V384q0-32 32-32"})]))}}),Zo=Dx;import{defineComponent as bx}from"vue";import{createElementVNode as yx,openBlock as Px,createElementBlock as Rx}from"vue";var Tx=bx({name:"VideoPlay",__name:"video-play",setup(t){return(e,o)=>(Px(),Rx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[yx("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896m0 832a384 384 0 0 0 0-768 384 384 0 0 0 0 768m-48-247.616L668.608 512 464 375.616zm10.624-342.656 249.472 166.336a48 48 0 0 1 0 79.872L474.624 718.272A48 48 0 0 1 400 678.336V345.6a48 48 0 0 1 74.624-39.936z"})]))}}),jo=Tx;import{defineComponent as Ox}from"vue";import{createElementVNode as Ix,openBlock as Gx,createElementBlock as Ux}from"vue";var Wx=Ox({name:"View",__name:"view",setup(t){return(e,o)=>(Gx(),Ux("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Ix("path",{fill:"currentColor",d:"M512 160c320 0 512 352 512 352S832 864 512 864 0 512 0 512s192-352 512-352m0 64c-225.28 0-384.128 208.064-436.8 288 52.608 79.872 211.456 288 436.8 288 225.28 0 384.128-208.064 436.8-288-52.608-79.872-211.456-288-436.8-288m0 64a224 224 0 1 1 0 448 224 224 0 0 1 0-448m0 64a160.19 160.19 0 0 0-160 160c0 88.192 71.744 160 160 160s160-71.808 160-160-71.744-160-160-160"})]))}}),Jo=Wx;import{defineComponent as Kx}from"vue";import{createElementVNode as Qx,openBlock as Zx,createElementBlock as jx}from"vue";var Jx=Kx({name:"WalletFilled",__name:"wallet-filled",setup(t){return(e,o)=>(Zx(),jx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Qx("path",{fill:"currentColor",d:"M688 512a112 112 0 1 0 0 224h208v160H128V352h768v160zm32 160h-32a48 48 0 0 1 0-96h32a48 48 0 0 1 0 96m-80-544 128 160H384z"})]))}}),Xo=Jx;import{defineComponent as Xx}from"vue";import{createElementVNode as M,openBlock as Yx,createElementBlock as $x}from"vue";var ek=Xx({name:"Wallet",__name:"wallet",setup(t){return(e,o)=>(Yx(),$x("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[M("path",{fill:"currentColor",d:"M640 288h-64V128H128v704h384v32a32 32 0 0 0 32 32H96a32 32 0 0 1-32-32V96a32 32 0 0 1 32-32h512a32 32 0 0 1 32 32z"}),M("path",{fill:"currentColor",d:"M128 320v512h768V320zm-32-64h832a32 32 0 0 1 32 32v576a32 32 0 0 1-32 32H96a32 32 0 0 1-32-32V288a32 32 0 0 1 32-32"}),M("path",{fill:"currentColor",d:"M704 640a64 64 0 1 1 0-128 64 64 0 0 1 0 128"})]))}}),Yo=ek;import{defineComponent as ok}from"vue";import{createElementVNode as tk,openBlock as ak,createElementBlock as rk}from"vue";var nk=ok({name:"WarnTriangleFilled",__name:"warn-triangle-filled",setup(t){return(e,o)=>(ak(),rk("svg",{xmlns:"http://www.w3.org/2000/svg","xml:space":"preserve",viewBox:"0 0 1024 1024"},[tk("path",{fill:"currentColor",d:"M928.99 755.83 574.6 203.25c-12.89-20.16-36.76-32.58-62.6-32.58s-49.71 12.43-62.6 32.58L95.01 755.83c-12.91 20.12-12.9 44.91.01 65.03 12.92 20.12 36.78 32.51 62.59 32.49h708.78c25.82.01 49.68-12.37 62.59-32.49s12.92-44.91.01-65.03M554.67 768h-85.33v-85.33h85.33zm0-426.67v298.66h-85.33V341.32z"})]))}}),$o=nk;import{defineComponent as lk}from"vue";import{createElementVNode as mk,openBlock as ck,createElementBlock as pk}from"vue";var sk=lk({name:"WarningFilled",__name:"warning-filled",setup(t){return(e,o)=>(ck(),pk("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[mk("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896m0 192a58.43 58.43 0 0 0-58.24 63.744l23.36 256.384a35.072 35.072 0 0 0 69.76 0l23.296-256.384A58.43 58.43 0 0 0 512 256m0 512a51.2 51.2 0 1 0 0-102.4 51.2 51.2 0 0 0 0 102.4"})]))}}),et=sk;import{defineComponent as _k}from"vue";import{createElementVNode as fk,openBlock as ik,createElementBlock as dk}from"vue";var uk=_k({name:"Warning",__name:"warning",setup(t){return(e,o)=>(ik(),dk("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[fk("path",{fill:"currentColor",d:"M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896m0 832a384 384 0 0 0 0-768 384 384 0 0 0 0 768m48-176a48 48 0 1 1-96 0 48 48 0 0 1 96 0m-48-464a32 32 0 0 1 32 32v288a32 32 0 0 1-64 0V288a32 32 0 0 1 32-32"})]))}}),ot=uk;import{defineComponent as hk}from"vue";import{createElementVNode as N,openBlock as vk,createElementBlock as wk}from"vue";var Bk=hk({name:"Watch",__name:"watch",setup(t){return(e,o)=>(vk(),wk("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[N("path",{fill:"currentColor",d:"M512 768a256 256 0 1 0 0-512 256 256 0 0 0 0 512m0 64a320 320 0 1 1 0-640 320 320 0 0 1 0 640"}),N("path",{fill:"currentColor",d:"M480 352a32 32 0 0 1 32 32v160a32 32 0 0 1-64 0V384a32 32 0 0 1 32-32"}),N("path",{fill:"currentColor",d:"M480 512h128q32 0 32 32t-32 32H480q-32 0-32-32t32-32m128-256V128H416v128h-64V64h320v192zM416 768v128h192V768h64v192H352V768z"})]))}}),tt=Bk;import{defineComponent as xk}from"vue";import{createElementVNode as kk,openBlock as Ck,createElementBlock as Ek}from"vue";var Vk=xk({name:"Watermelon",__name:"watermelon",setup(t){return(e,o)=>(Ck(),Ek("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[kk("path",{fill:"currentColor",d:"m683.072 600.32-43.648 162.816-61.824-16.512 53.248-198.528L576 493.248l-158.4 158.4-45.248-45.248 158.4-158.4-55.616-55.616-198.528 53.248-16.512-61.824 162.816-43.648L282.752 200A384 384 0 0 0 824 741.248zm231.552 141.056a448 448 0 1 1-632-632z"})]))}}),at=Vk;import{defineComponent as gk}from"vue";import{createElementVNode as Mk,openBlock as Nk,createElementBlock as zk}from"vue";var Hk=gk({name:"WindPower",__name:"wind-power",setup(t){return(e,o)=>(Nk(),zk("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Mk("path",{fill:"currentColor",d:"M160 64q32 0 32 32v832q0 32-32 32t-32-32V96q0-32 32-32m416 354.624 128-11.584V168.96l-128-11.52v261.12zm-64 5.824V151.552L320 134.08V160h-64V64l616.704 56.064A96 96 0 0 1 960 215.68v144.64a96 96 0 0 1-87.296 95.616L256 512V224h64v217.92zm256-23.232 98.88-8.96A32 32 0 0 0 896 360.32V215.68a32 32 0 0 0-29.12-31.872l-98.88-8.96z"})]))}}),rt=Hk;import{defineComponent as Lk}from"vue";import{createElementVNode as Ak,openBlock as Sk,createElementBlock as qk}from"vue";var Fk=Lk({name:"ZoomIn",__name:"zoom-in",setup(t){return(e,o)=>(Sk(),qk("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[Ak("path",{fill:"currentColor",d:"m795.904 750.72 124.992 124.928a32 32 0 0 1-45.248 45.248L750.656 795.904a416 416 0 1 1 45.248-45.248zM480 832a352 352 0 1 0 0-704 352 352 0 0 0 0 704m-32-384v-96a32 32 0 0 1 64 0v96h96a32 32 0 0 1 0 64h-96v96a32 32 0 0 1-64 0v-96h-96a32 32 0 0 1 0-64z"})]))}}),nt=Fk;import{defineComponent as Dk}from"vue";import{createElementVNode as bk,openBlock as yk,createElementBlock as Pk}from"vue";var Rk=Dk({name:"ZoomOut",__name:"zoom-out",setup(t){return(e,o)=>(yk(),Pk("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 1024 1024"},[bk("path",{fill:"currentColor",d:"m795.904 750.72 124.992 124.928a32 32 0 0 1-45.248 45.248L750.656 795.904a416 416 0 1 1 45.248-45.248zM480 832a352 352 0 1 0 0-704 352 352 0 0 0 0 704M352 448h256a32 32 0 0 1 0 64H352a32 32 0 0 1 0-64"})]))}}),lt=Rk;var gG=(t,{prefix:e="ElIcon"}={})=>{for(let[o,mt]of Object.entries(z))t.component(e+o,mt)};export{H as AddLocation,A as Aim,q as AlarmClock,F as Apple,b as ArrowDown,D as ArrowDownBold,P as ArrowLeft,y as ArrowLeftBold,T as ArrowRight,R as ArrowRightBold,I as ArrowUp,O as ArrowUpBold,G as Avatar,W as Back,Q as Baseball,Z as Basketball,J as Bell,j as BellFilled,X as Bicycle,t2 as Bottom,$ as BottomLeft,o2 as BottomRight,a2 as Bowl,r2 as Box,n2 as Briefcase,m2 as Brush,l2 as BrushFilled,c2 as Burger,p2 as Calendar,_2 as Camera,s2 as CameraFilled,f2 as CaretBottom,i2 as CaretLeft,d2 as CaretRight,u2 as CaretTop,h2 as Cellphone,w2 as ChatDotRound,x2 as ChatDotSquare,C2 as ChatLineRound,V2 as ChatLineSquare,g2 as ChatRound,M2 as ChatSquare,N2 as Check,z2 as Checked,H2 as Cherry,L2 as Chicken,A2 as ChromeFilled,F2 as CircleCheck,S2 as CircleCheckFilled,y2 as CircleClose,D2 as CircleCloseFilled,R2 as CirclePlus,P2 as CirclePlusFilled,T2 as Clock,I2 as Close,O2 as CloseBold,G2 as Cloudy,W2 as Coffee,U2 as CoffeeCup,K2 as Coin,Q2 as ColdDrink,J2 as Collection,Z2 as CollectionTag,X2 as Comment,$2 as Compass,oe as Connection,ae as Coordinate,ne as CopyDocument,me as Cpu,pe as CreditCard,_e as Crop,fe as DArrowLeft,ie as DArrowRight,de as DCaret,ue as DataAnalysis,he as DataBoard,ve as DataLine,xe as Delete,we as DeleteFilled,Be as DeleteLocation,ke as Dessert,Ee as Discount,ge as Dish,Ve as DishDot,Ae as Document,Me as DocumentAdd,Ne as DocumentChecked,ze as DocumentCopy,He as DocumentDelete,Le as DocumentRemove,Se as Download,qe as Drizzling,be as Edit,Fe as EditPen,Pe as Eleme,ye as ElemeFilled,Re as ElementPlus,Te as Expand,Oe as Failed,Ie as Female,Ge as Files,We as Film,Ke as Filter,Qe as Finished,je as FirstAidKit,Je as Flag,Xe as Fold,a0 as Folder,Ye as FolderAdd,$e as FolderChecked,e0 as FolderDelete,o0 as FolderOpened,t0 as FolderRemove,r0 as Food,l0 as Football,m0 as ForkSpoon,c0 as Fries,p0 as FullScreen,i0 as Goblet,s0 as GobletFull,f0 as GobletSquare,_0 as GobletSquareFull,u0 as GoldMedal,v0 as Goods,h0 as GoodsFilled,w0 as Grape,B0 as Grid,k0 as Guide,C0 as Handbag,E0 as Headset,g0 as Help,V0 as HelpFilled,N0 as Hide,z0 as Histogram,H0 as HomeFilled,L0 as HotWater,A0 as House,F0 as IceCream,S0 as IceCreamRound,q0 as IceCreamSquare,D0 as IceDrink,b0 as IceTea,y0 as InfoFilled,P0 as Iphone,R0 as Key,T0 as KnifeFork,I0 as Lightning,G0 as Link,U0 as List,W0 as Loading,j0 as Location,K0 as LocationFilled,Q0 as LocationInformation,X0 as Lock,Y0 as Lollipop,$0 as MagicStick,e1 as Magnet,o1 as Male,t1 as Management,r1 as MapLocation,l1 as Medal,m1 as Memo,c1 as Menu,_1 as Message,p1 as MessageBox,f1 as Mic,i1 as Microphone,d1 as MilkTea,u1 as Minus,h1 as Money,v1 as Monitor,x1 as Moon,B1 as MoonNight,C1 as More,k1 as MoreFilled,E1 as MostlyCloudy,g1 as Mouse,M1 as Mug,L1 as Mute,z1 as MuteNotification,A1 as NoSmoking,q1 as Notebook,D1 as Notification,b1 as Odometer,y1 as OfficeBuilding,R1 as Open,T1 as Operation,O1 as Opportunity,I1 as Orange,G1 as Paperclip,W1 as PartlyCloudy,K1 as Pear,Z1 as Phone,Q1 as PhoneFilled,$1 as Picture,j1 as PictureFilled,X1 as PictureRounded,o4 as PieChart,t4 as Place,a4 as Platform,r4 as Plus,n4 as Pointer,l4 as Position,c4 as Postcard,p4 as Pouring,s4 as Present,f4 as PriceTag,i4 as Printer,d4 as Promotion,u4 as QuartzWatch,h4 as QuestionFilled,v4 as Rank,k4 as Reading,B4 as ReadingLamp,V4 as Refresh,C4 as RefreshLeft,E4 as RefreshRight,g4 as Refrigerator,z4 as Remove,M4 as RemoveFilled,H4 as Right,L4 as ScaleToOriginal,A4 as School,S4 as Scissor,q4 as Search,F4 as Select,D4 as Sell,b4 as SemiSelect,y4 as Service,P4 as SetUp,R4 as Setting,T4 as Share,O4 as Ship,I4 as Shop,U4 as ShoppingBag,Q4 as ShoppingCart,K4 as ShoppingCartFull,Z4 as ShoppingTrolley,J4 as Smoking,X4 as Soccer,Y4 as SoldOut,oo as Sort,$4 as SortDown,eo as SortUp,to as Stamp,ro as Star,ao as StarFilled,lo as Stopwatch,mo as SuccessFilled,co as Sugar,_o as Suitcase,po as SuitcaseLine,fo as Sunny,io as Sunrise,uo as Sunset,xo as Switch,vo as SwitchButton,Bo as SwitchFilled,ko as TakeawayBox,Co as Ticket,Eo as Tickets,Vo as Timer,Mo as ToiletPaper,No as Tools,So as Top,Ho as TopLeft,Ao as TopRight,qo as TrendCharts,Do as Trophy,Fo as TrophyBase,yo as TurnOff,Po as Umbrella,To as Unlock,Io as Upload,Oo as UploadFilled,Uo as User,Go as UserFilled,Wo as Van,Qo as VideoCamera,Ko as VideoCameraFilled,Zo as VideoPause,jo as VideoPlay,Jo as View,Yo as Wallet,Xo as WalletFilled,$o as WarnTriangleFilled,ot as Warning,et as WarningFilled,tt as Watch,at as Watermelon,rt as WindPower,nt as ZoomIn,lt as ZoomOut,gG as default,z as icons};
