/**
 * 基础组件Composable
 * 提供组件的通用功能和状态管理
 */

import { computed, ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import type { BaseComponentProps, ComponentSize, ValidationRule } from '@/types/component'

/**
 * 基础组件Composable
 */
export function useBaseComponent(props: BaseComponentProps) {
  // 组件实例引用
  const componentRef = ref<HTMLElement>()
  
  // 组件状态
  const state = reactive({
    loading: false,
    disabled: false,
    visible: true,
    focused: false,
    hovered: false
  })

  // 计算属性
  const componentId = computed(() => props.id || `component-${Math.random().toString(36).substr(2, 9)}`)
  
  const componentClass = computed(() => {
    const classes: string[] = []
    
    if (props.class) {
      if (typeof props.class === 'string') {
        classes.push(props.class)
      } else if (Array.isArray(props.class)) {
        classes.push(...props.class)
      } else {
        Object.entries(props.class).forEach(([key, value]) => {
          if (value) classes.push(key)
        })
      }
    }
    
    // 添加状态类
    if (state.loading) classes.push('is-loading')
    if (state.disabled || props.disabled) classes.push('is-disabled')
    if (state.focused) classes.push('is-focused')
    if (state.hovered) classes.push('is-hovered')
    if (props.size) classes.push(`is-${props.size}`)
    
    return classes.join(' ')
  })
  
  const componentStyle = computed(() => {
    if (typeof props.style === 'string') {
      return props.style
    }
    return props.style || {}
  })
  
  const isDisabled = computed(() => state.disabled || props.disabled || state.loading)
  
  const testAttributes = computed(() => {
    const attrs: Record<string, string> = {}
    if (props.testId) {
      attrs['data-testid'] = props.testId
    }
    return attrs
  })

  // 方法
  const setLoading = (loading: boolean) => {
    state.loading = loading
  }
  
  const setDisabled = (disabled: boolean) => {
    state.disabled = disabled
  }
  
  const setVisible = (visible: boolean) => {
    state.visible = visible
  }
  
  const focus = async () => {
    await nextTick()
    if (componentRef.value) {
      const focusableElement = componentRef.value.querySelector('input, textarea, select, button, [tabindex]') as HTMLElement
      if (focusableElement) {
        focusableElement.focus()
      } else {
        componentRef.value.focus()
      }
    }
  }
  
  const blur = async () => {
    await nextTick()
    if (componentRef.value) {
      const focusedElement = componentRef.value.querySelector(':focus') as HTMLElement
      if (focusedElement) {
        focusedElement.blur()
      }
    }
  }

  // 事件处理
  const handleFocus = (event: FocusEvent) => {
    state.focused = true
  }
  
  const handleBlur = (event: FocusEvent) => {
    state.focused = false
  }
  
  const handleMouseEnter = (event: MouseEvent) => {
    state.hovered = true
  }
  
  const handleMouseLeave = (event: MouseEvent) => {
    state.hovered = false
  }

  return {
    // 引用
    componentRef,
    
    // 状态
    state,
    
    // 计算属性
    componentId,
    componentClass,
    componentStyle,
    isDisabled,
    testAttributes,
    
    // 方法
    setLoading,
    setDisabled,
    setVisible,
    focus,
    blur,
    
    // 事件处理
    handleFocus,
    handleBlur,
    handleMouseEnter,
    handleMouseLeave
  }
}

/**
 * 表单组件Composable
 */
export function useFormComponent(props: any, emit: any) {
  const baseComponent = useBaseComponent(props)
  
  // 表单状态
  const formState = reactive({
    value: props.modelValue,
    error: '',
    validating: false,
    touched: false,
    dirty: false
  })
  
  // 计算属性
  const hasError = computed(() => !!formState.error)
  const isRequired = computed(() => props.required || (props.rules && props.rules.some((rule: ValidationRule) => rule.required)))
  
  // 验证方法
  const validate = async (): Promise<boolean> => {
    if (!props.rules || props.rules.length === 0) {
      return true
    }
    
    formState.validating = true
    formState.error = ''
    
    try {
      for (const rule of props.rules) {
        const isValid = await validateRule(rule, formState.value)
        if (!isValid) {
          formState.error = rule.message || '验证失败'
          return false
        }
      }
      return true
    } finally {
      formState.validating = false
    }
  }
  
  const validateRule = (rule: ValidationRule, value: any): Promise<boolean> => {
    return new Promise((resolve) => {
      // 必填验证
      if (rule.required && (value === undefined || value === null || value === '')) {
        resolve(false)
        return
      }
      
      // 如果值为空且不是必填，则通过验证
      if (!rule.required && (value === undefined || value === null || value === '')) {
        resolve(true)
        return
      }
      
      // 类型验证
      if (rule.type) {
        switch (rule.type) {
          case 'email':
            resolve(/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value))
            return
          case 'url':
            resolve(/^https?:\/\/.+/.test(value))
            return
          case 'number':
            resolve(!isNaN(Number(value)))
            return
          case 'integer':
            resolve(Number.isInteger(Number(value)))
            return
          case 'array':
            resolve(Array.isArray(value))
            return
        }
      }
      
      // 长度验证
      if (rule.min !== undefined && value.length < rule.min) {
        resolve(false)
        return
      }
      
      if (rule.max !== undefined && value.length > rule.max) {
        resolve(false)
        return
      }
      
      if (rule.len !== undefined && value.length !== rule.len) {
        resolve(false)
        return
      }
      
      // 正则验证
      if (rule.pattern && !rule.pattern.test(value)) {
        resolve(false)
        return
      }
      
      // 自定义验证
      if (rule.validator) {
        rule.validator(rule, value, (error) => {
          resolve(!error)
        })
        return
      }
      
      resolve(true)
    })
  }
  
  const clearValidation = () => {
    formState.error = ''
    formState.validating = false
  }
  
  // 值更新
  const updateValue = (newValue: any) => {
    const oldValue = formState.value
    formState.value = newValue
    formState.dirty = true
    formState.touched = true
    
    emit('update:modelValue', newValue)
    emit('change', newValue, oldValue)
    
    // 自动验证
    if (props.rules && formState.touched) {
      nextTick(() => {
        validate()
      })
    }
  }
  
  // 监听外部值变化
  const handleExternalValueChange = (newValue: any) => {
    if (newValue !== formState.value) {
      formState.value = newValue
    }
  }
  
  // 事件处理
  const handleInput = (event: Event) => {
    const target = event.target as HTMLInputElement
    updateValue(target.value)
    emit('input', target.value)
  }
  
  const handleChange = (event: Event) => {
    const target = event.target as HTMLInputElement
    updateValue(target.value)
  }
  
  const handleBlur = (event: FocusEvent) => {
    baseComponent.handleBlur(event)
    formState.touched = true
    emit('blur', event)
    
    // 失焦时验证
    if (props.rules && formState.touched) {
      validate()
    }
  }

  return {
    ...baseComponent,
    
    // 表单状态
    formState,
    
    // 计算属性
    hasError,
    isRequired,
    
    // 方法
    validate,
    clearValidation,
    updateValue,
    handleExternalValueChange,
    
    // 事件处理
    handleInput,
    handleChange,
    handleBlur: handleBlur as any
  }
}
