package com.linkapi.controller;

import com.linkapi.common.Result;
import com.linkapi.entity.User;
import com.linkapi.service.UserService;
import com.linkapi.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 认证控制器
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@CrossOrigin(origins = "*")
public class AuthController {

    @Autowired
    private UserService userService;

    /**
     * 用户登录
     *
     * @param loginRequest 登录请求
     * @param request HTTP请求
     * @return 登录结果
     */
    @PostMapping("/login")
    public Result<Map<String, Object>> login(@RequestBody Map<String, String> loginRequest, 
                                            HttpServletRequest request) {
        String username = loginRequest.get("username");
        String password = loginRequest.get("password");
        
        log.info("用户登录请求: username={}", username);
        
        try {
            // 获取客户端IP
            String loginIp = getClientIp(request);
            
            // 用户登录验证
            User user = userService.login(username, password, loginIp);
            
            // 生成JWT token
            String token = JwtUtil.generateToken(user.getId(), user.getUsername(), user.getRoleCodes());
            
            Map<String, Object> data = new HashMap<>();
            data.put("token", "Bearer " + token);
            data.put("username", user.getUsername());
            data.put("name", user.getRealName() != null ? user.getRealName() : user.getUsername());
            data.put("avatar", user.getAvatar() != null ? user.getAvatar() : "/avatar/default.png");
            data.put("roles", user.getRoleCodes());
            data.put("permissions", user.getPermissionCodes());
            data.put("isAdmin", user.getIsAdmin());
            
            log.info("用户登录成功: username={}, userId={}", username, user.getId());
            return Result.success("登录成功", data);
        } catch (Exception e) {
            log.warn("用户登录失败: username={}, error={}", username, e.getMessage());
            return Result.error(e.getMessage());
        }
    }

    /**
     * 获取用户信息
     *
     * @param request HTTP请求
     * @return 用户信息
     */
    @GetMapping("/getInfo")
    public Result<Map<String, Object>> getInfo(HttpServletRequest request) {
        log.info("获取用户信息请求");
        
        try {
            // 从请求头获取token
            String token = getTokenFromRequest(request);
            if (token == null) {
                return Result.error("未登录或登录已过期");
            }
            
            // 验证token并获取用户信息
            Long userId = JwtUtil.getUserIdFromToken(token);
            String username = JwtUtil.getUsernameFromToken(token);
            
            if (userId == null || username == null) {
                return Result.error("Token无效");
            }
            
            // 查询用户详细信息
            User user = userService.findByUsernameWithRoles(username);
            if (user == null) {
                return Result.error("用户不存在");
            }
            
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", user.getId());
            userInfo.put("username", user.getUsername());
            userInfo.put("name", user.getRealName() != null ? user.getRealName() : user.getUsername());
            userInfo.put("email", user.getEmail());
            userInfo.put("phone", user.getPhone());
            userInfo.put("avatar", user.getAvatar() != null ? user.getAvatar() : "/avatar/default.png");
            userInfo.put("status", user.getStatus());
            userInfo.put("roles", user.getRoleCodes());
            userInfo.put("permissions", user.getPermissionCodes());
            userInfo.put("isAdmin", user.getIsAdmin());
            userInfo.put("lastLoginTime", user.getLastLoginTime());
            userInfo.put("lastLoginIp", user.getLastLoginIp());
            
            return Result.success("获取用户信息成功", userInfo);
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return Result.error("获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 用户登出
     *
     * @param request HTTP请求
     * @return 登出结果
     */
    @PostMapping("/logout")
    public Result<Void> logout(HttpServletRequest request) {
        log.info("用户登出请求");
        
        try {
            // 从请求头获取token
            String token = getTokenFromRequest(request);
            if (token != null) {
                String username = JwtUtil.getUsernameFromToken(token);
                log.info("用户登出成功: username={}", username);
            }
            
            // 这里可以实现token黑名单机制
            // tokenBlacklistService.addToBlacklist(token);
            
            return Result.success("登出成功", null);
        } catch (Exception e) {
            log.error("用户登出失败", e);
            return Result.error("登出失败: " + e.getMessage());
        }
    }

    /**
     * 获取验证码
     *
     * @return 验证码信息
     */
    @GetMapping("/captchaImage")
    public Result<Map<String, Object>> getCaptcha() {
        log.info("获取验证码请求");
        
        // 这里可以集成真实的验证码生成库，如kaptcha
        Map<String, Object> captcha = new HashMap<>();
        captcha.put("uuid", java.util.UUID.randomUUID().toString());
        captcha.put("img", "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==");
        captcha.put("captchaEnabled", false); // 暂时禁用验证码
        
        return Result.success("获取验证码成功", captcha);
    }

    /**
     * 从请求中获取token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0];
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
