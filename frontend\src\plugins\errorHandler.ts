/**
 * 全局错误处理插件
 * 统一处理Vue应用中的错误
 */

import type { App } from 'vue'
import { h } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import { useErrorHandler, ErrorType, ErrorLevel } from '@/composables/useErrorHandler'

/**
 * 错误处理插件配置
 */
export interface ErrorHandlerPluginOptions {
  /**
   * 是否在控制台输出错误
   */
  logToConsole?: boolean
  
  /**
   * 是否显示错误消息
   */
  showMessage?: boolean
  
  /**
   * 是否显示错误通知
   */
  showNotification?: boolean
  
  /**
   * 是否上报错误到服务器
   */
  reportToServer?: boolean
  
  /**
   * 错误上报接口
   */
  reportUrl?: string
  
  /**
   * 过滤器函数，返回false的错误不会被处理
   */
  filter?: (error: Error, errorInfo?: any) => boolean
  
  /**
   * 自定义错误处理函数
   */
  onError?: (error: Error, errorInfo?: any) => void
}

/**
 * 默认配置
 */
const defaultOptions: ErrorHandlerPluginOptions = {
  logToConsole: true,
  showMessage: true,
  showNotification: false,
  reportToServer: false,
  filter: () => true
}

/**
 * 错误处理插件
 */
export const errorHandlerPlugin = {
  install(app: App, options: ErrorHandlerPluginOptions = {}) {
    const finalOptions = { ...defaultOptions, ...options }
    
    // 创建错误处理器实例
    const { handleError, parseError } = useErrorHandler({
      showMessage: finalOptions.showMessage,
      showNotification: finalOptions.showNotification,
      logError: finalOptions.logToConsole
    })
    
    // Vue错误处理
    app.config.errorHandler = (error: Error, instance, info) => {
      // 应用过滤器
      if (finalOptions.filter && !finalOptions.filter(error, { instance, info })) {
        return
      }
      
      // 解析错误信息
      const errorInfo = parseError(error)
      
      // 控制台输出
      if (finalOptions.logToConsole) {
        console.group('🚨 Vue Error Handler')
        console.error('Error:', error)
        console.error('Component:', instance)
        console.error('Info:', info)
        console.error('Parsed Error:', errorInfo)
        console.groupEnd()
      }
      
      // 处理错误
      handleError(error, `Vue Error: ${info}`)
      
      // 上报错误
      if (finalOptions.reportToServer && finalOptions.reportUrl) {
        reportError(error, { instance, info, errorInfo }, finalOptions.reportUrl)
      }
      
      // 自定义错误处理
      if (finalOptions.onError) {
        finalOptions.onError(error, { instance, info, errorInfo })
      }
    }
    
    // 全局未捕获的Promise错误
    window.addEventListener('unhandledrejection', (event) => {
      const error = event.reason
      
      // 应用过滤器
      if (finalOptions.filter && !finalOptions.filter(error, { type: 'unhandledrejection' })) {
        return
      }
      
      // 解析错误信息
      const errorInfo = parseError(error)
      
      // 控制台输出
      if (finalOptions.logToConsole) {
        console.group('🚨 Unhandled Promise Rejection')
        console.error('Reason:', error)
        console.error('Event:', event)
        console.error('Parsed Error:', errorInfo)
        console.groupEnd()
      }
      
      // 处理错误
      handleError(error, 'Unhandled Promise Rejection')
      
      // 上报错误
      if (finalOptions.reportToServer && finalOptions.reportUrl) {
        reportError(error, { type: 'unhandledrejection', event, errorInfo }, finalOptions.reportUrl)
      }
      
      // 自定义错误处理
      if (finalOptions.onError) {
        finalOptions.onError(error, { type: 'unhandledrejection', event, errorInfo })
      }
      
      // 阻止默认行为（防止在控制台输出）
      event.preventDefault()
    })
    
    // 全局JavaScript错误
    window.addEventListener('error', (event) => {
      const error = event.error || new Error(event.message)
      
      // 应用过滤器
      if (finalOptions.filter && !finalOptions.filter(error, { type: 'javascript', event })) {
        return
      }
      
      // 解析错误信息
      const errorInfo = parseError(error)
      
      // 控制台输出
      if (finalOptions.logToConsole) {
        console.group('🚨 JavaScript Error')
        console.error('Error:', error)
        console.error('Event:', event)
        console.error('Parsed Error:', errorInfo)
        console.groupEnd()
      }
      
      // 处理错误
      handleError(error, 'JavaScript Error')
      
      // 上报错误
      if (finalOptions.reportToServer && finalOptions.reportUrl) {
        reportError(error, { type: 'javascript', event, errorInfo }, finalOptions.reportUrl)
      }
      
      // 自定义错误处理
      if (finalOptions.onError) {
        finalOptions.onError(error, { type: 'javascript', event, errorInfo })
      }
    })
    
    // 资源加载错误
    window.addEventListener('error', (event) => {
      const target = event.target as HTMLElement
      
      // 只处理资源加载错误
      if (target && target !== window && (target.tagName === 'IMG' || target.tagName === 'SCRIPT' || target.tagName === 'LINK')) {
        const error = new Error(`Resource load failed: ${target.tagName} - ${(target as any).src || (target as any).href}`)
        
        // 应用过滤器
        if (finalOptions.filter && !finalOptions.filter(error, { type: 'resource', target })) {
          return
        }
        
        // 解析错误信息
        const errorInfo = parseError(error)
        errorInfo.type = ErrorType.NETWORK
        errorInfo.level = ErrorLevel.MEDIUM
        
        // 控制台输出
        if (finalOptions.logToConsole) {
          console.group('🚨 Resource Load Error')
          console.error('Target:', target)
          console.error('Error:', error)
          console.error('Parsed Error:', errorInfo)
          console.groupEnd()
        }
        
        // 处理错误
        handleError(error, 'Resource Load Error')
        
        // 上报错误
        if (finalOptions.reportToServer && finalOptions.reportUrl) {
          reportError(error, { type: 'resource', target, errorInfo }, finalOptions.reportUrl)
        }
        
        // 自定义错误处理
        if (finalOptions.onError) {
          finalOptions.onError(error, { type: 'resource', target, errorInfo })
        }
      }
    }, true) // 使用捕获阶段
    
    // 提供全局错误处理方法
    app.config.globalProperties.$handleError = handleError
    app.provide('errorHandler', { handleError, parseError })
  }
}

/**
 * 上报错误到服务器
 */
async function reportError(error: Error, context: any, reportUrl: string) {
  try {
    const errorReport = {
      message: error.message,
      stack: error.stack,
      name: error.name,
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      context: JSON.stringify(context, null, 2)
    }
    
    await fetch(reportUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(errorReport)
    })
  } catch (reportError) {
    console.error('Failed to report error:', reportError)
  }
}

/**
 * 创建错误边界组件
 */
export function createErrorBoundary(fallbackComponent?: any) {
  return {
    name: 'ErrorBoundary',
    data() {
      return {
        hasError: false,
        error: null as Error | null
      }
    },
    errorCaptured(error: Error, instance: any, info: string) {
      this.hasError = true
      this.error = error
      
      // 处理错误
      const { handleError } = useErrorHandler()
      handleError(error, `Error Boundary: ${info}`)
      
      // 阻止错误继续传播
      return false
    },
    render() {
      if (this.hasError) {
        if (fallbackComponent) {
          return h(fallbackComponent, { error: this.error })
        }
        
        return h('div', {
          class: 'error-boundary',
          style: {
            padding: '20px',
            textAlign: 'center',
            color: '#f56c6c'
          }
        }, [
          h('h3', '出现了一些问题'),
          h('p', '页面遇到错误，请刷新页面重试'),
          h('button', {
            onClick: () => window.location.reload(),
            style: {
              padding: '8px 16px',
              backgroundColor: '#409eff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }
          }, '刷新页面')
        ])
      }
      
      return this.$slots.default?.()
    }
  }
}

export default errorHandlerPlugin
