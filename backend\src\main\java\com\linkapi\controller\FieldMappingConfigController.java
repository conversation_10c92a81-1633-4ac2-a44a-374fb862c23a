package com.linkapi.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.linkapi.common.Result;
import com.linkapi.dto.FieldMappingConfigDTO;
import com.linkapi.entity.FieldMappingConfig;
import com.linkapi.service.interfaces.FieldMappingConfigServiceInterface;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * 字段映射配置控制器
 * 
 * <AUTHOR> API Team
 */
@Slf4j
@RestController
@RequestMapping("/api/field-mapping")
@RequiredArgsConstructor
@Validated
public class FieldMappingConfigController {

    private final FieldMappingConfigServiceInterface fieldMappingConfigService;

    // ==================== 配置管理 ====================

    @PostMapping("/configs")
    public Result<FieldMappingConfigDTO> createConfig(@Valid @RequestBody FieldMappingConfigDTO configDTO) {
        try {
            FieldMappingConfigDTO result = fieldMappingConfigService.createConfig(configDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("创建字段映射配置失败", e);
            return Result.error("创建字段映射配置失败：" + e.getMessage());
        }
    }

    @PutMapping("/configs/{id}")
    public Result<FieldMappingConfigDTO> updateConfig(@PathVariable Long id, @Valid @RequestBody FieldMappingConfigDTO configDTO) {
        try {
            configDTO.setId(id);
            FieldMappingConfigDTO result = fieldMappingConfigService.updateConfig(configDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("更新字段映射配置失败，ID: {}", id, e);
            return Result.error("更新字段映射配置失败：" + e.getMessage());
        }
    }

    @DeleteMapping("/configs/{id}")
    public Result<Boolean> deleteConfig(@PathVariable Long id) {
        try {
            boolean result = fieldMappingConfigService.deleteConfig(id);
            return Result.success(result);
        } catch (Exception e) {
            log.error("删除字段映射配置失败，ID: {}", id, e);
            return Result.error("删除字段映射配置失败：" + e.getMessage());
        }
    }

    @GetMapping("/configs/{id}")
    public Result<FieldMappingConfigDTO> getConfigDetail(@PathVariable Long id) {
        try {
            FieldMappingConfigDTO result = fieldMappingConfigService.getConfigDetail(id);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取字段映射配置详情失败，ID: {}", id, e);
            return Result.error("获取字段映射配置详情失败：" + e.getMessage());
        }
    }

    @GetMapping("/configs")
    public Result<IPage<FieldMappingConfigDTO.ConfigPreviewDTO>> getConfigPage(@ModelAttribute FieldMappingConfigDTO.ConfigQueryDTO queryDTO) {
        try {
            IPage<FieldMappingConfigDTO.ConfigPreviewDTO> result = fieldMappingConfigService.getConfigPage(queryDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("分页查询字段映射配置失败", e);
            return Result.error("分页查询字段映射配置失败：" + e.getMessage());
        }
    }

    // ==================== 配置查询 ====================

    @GetMapping("/configs/by-type-module")
    public Result<List<FieldMappingConfig>> getConfigsByTypeAndModule(@RequestParam FieldMappingConfig.ConfigType configType, @RequestParam String moduleType) {
        try {
            List<FieldMappingConfig> result = fieldMappingConfigService.getConfigsByTypeAndModule(configType, moduleType);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据类型和模块查询配置失败", e);
            return Result.error("查询配置失败：" + e.getMessage());
        }
    }

    @GetMapping("/configs/by-source-target")
    public Result<List<FieldMappingConfig>> getConfigsBySourceAndTarget(@RequestParam String sourceSystem, @RequestParam String targetSystem) {
        try {
            List<FieldMappingConfig> result = fieldMappingConfigService.getConfigsBySourceAndTarget(sourceSystem, targetSystem);
            return Result.success(result);
        } catch (Exception e) {
            log.error("根据源系统和目标系统查询配置失败", e);
            return Result.error("查询配置失败：" + e.getMessage());
        }
    }

    @GetMapping("/configs/default")
    public Result<FieldMappingConfig> getDefaultConfig(@RequestParam FieldMappingConfig.ConfigType configType, @RequestParam String moduleType) {
        try {
            FieldMappingConfig result = fieldMappingConfigService.getDefaultConfig(configType, moduleType);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取默认配置失败", e);
            return Result.error("获取默认配置失败：" + e.getMessage());
        }
    }

    @GetMapping("/configs/enabled")
    public Result<List<FieldMappingConfig>> getEnabledConfigs() {
        try {
            List<FieldMappingConfig> result = fieldMappingConfigService.getEnabledConfigs();
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取启用配置列表失败", e);
            return Result.error("获取启用配置列表失败：" + e.getMessage());
        }
    }

    // ==================== 配置操作 ====================

    @PostMapping("/configs/{id}/publish")
    public Result<Boolean> publishConfig(@PathVariable Long id) {
        try {
            boolean result = fieldMappingConfigService.publishConfig(id);
            return Result.success(result);
        } catch (Exception e) {
            log.error("发布配置失败，ID: {}", id, e);
            return Result.error("发布配置失败：" + e.getMessage());
        }
    }

    @PostMapping("/configs/{id}/disable")
    public Result<Boolean> disableConfig(@PathVariable Long id) {
        try {
            boolean result = fieldMappingConfigService.disableConfig(id);
            return Result.success(result);
        } catch (Exception e) {
            log.error("停用配置失败，ID: {}", id, e);
            return Result.error("停用配置失败：" + e.getMessage());
        }
    }

    @PostMapping("/configs/{id}/enable")
    public Result<Boolean> enableConfig(@PathVariable Long id) {
        try {
            boolean result = fieldMappingConfigService.enableConfig(id);
            return Result.success(result);
        } catch (Exception e) {
            log.error("启用配置失败，ID: {}", id, e);
            return Result.error("启用配置失败：" + e.getMessage());
        }
    }

    @PostMapping("/configs/{id}/set-default")
    public Result<Boolean> setDefaultConfig(@PathVariable Long id) {
        try {
            boolean result = fieldMappingConfigService.setDefaultConfig(id);
            return Result.success(result);
        } catch (Exception e) {
            log.error("设置默认配置失败，ID: {}", id, e);
            return Result.error("设置默认配置失败：" + e.getMessage());
        }
    }

    @PostMapping("/configs/{id}/copy")
    public Result<FieldMappingConfigDTO> copyConfig(@PathVariable Long id, @RequestParam String newConfigName) {
        try {
            FieldMappingConfigDTO result = fieldMappingConfigService.copyConfig(id, newConfigName);
            return Result.success(result);
        } catch (Exception e) {
            log.error("复制配置失败，ID: {}, newName: {}", id, newConfigName, e);
            return Result.error("复制配置失败：" + e.getMessage());
        }
    }

    // ==================== 字段映射应用 ====================

    @PostMapping("/configs/{id}/apply")
    public Result<Map<String, Object>> applyFieldMapping(@PathVariable Long id, @RequestBody Map<String, Object> sourceData) {
        try {
            Map<String, Object> result = fieldMappingConfigService.applyFieldMapping(id, sourceData);
            return Result.success(result);
        } catch (Exception e) {
            log.error("应用字段映射失败，configId: {}", id, e);
            return Result.error("应用字段映射失败：" + e.getMessage());
        }
    }

    @PostMapping("/configs/{id}/batch-apply")
    public Result<List<Map<String, Object>>> batchApplyFieldMapping(@PathVariable Long id, @RequestBody List<Map<String, Object>> sourceDataList) {
        try {
            List<Map<String, Object>> result = fieldMappingConfigService.batchApplyFieldMapping(id, sourceDataList);
            return Result.success(result);
        } catch (Exception e) {
            log.error("批量应用字段映射失败，configId: {}", id, e);
            return Result.error("批量应用字段映射失败：" + e.getMessage());
        }
    }

    @PostMapping("/configs/{id}/validate")
    public Result<FieldMappingConfigServiceInterface.ValidationResult> validateData(@PathVariable Long id, @RequestBody Map<String, Object> data) {
        try {
            FieldMappingConfigServiceInterface.ValidationResult result = fieldMappingConfigService.validateData(id, data);
            return Result.success(result);
        } catch (Exception e) {
            log.error("验证数据失败，configId: {}", id, e);
            return Result.error("验证数据失败：" + e.getMessage());
        }
    }

    // ==================== 配置导入导出 ====================

    @GetMapping("/configs/{id}/export")
    public void exportConfig(@PathVariable Long id, HttpServletResponse response) {
        try {
            String configJson = fieldMappingConfigService.exportConfig(id);
            
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=field_mapping_config_" + id + ".json");
            
            response.getWriter().write(configJson);
            response.getWriter().flush();
            
        } catch (Exception e) {
            log.error("导出配置失败，ID: {}", id, e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("{\"error\":\"导出配置失败：" + e.getMessage() + "\"}");
            } catch (IOException ioException) {
                log.error("写入错误响应失败", ioException);
            }
        }
    }

    @PostMapping("/configs/import")
    public Result<FieldMappingConfigDTO> importConfig(@RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return Result.error("文件不能为空");
            }
            
            String configJson = new String(file.getBytes(), StandardCharsets.UTF_8);
            FieldMappingConfigDTO result = fieldMappingConfigService.importConfig(configJson);
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("导入配置失败", e);
            return Result.error("导入配置失败：" + e.getMessage());
        }
    }

    @PostMapping("/configs/batch-export")
    public void batchExportConfigs(@RequestBody @NotEmpty List<Long> ids, HttpServletResponse response) {
        try {
            String configsJson = fieldMappingConfigService.batchExportConfigs(ids);
            
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=field_mapping_configs_batch.json");
            
            response.getWriter().write(configsJson);
            response.getWriter().flush();
            
        } catch (Exception e) {
            log.error("批量导出配置失败，IDs: {}", ids, e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("{\"error\":\"批量导出配置失败：" + e.getMessage() + "\"}");
            } catch (IOException ioException) {
                log.error("写入错误响应失败", ioException);
            }
        }
    }

    @PostMapping("/configs/batch-import")
    public Result<FieldMappingConfigServiceInterface.ImportResult> batchImportConfigs(@RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return Result.error("文件不能为空");
            }
            
            String configsJson = new String(file.getBytes(), StandardCharsets.UTF_8);
            FieldMappingConfigServiceInterface.ImportResult result = fieldMappingConfigService.batchImportConfigs(configsJson);
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("批量导入配置失败", e);
            return Result.error("批量导入配置失败：" + e.getMessage());
        }
    }

    // ==================== 统计信息 ====================

    @GetMapping("/statistics")
    public Result<FieldMappingConfigServiceInterface.ConfigStatistics> getConfigStatistics() {
        try {
            FieldMappingConfigServiceInterface.ConfigStatistics result = fieldMappingConfigService.getConfigStatistics();
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取配置统计信息失败", e);
            return Result.error("获取配置统计信息失败：" + e.getMessage());
        }
    }

    @GetMapping("/configs/{id}/field-type-statistics")
    public Result<List<FieldMappingConfigServiceInterface.FieldTypeStatistics>> getFieldTypeStatistics(@PathVariable Long id) {
        try {
            List<FieldMappingConfigServiceInterface.FieldTypeStatistics> result = fieldMappingConfigService.getFieldTypeStatistics(id);
            return Result.success(result);
        } catch (Exception e) {
            log.error("获取字段类型统计失败，configId: {}", id, e);
            return Result.error("获取字段类型统计失败：" + e.getMessage());
        }
    }
}
