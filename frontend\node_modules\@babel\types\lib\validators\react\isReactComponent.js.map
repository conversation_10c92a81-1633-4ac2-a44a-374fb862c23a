{"version": 3, "names": ["_buildMatchMemberExpression", "require", "isReactComponent", "buildMatchMemberExpression", "_default", "exports", "default"], "sources": ["../../../src/validators/react/isReactComponent.ts"], "sourcesContent": ["import buildMatchMemberExpression from \"../buildMatchMemberExpression.ts\";\n\nconst isReactComponent = buildMatchMemberExpression(\"React.Component\");\n\nexport default isReactComponent;\n"], "mappings": ";;;;;;AAAA,IAAAA,2BAAA,GAAAC,OAAA;AAEA,MAAMC,gBAAgB,GAAG,IAAAC,mCAA0B,EAAC,iBAAiB,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAExDJ,gBAAgB", "ignoreList": []}