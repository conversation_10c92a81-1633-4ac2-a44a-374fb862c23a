-- Link API 数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS `link_api` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `link_api`;

-- 1. 单据主表 (documents)
CREATE TABLE `documents` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `document_no` VARCHAR(100) NOT NULL COMMENT '单据编号（外部单号）',
    `document_type` VARCHAR(50) NOT NULL COMMENT '单据类型：SALE_OUT-销售出库，SALE_RETURN-退货，PURCHASE_IN-采购入库，STOCK_TRANSFER-调拨',
    `document_date` DATETIME NOT NULL COMMENT '单据日期',
    `customer_code` VARCHAR(100) COMMENT '客户编码',
    `customer_name` VARCHAR(200) COMMENT '客户名称',
    `warehouse_code` VARCHAR(100) COMMENT '仓库编码',
    `warehouse_name` VARCHAR(200) COMMENT '仓库名称',
    `total_amount` DECIMAL(18,4) DEFAULT 0.00 COMMENT '单据总金额',
    `total_quantity` DECIMAL(18,4) DEFAULT 0.00 COMMENT '单据总数量',
    `remark` TEXT COMMENT '备注',
    `status` VARCHAR(50) NOT NULL DEFAULT 'PENDING' COMMENT '状态：PENDING-待校验，VALIDATED-已校验，PUSHING-推送中，SUCCESS-成功，FAILED-失败',
    `kingdee_document_no` VARCHAR(100) COMMENT '金蝶单据编号',
    `error_message` TEXT COMMENT '错误信息',
    `retry_count` INT DEFAULT 0 COMMENT '重试次数',
    `last_push_time` DATETIME COMMENT '最后推送时间',
    `source_file` VARCHAR(500) COMMENT '来源文件',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by` VARCHAR(100) COMMENT '创建人',
    `updated_by` VARCHAR(100) COMMENT '更新人',
    `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标识',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_document_no_type` (`document_no`, `document_type`, `deleted`),
    KEY `idx_document_type` (`document_type`),
    KEY `idx_status` (`status`),
    KEY `idx_document_date` (`document_date`),
    KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='单据主表';

-- 2. 单据明细表 (document_details)
CREATE TABLE `document_details` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `document_id` BIGINT NOT NULL COMMENT '单据主表ID',
    `line_no` INT NOT NULL COMMENT '行号',
    `product_code` VARCHAR(100) NOT NULL COMMENT '商品编码',
    `product_name` VARCHAR(500) COMMENT '商品名称',
    `specification` VARCHAR(200) COMMENT '规格型号',
    `unit` VARCHAR(50) COMMENT '单位',
    `quantity` DECIMAL(18,4) NOT NULL COMMENT '数量',
    `unit_price` DECIMAL(18,4) NOT NULL COMMENT '单价',
    `amount` DECIMAL(18,4) NOT NULL COMMENT '金额',
    `batch_no` VARCHAR(100) COMMENT '批次号',
    `remark` VARCHAR(500) COMMENT '备注',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标识',
    PRIMARY KEY (`id`),
    KEY `idx_document_id` (`document_id`),
    KEY `idx_product_code` (`product_code`),
    CONSTRAINT `fk_document_details_document_id` FOREIGN KEY (`document_id`) REFERENCES `documents` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='单据明细表';

-- 3. 商品映射表 (product_mapping)
CREATE TABLE `product_mapping` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `external_code` VARCHAR(100) NOT NULL COMMENT '外部系统商品编码（旺店通SKU）',
    `external_name` VARCHAR(500) COMMENT '外部系统商品名称',
    `kingdee_code` VARCHAR(100) NOT NULL COMMENT '金蝶存货编码',
    `kingdee_name` VARCHAR(500) COMMENT '金蝶存货名称',
    `specification` VARCHAR(200) COMMENT '规格型号',
    `unit` VARCHAR(50) COMMENT '单位',
    `status` VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-有效，INACTIVE-无效',
    `remark` VARCHAR(500) COMMENT '备注',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by` VARCHAR(100) COMMENT '创建人',
    `updated_by` VARCHAR(100) COMMENT '更新人',
    `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标识',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_external_code` (`external_code`, `deleted`),
    UNIQUE KEY `uk_kingdee_code` (`kingdee_code`, `deleted`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品映射表';

-- 4. 客户映射表 (customer_mapping)
CREATE TABLE `customer_mapping` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `external_code` VARCHAR(100) NOT NULL COMMENT '外部系统客户编码（旺店通店铺）',
    `external_name` VARCHAR(200) COMMENT '外部系统客户名称',
    `kingdee_code` VARCHAR(100) NOT NULL COMMENT '金蝶客户编码',
    `kingdee_name` VARCHAR(200) COMMENT '金蝶客户名称',
    `customer_type` VARCHAR(50) COMMENT '客户类型',
    `status` VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-有效，INACTIVE-无效',
    `remark` VARCHAR(500) COMMENT '备注',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by` VARCHAR(100) COMMENT '创建人',
    `updated_by` VARCHAR(100) COMMENT '更新人',
    `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标识',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_external_code` (`external_code`, `deleted`),
    UNIQUE KEY `uk_kingdee_code` (`kingdee_code`, `deleted`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户映射表';

-- 5. 仓库映射表 (warehouse_mapping)
CREATE TABLE `warehouse_mapping` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `external_code` VARCHAR(100) NOT NULL COMMENT '外部系统仓库编码',
    `external_name` VARCHAR(200) COMMENT '外部系统仓库名称',
    `kingdee_code` VARCHAR(100) NOT NULL COMMENT '金蝶仓库编码',
    `kingdee_name` VARCHAR(200) COMMENT '金蝶仓库名称',
    `warehouse_type` VARCHAR(50) COMMENT '仓库类型',
    `status` VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-有效，INACTIVE-无效',
    `remark` VARCHAR(500) COMMENT '备注',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by` VARCHAR(100) COMMENT '创建人',
    `updated_by` VARCHAR(100) COMMENT '更新人',
    `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标识',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_external_code` (`external_code`, `deleted`),
    UNIQUE KEY `uk_kingdee_code` (`kingdee_code`, `deleted`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='仓库映射表';

-- 6. 金蝶基础资料表 (kingdee_master_data)
CREATE TABLE `kingdee_master_data` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `data_type` VARCHAR(50) NOT NULL COMMENT '数据类型：PRODUCT-商品，CUSTOMER-客户，WAREHOUSE-仓库',
    `code` VARCHAR(100) NOT NULL COMMENT '编码',
    `name` VARCHAR(500) COMMENT '名称',
    `specification` VARCHAR(200) COMMENT '规格型号（商品专用）',
    `unit` VARCHAR(50) COMMENT '单位（商品专用）',
    `category` VARCHAR(100) COMMENT '分类',
    `status` VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-有效，INACTIVE-无效',
    `sync_time` DATETIME NOT NULL COMMENT '同步时间',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标识',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_data_type_code` (`data_type`, `code`, `deleted`),
    KEY `idx_data_type` (`data_type`),
    KEY `idx_status` (`status`),
    KEY `idx_sync_time` (`sync_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='金蝶基础资料表';

-- 7. 系统日志表 (system_logs)
CREATE TABLE `system_logs` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `log_type` VARCHAR(50) NOT NULL COMMENT '日志类型：DOCUMENT_PROCESS-单据处理，API_CALL-API调用，DATA_SYNC-数据同步，SYSTEM_ERROR-系统错误',
    `business_id` VARCHAR(100) COMMENT '业务ID（如单据ID）',
    `business_type` VARCHAR(50) COMMENT '业务类型',
    `operation` VARCHAR(100) NOT NULL COMMENT '操作名称',
    `operator` VARCHAR(100) COMMENT '操作人',
    `request_data` LONGTEXT COMMENT '请求数据',
    `response_data` LONGTEXT COMMENT '响应数据',
    `status` VARCHAR(20) NOT NULL COMMENT '状态：SUCCESS-成功，FAILED-失败',
    `error_message` TEXT COMMENT '错误信息',
    `execution_time` BIGINT COMMENT '执行时间（毫秒）',
    `ip_address` VARCHAR(50) COMMENT 'IP地址',
    `user_agent` VARCHAR(500) COMMENT '用户代理',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_log_type` (`log_type`),
    KEY `idx_business_id` (`business_id`),
    KEY `idx_status` (`status`),
    KEY `idx_created_time` (`created_time`),
    KEY `idx_operator` (`operator`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';

-- 8. 文件上传记录表 (upload_files)
CREATE TABLE `upload_files` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `file_name` VARCHAR(500) NOT NULL COMMENT '文件名',
    `original_name` VARCHAR(500) NOT NULL COMMENT '原始文件名',
    `file_path` VARCHAR(1000) NOT NULL COMMENT '文件路径',
    `file_size` BIGINT NOT NULL COMMENT '文件大小（字节）',
    `file_type` VARCHAR(100) COMMENT '文件类型',
    `upload_status` VARCHAR(20) NOT NULL DEFAULT 'UPLOADED' COMMENT '上传状态：UPLOADED-已上传，PROCESSING-处理中，PROCESSED-已处理，FAILED-失败',
    `process_status` VARCHAR(20) COMMENT '处理状态：PENDING-待处理，SUCCESS-成功，FAILED-失败',
    `total_rows` INT COMMENT '总行数',
    `success_rows` INT COMMENT '成功行数',
    `failed_rows` INT COMMENT '失败行数',
    `error_message` TEXT COMMENT '错误信息',
    `uploaded_by` VARCHAR(100) COMMENT '上传人',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标识',
    PRIMARY KEY (`id`),
    KEY `idx_upload_status` (`upload_status`),
    KEY `idx_process_status` (`process_status`),
    KEY `idx_uploaded_by` (`uploaded_by`),
    KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件上传记录表';

-- 9. 系统配置表 (system_config)
CREATE TABLE `system_config` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `config_key` VARCHAR(100) NOT NULL COMMENT '配置键',
    `config_value` TEXT COMMENT '配置值',
    `config_desc` VARCHAR(500) COMMENT '配置描述',
    `config_group` VARCHAR(50) COMMENT '配置分组',
    `data_type` VARCHAR(20) NOT NULL DEFAULT 'STRING' COMMENT '数据类型：STRING-字符串，NUMBER-数字，BOOLEAN-布尔，JSON-JSON对象',
    `is_encrypted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否加密',
    `status` VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-有效，INACTIVE-无效',
    `created_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `created_by` VARCHAR(100) COMMENT '创建人',
    `updated_by` VARCHAR(100) COMMENT '更新人',
    `deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标识',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_config_key` (`config_key`, `deleted`),
    KEY `idx_config_group` (`config_group`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 插入初始配置数据
INSERT INTO `system_config` (`config_key`, `config_value`, `config_desc`, `config_group`, `data_type`) VALUES
('kingdee.base.url', 'https://api.kingdee.com', '金蝶API基础URL', 'kingdee', 'STRING'),
('kingdee.app.id', '', '金蝶应用ID', 'kingdee', 'STRING'),
('kingdee.app.secret', '', '金蝶应用密钥', 'kingdee', 'STRING'),
('kingdee.timeout', '30000', '金蝶API超时时间（毫秒）', 'kingdee', 'NUMBER'),
('kingdee.retry.times', '3', '金蝶API重试次数', 'kingdee', 'NUMBER'),
('document.batch.size', '100', '单据批量处理大小', 'business', 'NUMBER'),
('document.max.retry', '3', '单据最大重试次数', 'business', 'NUMBER'),
('sync.enabled', 'true', '基础资料同步开关', 'sync', 'BOOLEAN'),
('sync.cron', '0 0 2 * * ?', '基础资料同步定时任务表达式', 'sync', 'STRING'),
('file.upload.path', './uploads/', '文件上传路径', 'file', 'STRING'),
('file.max.size', '52428800', '文件最大大小（字节）', 'file', 'NUMBER');

-- 创建索引优化查询性能
CREATE INDEX `idx_documents_composite` ON `documents` (`status`, `document_type`, `created_time`);
CREATE INDEX `idx_system_logs_composite` ON `system_logs` (`log_type`, `status`, `created_time`);
CREATE INDEX `idx_upload_files_composite` ON `upload_files` (`upload_status`, `process_status`, `created_time`);

-- 创建视图简化查询
CREATE VIEW `v_document_summary` AS
SELECT 
    d.id,
    d.document_no,
    d.document_type,
    d.document_date,
    d.customer_name,
    d.warehouse_name,
    d.total_amount,
    d.total_quantity,
    d.status,
    d.error_message,
    d.created_time,
    COUNT(dd.id) as detail_count
FROM documents d
LEFT JOIN document_details dd ON d.id = dd.document_id AND dd.deleted = 0
WHERE d.deleted = 0
GROUP BY d.id;

COMMIT;