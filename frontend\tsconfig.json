{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "allowJs": true, "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "~/*": ["src/*"], "components/*": ["src/components/*"], "views/*": ["src/views/*"], "utils/*": ["src/utils/*"], "api/*": ["src/api/*"], "stores/*": ["src/stores/*"], "types/*": ["src/types/*"]}, "types": ["vite/client", "element-plus/global", "node"]}, "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "auto-imports.d.ts", "components.d.ts"], "exclude": ["src/**/__tests__/*", "dist", "node_modules"], "references": [{"path": "./tsconfig.node.json"}]}