package com.linkapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linkapi.entity.LoginLog;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 登录日志Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface LoginLogMapper extends BaseMapper<LoginLog> {

    /**
     * 记录登录日志
     *
     * @param userId 用户ID
     * @param username 用户名
     * @param loginIp 登录IP
     * @param loginStatus 登录状态
     * @param failureReason 失败原因
     * @param userAgent 用户代理
     * @return 插入行数
     */
    @Insert("INSERT INTO login_logs (user_id, username, login_ip, login_time, status, message) " +
            "VALUES (#{userId}, #{username}, #{loginIp}, #{loginTime}, #{loginStatus}, #{failureReason})")
    int insertLoginLog(@Param("userId") Long userId,
                      @Param("username") String username,
                      @Param("loginIp") String loginIp,
                      @Param("loginTime") LocalDateTime loginTime,
                      @Param("loginStatus") String loginStatus,
                      @Param("failureReason") String failureReason,
                      @Param("userAgent") String userAgent);

    /**
     * 查询用户最近的登录日志
     *
     * @param userId 用户ID
     * @param limit 限制条数
     * @return 登录日志列表
     */
    @Select("SELECT * FROM login_logs WHERE user_id = #{userId} " +
            "ORDER BY login_time DESC LIMIT #{limit}")
    List<LoginLog> findRecentLoginLogs(@Param("userId") Long userId, @Param("limit") int limit);

    /**
     * 查询指定时间范围内的登录失败次数
     *
     * @param username 用户名
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 失败次数
     */
    @Select("SELECT COUNT(*) FROM login_logs " +
            "WHERE username = #{username} AND status = 'FAILED' " +
            "AND login_time BETWEEN #{startTime} AND #{endTime}")
    int countFailedLogins(@Param("username") String username,
                         @Param("startTime") LocalDateTime startTime,
                         @Param("endTime") LocalDateTime endTime);
}