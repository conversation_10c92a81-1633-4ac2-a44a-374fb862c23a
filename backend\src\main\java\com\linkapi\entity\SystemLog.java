package com.linkapi.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 系统日志实体
 * 
 * <AUTHOR> API Team
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("system_logs")
public class SystemLog {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 日志类型
     */
    @NotBlank(message = "日志类型不能为空")
    @Size(max = 50, message = "日志类型长度不能超过50个字符")
    @TableField("log_type")
    private String logType;

    /**
     * 业务ID
     */
    @Size(max = 100, message = "业务ID长度不能超过100个字符")
    @TableField("business_id")
    private String businessId;

    /**
     * 业务类型
     */
    @Size(max = 50, message = "业务类型长度不能超过50个字符")
    @TableField("business_type")
    private String businessType;

    /**
     * 操作名称
     */
    @NotBlank(message = "操作名称不能为空")
    @Size(max = 100, message = "操作名称长度不能超过100个字符")
    @TableField("operation")
    private String operation;

    /**
     * 操作人
     */
    @Size(max = 100, message = "操作人长度不能超过100个字符")
    @TableField("operator")
    private String operator;

    /**
     * 请求数据
     */
    @TableField("request_data")
    private String requestData;

    /**
     * 响应数据
     */
    @TableField("response_data")
    private String responseData;

    /**
     * 状态
     */
    @NotBlank(message = "状态不能为空")
    @Size(max = 20, message = "状态长度不能超过20个字符")
    @TableField("status")
    private String status;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 执行时间（毫秒）
     */
    @TableField("execution_time")
    private Long executionTime;

    /**
     * IP地址
     */
    @Size(max = 50, message = "IP地址长度不能超过50个字符")
    @TableField("ip_address")
    private String ipAddress;

    /**
     * 用户代理
     */
    @Size(max = 500, message = "用户代理长度不能超过500个字符")
    @TableField("user_agent")
    private String userAgent;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 日志类型枚举
     */
    public enum LogType {
        DOCUMENT_PROCESS("单据处理"),
        API_CALL("API调用"),
        DATA_SYNC("数据同步"),
        SYSTEM_ERROR("系统错误"),
        USER_OPERATION("用户操作"),
        FILE_OPERATION("文件操作"),
        MAPPING_OPERATION("映射操作");

        private final String description;

        LogType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 日志状态枚举
     */
    public enum LogStatus {
        SUCCESS("成功"),
        FAILED("失败"),
        PROCESSING("处理中"),
        WARNING("警告");

        private final String description;

        LogStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 检查是否为成功状态
     */
    public boolean isSuccess() {
        return LogStatus.SUCCESS.name().equals(this.status);
    }

    /**
     * 检查是否为失败状态
     */
    public boolean isFailed() {
        return LogStatus.FAILED.name().equals(this.status);
    }

    /**
     * 检查是否为处理中状态
     */
    public boolean isProcessing() {
        return LogStatus.PROCESSING.name().equals(this.status);
    }

    /**
     * 检查是否为警告状态
     */
    public boolean isWarning() {
        return LogStatus.WARNING.name().equals(this.status);
    }

    /**
     * 获取执行时间（秒）
     */
    public Double getExecutionTimeInSeconds() {
        return executionTime != null ? executionTime / 1000.0 : null;
    }

    /**
     * 设置执行时间（秒）
     */
    public void setExecutionTimeInSeconds(Double seconds) {
        this.executionTime = seconds != null ? Math.round(seconds * 1000) : null;
    }

    /**
     * 获取简化的错误信息（前200个字符）
     */
    public String getShortErrorMessage() {
        if (errorMessage == null) {
            return null;
        }
        return errorMessage.length() > 200 ? errorMessage.substring(0, 200) + "..." : errorMessage;
    }

    /**
     * 获取简化的请求数据（前500个字符）
     */
    public String getShortRequestData() {
        if (requestData == null) {
            return null;
        }
        return requestData.length() > 500 ? requestData.substring(0, 500) + "..." : requestData;
    }

    /**
     * 获取简化的响应数据（前500个字符）
     */
    public String getShortResponseData() {
        if (responseData == null) {
            return null;
        }
        return responseData.length() > 500 ? responseData.substring(0, 500) + "..." : responseData;
    }
}