package com.linkapi.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 字段映射配置实体
 * 用于定义数据导入和API接口的字段映射规则
 * 
 * <AUTHOR> API Team
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("field_mapping_config")
public class FieldMappingConfig {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 配置名称
     */
    @NotBlank(message = "配置名称不能为空")
    @Size(max = 100, message = "配置名称长度不能超过100个字符")
    @TableField("config_name")
    private String configName;

    /**
     * 配置描述
     */
    @Size(max = 500, message = "配置描述长度不能超过500个字符")
    @TableField("config_description")
    private String configDescription;

    /**
     * 配置类型
     */
    @NotNull(message = "配置类型不能为空")
    @TableField("config_type")
    private ConfigType configType;

    /**
     * 模块类型
     */
    @NotBlank(message = "模块类型不能为空")
    @Size(max = 50, message = "模块类型长度不能超过50个字符")
    @TableField("module_type")
    private String moduleType;

    /**
     * 源系统
     */
    @NotBlank(message = "源系统不能为空")
    @Size(max = 50, message = "源系统长度不能超过50个字符")
    @TableField("source_system")
    private String sourceSystem;

    /**
     * 目标系统
     */
    @NotBlank(message = "目标系统不能为空")
    @Size(max = 50, message = "目标系统长度不能超过50个字符")
    @TableField("target_system")
    private String targetSystem;

    /**
     * 配置内容（JSON格式）
     */
    @NotBlank(message = "配置内容不能为空")
    @TableField("config_content")
    private String configContent;

    /**
     * 配置版本
     */
    @NotBlank(message = "配置版本不能为空")
    @Size(max = 20, message = "配置版本长度不能超过20个字符")
    @TableField("config_version")
    private String configVersion;

    /**
     * 是否启用
     */
    @TableField("is_enabled")
    private Boolean isEnabled;

    /**
     * 是否为默认配置
     */
    @TableField("is_default")
    private Boolean isDefault;

    /**
     * 优先级（数字越小优先级越高）
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 状态
     */
    @TableField("status")
    private ConfigStatus status;

    /**
     * 创建人
     */
    @Size(max = 50, message = "创建人长度不能超过50个字符")
    @TableField("created_by")
    private String createdBy;

    /**
     * 更新人
     */
    @Size(max = 50, message = "更新人长度不能超过50个字符")
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    @TableField("deleted")
    private Boolean deleted;

    /**
     * 配置类型枚举
     */
    public enum ConfigType {
        /**
         * 导入字段映射
         */
        IMPORT_MAPPING("导入字段映射"),
        
        /**
         * API字段映射
         */
        API_MAPPING("API字段映射"),
        
        /**
         * 数据转换规则
         */
        DATA_TRANSFORM("数据转换规则"),
        
        /**
         * 验证规则
         */
        VALIDATION_RULE("验证规则"),
        
        /**
         * 字段权限配置
         */
        FIELD_PERMISSION("字段权限配置");

        private final String description;

        ConfigType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 配置状态枚举
     */
    public enum ConfigStatus {
        /**
         * 草稿
         */
        DRAFT("草稿"),
        
        /**
         * 已发布
         */
        PUBLISHED("已发布"),
        
        /**
         * 已停用
         */
        DISABLED("已停用"),
        
        /**
         * 已归档
         */
        ARCHIVED("已归档");

        private final String description;

        ConfigStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
