package com.linkapi.config;

import com.kingdee.service.Configuration;
import com.kingdee.service.ApiClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 星辰客户端配置类
 * 用于初始化星辰API客户端
 */
// @Component - 临时禁用以解决启动问题
public class ClientApiConfig {
    
    @Value("${app.kingdee.client_id}")
    private String clientId;
    
    @Value("${app.kingdee.client_secret}")
    private String clientSecret;
    
    /**
     * 初始化星辰API客户端配置
     */
    @PostConstruct
    public void init() {
        ApiClient defaultApiClient = Configuration.getDefaultApiClient();
        defaultApiClient.setClientId(clientId);
        defaultApiClient.setClientSecret(clientSecret);
    }
    
    /**
     * 获取客户端ID
     * @return 客户端ID
     */
    public String getClientId() {
        return clientId;
    }
    
    /**
     * 获取客户端密钥
     * @return 客户端密钥
     */
    public String getClientSecret() {
        return clientSecret;
    }
}