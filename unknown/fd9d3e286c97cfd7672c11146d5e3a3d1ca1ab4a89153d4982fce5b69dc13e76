{"version": 3, "names": ["_index", "require", "STANDARDIZED_TYPES", "exports", "FLIPPED_ALIAS_KEYS", "EXPRESSION_TYPES", "BINARY_TYPES", "SCOPABLE_TYPES", "BLOCKPARENT_TYPES", "BLOCK_TYPES", "STATEMENT_TYPES", "TERMINATORLESS_TYPES", "COMPLETIONSTATEMENT_TYPES", "CONDITIONAL_TYPES", "LOOP_TYPES", "WHILE_TYPES", "EXPRESSIONWRAPPER_TYPES", "FOR_TYPES", "FORXSTATEMENT_TYPES", "FUNCTION_TYPES", "FUNCTIONPARENT_TYPES", "PUREISH_TYPES", "DECLARATION_TYPES", "FUNCTIONPARAMETER_TYPES", "PATTERNLIKE_TYPES", "LVAL_TYPES", "TSENTITYNAME_TYPES", "LITERAL_TYPES", "IMMUTABLE_TYPES", "USERWHITESPACABLE_TYPES", "METHOD_TYPES", "OBJECTMEMBER_TYPES", "PROPERTY_TYPES", "UNARYLIKE_TYPES", "PATTERN_TYPES", "CLASS_TYPES", "IMPORTOREXPORTDECLARATION_TYPES", "EXPORTDECLARATION_TYPES", "MODULESPECIFIER_TYPES", "ACCESSOR_TYPES", "PRIVATE_TYPES", "FLOW_TYPES", "FLOWTYPE_TYPES", "FLOWBASEANNOTATION_TYPES", "FLOWDECLARATION_TYPES", "FLOWPREDICATE_TYPES", "ENUMBODY_TYPES", "ENUMMEMBER_TYPES", "JSX_TYPES", "MISCELLANEOUS_TYPES", "TYPESCRIPT_TYPES", "TSTYPEELEMENT_TYPES", "TSTYPE_TYPES", "TSBASETYPE_TYPES", "MODULEDECLARATION_TYPES"], "sources": ["../../../src/constants/generated/index.ts"], "sourcesContent": ["/*\n * This file is auto-generated! Do not modify it directly.\n * To re-generate run 'make build'\n */\nimport { FLIPPED_ALIAS_KEYS } from \"../../definitions/index.ts\";\n\nexport const STANDARDIZED_TYPES = FLIPPED_ALIAS_KEYS[\"Standardized\"];\nexport const EXPRESSION_TYPES = FLIPPED_ALIAS_KEYS[\"Expression\"];\nexport const BINARY_TYPES = FLIPPED_ALIAS_KEYS[\"Binary\"];\nexport const SCOPABLE_TYPES = FLIPPED_ALIAS_KEYS[\"Scopable\"];\nexport const BLOCKPARENT_TYPES = FLIPPED_ALIAS_KEYS[\"BlockParent\"];\nexport const BLOCK_TYPES = FLIPPED_ALIAS_KEYS[\"Block\"];\nexport const STATEMENT_TYPES = FLIPPED_ALIAS_KEYS[\"Statement\"];\nexport const TERMINATORLESS_TYPES = FLIPPED_ALIAS_KEYS[\"Terminatorless\"];\nexport const COMPLETIONSTATEMENT_TYPES =\n  FLIPPED_ALIAS_KEYS[\"CompletionStatement\"];\nexport const CONDITIONAL_TYPES = FLIPPED_ALIAS_KEYS[\"Conditional\"];\nexport const LOOP_TYPES = FLIPPED_ALIAS_KEYS[\"Loop\"];\nexport const WHILE_TYPES = FLIPPED_ALIAS_KEYS[\"While\"];\nexport const EXPRESSIONWRAPPER_TYPES = FLIPPED_ALIAS_KEYS[\"ExpressionWrapper\"];\nexport const FOR_TYPES = FLIPPED_ALIAS_KEYS[\"For\"];\nexport const FORXSTATEMENT_TYPES = FLIPPED_ALIAS_KEYS[\"ForXStatement\"];\nexport const FUNCTION_TYPES = FLIPPED_ALIAS_KEYS[\"Function\"];\nexport const FUNCTIONPARENT_TYPES = FLIPPED_ALIAS_KEYS[\"FunctionParent\"];\nexport const PUREISH_TYPES = FLIPPED_ALIAS_KEYS[\"Pureish\"];\nexport const DECLARATION_TYPES = FLIPPED_ALIAS_KEYS[\"Declaration\"];\nexport const FUNCTIONPARAMETER_TYPES = FLIPPED_ALIAS_KEYS[\"FunctionParameter\"];\nexport const PATTERNLIKE_TYPES = FLIPPED_ALIAS_KEYS[\"PatternLike\"];\nexport const LVAL_TYPES = FLIPPED_ALIAS_KEYS[\"LVal\"];\nexport const TSENTITYNAME_TYPES = FLIPPED_ALIAS_KEYS[\"TSEntityName\"];\nexport const LITERAL_TYPES = FLIPPED_ALIAS_KEYS[\"Literal\"];\nexport const IMMUTABLE_TYPES = FLIPPED_ALIAS_KEYS[\"Immutable\"];\nexport const USERWHITESPACABLE_TYPES = FLIPPED_ALIAS_KEYS[\"UserWhitespacable\"];\nexport const METHOD_TYPES = FLIPPED_ALIAS_KEYS[\"Method\"];\nexport const OBJECTMEMBER_TYPES = FLIPPED_ALIAS_KEYS[\"ObjectMember\"];\nexport const PROPERTY_TYPES = FLIPPED_ALIAS_KEYS[\"Property\"];\nexport const UNARYLIKE_TYPES = FLIPPED_ALIAS_KEYS[\"UnaryLike\"];\nexport const PATTERN_TYPES = FLIPPED_ALIAS_KEYS[\"Pattern\"];\nexport const CLASS_TYPES = FLIPPED_ALIAS_KEYS[\"Class\"];\nexport const IMPORTOREXPORTDECLARATION_TYPES =\n  FLIPPED_ALIAS_KEYS[\"ImportOrExportDeclaration\"];\nexport const EXPORTDECLARATION_TYPES = FLIPPED_ALIAS_KEYS[\"ExportDeclaration\"];\nexport const MODULESPECIFIER_TYPES = FLIPPED_ALIAS_KEYS[\"ModuleSpecifier\"];\nexport const ACCESSOR_TYPES = FLIPPED_ALIAS_KEYS[\"Accessor\"];\nexport const PRIVATE_TYPES = FLIPPED_ALIAS_KEYS[\"Private\"];\nexport const FLOW_TYPES = FLIPPED_ALIAS_KEYS[\"Flow\"];\nexport const FLOWTYPE_TYPES = FLIPPED_ALIAS_KEYS[\"FlowType\"];\nexport const FLOWBASEANNOTATION_TYPES =\n  FLIPPED_ALIAS_KEYS[\"FlowBaseAnnotation\"];\nexport const FLOWDECLARATION_TYPES = FLIPPED_ALIAS_KEYS[\"FlowDeclaration\"];\nexport const FLOWPREDICATE_TYPES = FLIPPED_ALIAS_KEYS[\"FlowPredicate\"];\nexport const ENUMBODY_TYPES = FLIPPED_ALIAS_KEYS[\"EnumBody\"];\nexport const ENUMMEMBER_TYPES = FLIPPED_ALIAS_KEYS[\"EnumMember\"];\nexport const JSX_TYPES = FLIPPED_ALIAS_KEYS[\"JSX\"];\nexport const MISCELLANEOUS_TYPES = FLIPPED_ALIAS_KEYS[\"Miscellaneous\"];\nexport const TYPESCRIPT_TYPES = FLIPPED_ALIAS_KEYS[\"TypeScript\"];\nexport const TSTYPEELEMENT_TYPES = FLIPPED_ALIAS_KEYS[\"TSTypeElement\"];\nexport const TSTYPE_TYPES = FLIPPED_ALIAS_KEYS[\"TSType\"];\nexport const TSBASETYPE_TYPES = FLIPPED_ALIAS_KEYS[\"TSBaseType\"];\n/**\n * @deprecated migrate to IMPORTOREXPORTDECLARATION_TYPES.\n */\nexport const MODULEDECLARATION_TYPES = IMPORTOREXPORTDECLARATION_TYPES;\n"], "mappings": ";;;;;;AAIA,IAAAA,MAAA,GAAAC,OAAA;AAEO,MAAMC,kBAAkB,GAAAC,OAAA,CAAAD,kBAAA,GAAGE,yBAAkB,CAAC,cAAc,CAAC;AAC7D,MAAMC,gBAAgB,GAAAF,OAAA,CAAAE,gBAAA,GAAGD,yBAAkB,CAAC,YAAY,CAAC;AACzD,MAAME,YAAY,GAAAH,OAAA,CAAAG,YAAA,GAAGF,yBAAkB,CAAC,QAAQ,CAAC;AACjD,MAAMG,cAAc,GAAAJ,OAAA,CAAAI,cAAA,GAAGH,yBAAkB,CAAC,UAAU,CAAC;AACrD,MAAMI,iBAAiB,GAAAL,OAAA,CAAAK,iBAAA,GAAGJ,yBAAkB,CAAC,aAAa,CAAC;AAC3D,MAAMK,WAAW,GAAAN,OAAA,CAAAM,WAAA,GAAGL,yBAAkB,CAAC,OAAO,CAAC;AAC/C,MAAMM,eAAe,GAAAP,OAAA,CAAAO,eAAA,GAAGN,yBAAkB,CAAC,WAAW,CAAC;AACvD,MAAMO,oBAAoB,GAAAR,OAAA,CAAAQ,oBAAA,GAAGP,yBAAkB,CAAC,gBAAgB,CAAC;AACjE,MAAMQ,yBAAyB,GAAAT,OAAA,CAAAS,yBAAA,GACpCR,yBAAkB,CAAC,qBAAqB,CAAC;AACpC,MAAMS,iBAAiB,GAAAV,OAAA,CAAAU,iBAAA,GAAGT,yBAAkB,CAAC,aAAa,CAAC;AAC3D,MAAMU,UAAU,GAAAX,OAAA,CAAAW,UAAA,GAAGV,yBAAkB,CAAC,MAAM,CAAC;AAC7C,MAAMW,WAAW,GAAAZ,OAAA,CAAAY,WAAA,GAAGX,yBAAkB,CAAC,OAAO,CAAC;AAC/C,MAAMY,uBAAuB,GAAAb,OAAA,CAAAa,uBAAA,GAAGZ,yBAAkB,CAAC,mBAAmB,CAAC;AACvE,MAAMa,SAAS,GAAAd,OAAA,CAAAc,SAAA,GAAGb,yBAAkB,CAAC,KAAK,CAAC;AAC3C,MAAMc,mBAAmB,GAAAf,OAAA,CAAAe,mBAAA,GAAGd,yBAAkB,CAAC,eAAe,CAAC;AAC/D,MAAMe,cAAc,GAAAhB,OAAA,CAAAgB,cAAA,GAAGf,yBAAkB,CAAC,UAAU,CAAC;AACrD,MAAMgB,oBAAoB,GAAAjB,OAAA,CAAAiB,oBAAA,GAAGhB,yBAAkB,CAAC,gBAAgB,CAAC;AACjE,MAAMiB,aAAa,GAAAlB,OAAA,CAAAkB,aAAA,GAAGjB,yBAAkB,CAAC,SAAS,CAAC;AACnD,MAAMkB,iBAAiB,GAAAnB,OAAA,CAAAmB,iBAAA,GAAGlB,yBAAkB,CAAC,aAAa,CAAC;AAC3D,MAAMmB,uBAAuB,GAAApB,OAAA,CAAAoB,uBAAA,GAAGnB,yBAAkB,CAAC,mBAAmB,CAAC;AACvE,MAAMoB,iBAAiB,GAAArB,OAAA,CAAAqB,iBAAA,GAAGpB,yBAAkB,CAAC,aAAa,CAAC;AAC3D,MAAMqB,UAAU,GAAAtB,OAAA,CAAAsB,UAAA,GAAGrB,yBAAkB,CAAC,MAAM,CAAC;AAC7C,MAAMsB,kBAAkB,GAAAvB,OAAA,CAAAuB,kBAAA,GAAGtB,yBAAkB,CAAC,cAAc,CAAC;AAC7D,MAAMuB,aAAa,GAAAxB,OAAA,CAAAwB,aAAA,GAAGvB,yBAAkB,CAAC,SAAS,CAAC;AACnD,MAAMwB,eAAe,GAAAzB,OAAA,CAAAyB,eAAA,GAAGxB,yBAAkB,CAAC,WAAW,CAAC;AACvD,MAAMyB,uBAAuB,GAAA1B,OAAA,CAAA0B,uBAAA,GAAGzB,yBAAkB,CAAC,mBAAmB,CAAC;AACvE,MAAM0B,YAAY,GAAA3B,OAAA,CAAA2B,YAAA,GAAG1B,yBAAkB,CAAC,QAAQ,CAAC;AACjD,MAAM2B,kBAAkB,GAAA5B,OAAA,CAAA4B,kBAAA,GAAG3B,yBAAkB,CAAC,cAAc,CAAC;AAC7D,MAAM4B,cAAc,GAAA7B,OAAA,CAAA6B,cAAA,GAAG5B,yBAAkB,CAAC,UAAU,CAAC;AACrD,MAAM6B,eAAe,GAAA9B,OAAA,CAAA8B,eAAA,GAAG7B,yBAAkB,CAAC,WAAW,CAAC;AACvD,MAAM8B,aAAa,GAAA/B,OAAA,CAAA+B,aAAA,GAAG9B,yBAAkB,CAAC,SAAS,CAAC;AACnD,MAAM+B,WAAW,GAAAhC,OAAA,CAAAgC,WAAA,GAAG/B,yBAAkB,CAAC,OAAO,CAAC;AAC/C,MAAMgC,+BAA+B,GAAAjC,OAAA,CAAAiC,+BAAA,GAC1ChC,yBAAkB,CAAC,2BAA2B,CAAC;AAC1C,MAAMiC,uBAAuB,GAAAlC,OAAA,CAAAkC,uBAAA,GAAGjC,yBAAkB,CAAC,mBAAmB,CAAC;AACvE,MAAMkC,qBAAqB,GAAAnC,OAAA,CAAAmC,qBAAA,GAAGlC,yBAAkB,CAAC,iBAAiB,CAAC;AACnE,MAAMmC,cAAc,GAAApC,OAAA,CAAAoC,cAAA,GAAGnC,yBAAkB,CAAC,UAAU,CAAC;AACrD,MAAMoC,aAAa,GAAArC,OAAA,CAAAqC,aAAA,GAAGpC,yBAAkB,CAAC,SAAS,CAAC;AACnD,MAAMqC,UAAU,GAAAtC,OAAA,CAAAsC,UAAA,GAAGrC,yBAAkB,CAAC,MAAM,CAAC;AAC7C,MAAMsC,cAAc,GAAAvC,OAAA,CAAAuC,cAAA,GAAGtC,yBAAkB,CAAC,UAAU,CAAC;AACrD,MAAMuC,wBAAwB,GAAAxC,OAAA,CAAAwC,wBAAA,GACnCvC,yBAAkB,CAAC,oBAAoB,CAAC;AACnC,MAAMwC,qBAAqB,GAAAzC,OAAA,CAAAyC,qBAAA,GAAGxC,yBAAkB,CAAC,iBAAiB,CAAC;AACnE,MAAMyC,mBAAmB,GAAA1C,OAAA,CAAA0C,mBAAA,GAAGzC,yBAAkB,CAAC,eAAe,CAAC;AAC/D,MAAM0C,cAAc,GAAA3C,OAAA,CAAA2C,cAAA,GAAG1C,yBAAkB,CAAC,UAAU,CAAC;AACrD,MAAM2C,gBAAgB,GAAA5C,OAAA,CAAA4C,gBAAA,GAAG3C,yBAAkB,CAAC,YAAY,CAAC;AACzD,MAAM4C,SAAS,GAAA7C,OAAA,CAAA6C,SAAA,GAAG5C,yBAAkB,CAAC,KAAK,CAAC;AAC3C,MAAM6C,mBAAmB,GAAA9C,OAAA,CAAA8C,mBAAA,GAAG7C,yBAAkB,CAAC,eAAe,CAAC;AAC/D,MAAM8C,gBAAgB,GAAA/C,OAAA,CAAA+C,gBAAA,GAAG9C,yBAAkB,CAAC,YAAY,CAAC;AACzD,MAAM+C,mBAAmB,GAAAhD,OAAA,CAAAgD,mBAAA,GAAG/C,yBAAkB,CAAC,eAAe,CAAC;AAC/D,MAAMgD,YAAY,GAAAjD,OAAA,CAAAiD,YAAA,GAAGhD,yBAAkB,CAAC,QAAQ,CAAC;AACjD,MAAMiD,gBAAgB,GAAAlD,OAAA,CAAAkD,gBAAA,GAAGjD,yBAAkB,CAAC,YAAY,CAAC;AAIzD,MAAMkD,uBAAuB,GAAAnD,OAAA,CAAAmD,uBAAA,GAAGlB,+BAA+B", "ignoreList": []}