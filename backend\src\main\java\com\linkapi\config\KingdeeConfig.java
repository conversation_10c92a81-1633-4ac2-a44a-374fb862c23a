package com.linkapi.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 金蝶API配置
 * 
 * <AUTHOR> API Team
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "app.kingdee")
public class KingdeeConfig {

    /**
     * 金蝶API基础URL
     */
    private String baseUrl = "https://api.kingdee.com";

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 应用密钥
     */
    private String appSecret;

    /**
     * 超时时间（毫秒）
     */
    private Integer timeout = 30000;

    /**
     * 重试次数
     */
    private Integer retryTimes = 3;

    /**
     * 数据库ID
     */
    private String dbId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 语言代码
     */
    private String lcid = "2052";

    /**
     * API版本
     */
    private String version = "1.0";

    /**
     * 是否启用调试模式
     */
    private Boolean debug = false;

    /**
     * 连接池配置
     */
    private Pool pool = new Pool();

    @Data
    public static class Pool {
        /**
         * 最大连接数
         */
        private Integer maxConnections = 100;

        /**
         * 最大空闲连接数
         */
        private Integer maxIdleConnections = 10;

        /**
         * 连接保持时间（分钟）
         */
        private Integer keepAliveDuration = 5;

        /**
         * 连接超时时间（秒）
         */
        private Integer connectTimeout = 10;

        /**
         * 读取超时时间（秒）
         */
        private Integer readTimeout = 30;

        /**
         * 写入超时时间（秒）
         */
        private Integer writeTimeout = 30;
    }

    /**
     * API端点配置
     */
    @Data
    public static class Endpoints {
        /**
         * 登录端点
         */
        private String login = "/k3cloud/Kingdee.BOS.WebApi.ServicesStub.AuthService.ValidateUser.common.kdsvc";

        /**
         * 保存端点
         */
        private String save = "/k3cloud/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Save.common.kdsvc";

        /**
         * 查询端点
         */
        private String query = "/k3cloud/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc";

        /**
         * 删除端点
         */
        private String delete = "/k3cloud/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Delete.common.kdsvc";

        /**
         * 审核端点
         */
        private String audit = "/k3cloud/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Audit.common.kdsvc";

        /**
         * 反审核端点
         */
        private String unAudit = "/k3cloud/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.UnAudit.common.kdsvc";
    }

    private Endpoints endpoints = new Endpoints();

    /**
     * 单据类型映射配置
     */
    @Data
    public static class DocumentTypes {
        /**
         * 销售出库单
         */
        private String saleOut = "SAL_OUTSTOCK";

        /**
         * 销售退货单
         */
        private String saleReturn = "SAL_RETURNSTOCK";

        /**
         * 采购入库单
         */
        private String purchaseIn = "PUR_RECEIVESTOCK";

        /**
         * 调拨单
         */
        private String stockTransfer = "STK_TRANSFERAPPLY";
    }

    private DocumentTypes documentTypes = new DocumentTypes();

    /**
     * 获取完整的API URL
     */
    public String getFullUrl(String endpoint) {
        return baseUrl + endpoint;
    }

    /**
     * 根据单据类型获取金蝶表单ID
     */
    public String getFormId(String documentType) {
        switch (documentType) {
            case "SALE_OUT":
                return documentTypes.getSaleOut();
            case "SALE_RETURN":
                return documentTypes.getSaleReturn();
            case "PURCHASE_IN":
                return documentTypes.getPurchaseIn();
            case "STOCK_TRANSFER":
                return documentTypes.getStockTransfer();
            default:
                throw new IllegalArgumentException("不支持的单据类型: " + documentType);
        }
    }

    /**
     * 验证配置完整性
     */
    public void validate() {
        if (appId == null || appId.trim().isEmpty()) {
            throw new IllegalStateException("金蝶应用ID不能为空");
        }
        if (appSecret == null || appSecret.trim().isEmpty()) {
            throw new IllegalStateException("金蝶应用密钥不能为空");
        }
        if (dbId == null || dbId.trim().isEmpty()) {
            throw new IllegalStateException("金蝶数据库ID不能为空");
        }
        if (username == null || username.trim().isEmpty()) {
            throw new IllegalStateException("金蝶用户名不能为空");
        }
        if (password == null || password.trim().isEmpty()) {
            throw new IllegalStateException("金蝶密码不能为空");
        }
    }
}