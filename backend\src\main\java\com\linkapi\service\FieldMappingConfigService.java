package com.linkapi.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkapi.common.BusinessException;
import com.linkapi.common.ResultCode;
import com.linkapi.dto.FieldMappingConfigDTO;
import com.linkapi.entity.FieldMappingConfig;
import com.linkapi.entity.FieldMappingRule;
import com.linkapi.mapper.FieldMappingConfigMapper;
import com.linkapi.mapper.FieldMappingRuleMapper;
import com.linkapi.service.base.BaseServiceImpl;
import com.linkapi.service.interfaces.FieldMappingConfigServiceInterface;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 字段映射配置服务实现类
 * 
 * <AUTHOR> API Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FieldMappingConfigService extends BaseServiceImpl<FieldMappingConfig, Long, FieldMappingConfigMapper> 
        implements FieldMappingConfigServiceInterface {

    private final FieldMappingConfigMapper configMapper;
    private final FieldMappingRuleMapper ruleMapper;

    @Override
    protected FieldMappingConfigMapper getMapper() {
        return configMapper;
    }

    @Override
    protected String getEntityName() {
        return "字段映射配置";
    }

    @Override
    protected void setCreateTime(FieldMappingConfig entity) {
        entity.setCreatedTime(LocalDateTime.now());
    }

    @Override
    protected void setUpdateTime(FieldMappingConfig entity) {
        entity.setUpdatedTime(LocalDateTime.now());
    }

    @Override
    protected void validateBeforeCreate(FieldMappingConfig entity) throws BusinessException {
        super.validateBeforeCreate(entity);
        
        // 检查配置名称是否重复
        int count = configMapper.countByConfigName(entity.getConfigName(), 0L);
        if (count > 0) {
            throw new BusinessException(ResultCode.DATA_ALREADY_EXISTS, "配置名称已存在");
        }
    }

    @Override
    protected void validateBeforeUpdate(FieldMappingConfig entity) throws BusinessException {
        super.validateBeforeUpdate(entity);
        
        // 检查配置名称是否重复
        int count = configMapper.countByConfigName(entity.getConfigName(), entity.getId());
        if (count > 0) {
            throw new BusinessException(ResultCode.DATA_ALREADY_EXISTS, "配置名称已存在");
        }
    }

    // ==================== 配置管理 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FieldMappingConfigDTO createConfig(FieldMappingConfigDTO configDTO) throws BusinessException {
        try {
            // 转换DTO为实体
            FieldMappingConfig config = convertDTOToEntity(configDTO);
            
            // 设置默认值
            if (config.getIsEnabled() == null) {
                config.setIsEnabled(true);
            }
            if (config.getIsDefault() == null) {
                config.setIsDefault(false);
            }
            if (config.getPriority() == null) {
                config.setPriority(100);
            }
            if (config.getStatus() == null) {
                config.setStatus(FieldMappingConfig.ConfigStatus.DRAFT);
            }
            
            // 创建配置
            FieldMappingConfig createdConfig = create(config);
            
            // 创建字段规则
            if (configDTO.getFieldRules() != null && !configDTO.getFieldRules().isEmpty()) {
                List<FieldMappingRule> rules = convertRuleDTOsToEntities(configDTO.getFieldRules(), createdConfig.getId());
                ruleMapper.batchInsert(rules);
            }
            
            // 返回完整的配置信息
            return getConfigDetail(createdConfig.getId());
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建字段映射配置失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "创建字段映射配置失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FieldMappingConfigDTO updateConfig(FieldMappingConfigDTO configDTO) throws BusinessException {
        try {
            if (configDTO.getId() == null) {
                throw new BusinessException(ResultCode.PARAM_ERROR, "配置ID不能为空");
            }
            
            // 检查配置是否存在
            FieldMappingConfig existingConfig = getById(configDTO.getId());
            if (existingConfig == null) {
                throw new BusinessException(ResultCode.DATA_NOT_FOUND, "配置不存在");
            }
            
            // 转换DTO为实体
            FieldMappingConfig config = convertDTOToEntity(configDTO);
            config.setId(configDTO.getId());
            
            // 更新配置
            FieldMappingConfig updatedConfig = update(config);
            
            // 删除原有规则
            ruleMapper.deleteByConfigId(updatedConfig.getId());
            
            // 创建新规则
            if (configDTO.getFieldRules() != null && !configDTO.getFieldRules().isEmpty()) {
                List<FieldMappingRule> rules = convertRuleDTOsToEntities(configDTO.getFieldRules(), updatedConfig.getId());
                ruleMapper.batchInsert(rules);
            }
            
            // 返回完整的配置信息
            return getConfigDetail(updatedConfig.getId());
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新字段映射配置失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "更新字段映射配置失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteConfig(Long id) throws BusinessException {
        try {
            // 检查配置是否存在
            FieldMappingConfig config = getById(id);
            if (config == null) {
                throw new BusinessException(ResultCode.DATA_NOT_FOUND, "配置不存在");
            }
            
            // 检查是否为默认配置
            if (Boolean.TRUE.equals(config.getIsDefault())) {
                throw new BusinessException(ResultCode.BUSINESS_ERROR, "默认配置不能删除");
            }
            
            // 删除配置和相关规则
            ruleMapper.deleteByConfigId(id);
            return deleteById(id);
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除字段映射配置失败，ID: {}", id, e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "删除字段映射配置失败");
        }
    }

    @Override
    public FieldMappingConfigDTO getConfigDetail(Long id) throws BusinessException {
        try {
            // 获取配置基本信息
            FieldMappingConfig config = getById(id);
            if (config == null) {
                throw new BusinessException(ResultCode.DATA_NOT_FOUND, "配置不存在");
            }
            
            // 获取字段规则
            List<FieldMappingRule> rules = ruleMapper.selectAllByConfigId(id);
            
            // 转换为DTO
            return convertEntityToDTO(config, rules);
            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取字段映射配置详情失败，ID: {}", id, e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "获取字段映射配置详情失败");
        }
    }

    @Override
    public IPage<FieldMappingConfigDTO.ConfigPreviewDTO> getConfigPage(FieldMappingConfigDTO.ConfigQueryDTO queryDTO) 
            throws BusinessException {
        try {
            Page<FieldMappingConfigDTO.ConfigPreviewDTO> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
            return configMapper.selectConfigPreviewPage(page, queryDTO);
            
        } catch (Exception e) {
            log.error("分页查询字段映射配置失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "分页查询字段映射配置失败");
        }
    }

    // ==================== 配置查询 ====================

    @Override
    public List<FieldMappingConfig> getConfigsByTypeAndModule(FieldMappingConfig.ConfigType configType, String moduleType) 
            throws BusinessException {
        try {
            return configMapper.selectByTypeAndModule(configType, moduleType);
        } catch (Exception e) {
            log.error("根据类型和模块查询配置失败，configType: {}, moduleType: {}", configType, moduleType, e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "查询配置失败");
        }
    }

    @Override
    public List<FieldMappingConfig> getConfigsBySourceAndTarget(String sourceSystem, String targetSystem) 
            throws BusinessException {
        try {
            return configMapper.selectBySourceAndTarget(sourceSystem, targetSystem);
        } catch (Exception e) {
            log.error("根据源系统和目标系统查询配置失败，sourceSystem: {}, targetSystem: {}", sourceSystem, targetSystem, e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "查询配置失败");
        }
    }

    @Override
    public FieldMappingConfig getDefaultConfig(FieldMappingConfig.ConfigType configType, String moduleType) 
            throws BusinessException {
        try {
            return configMapper.selectDefaultConfig(configType, moduleType);
        } catch (Exception e) {
            log.error("获取默认配置失败，configType: {}, moduleType: {}", configType, moduleType, e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "获取默认配置失败");
        }
    }

    @Override
    public List<FieldMappingConfig> getEnabledConfigs() throws BusinessException {
        try {
            return configMapper.selectEnabledConfigs();
        } catch (Exception e) {
            log.error("获取启用配置列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "获取启用配置列表失败");
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 转换DTO为实体
     */
    private FieldMappingConfig convertDTOToEntity(FieldMappingConfigDTO dto) {
        FieldMappingConfig entity = new FieldMappingConfig();
        BeanUtils.copyProperties(dto, entity);
        
        // 处理扩展配置
        if (dto.getExtendedConfig() != null) {
            entity.setConfigContent(JSON.toJSONString(dto.getExtendedConfig()));
        }
        
        return entity;
    }

    /**
     * 转换实体为DTO
     */
    private FieldMappingConfigDTO convertEntityToDTO(FieldMappingConfig entity, List<FieldMappingRule> rules) {
        FieldMappingConfigDTO dto = new FieldMappingConfigDTO();
        BeanUtils.copyProperties(entity, dto);
        
        // 处理扩展配置
        if (StringUtils.hasText(entity.getConfigContent())) {
            try {
                Map<String, Object> extendedConfig = JSON.parseObject(entity.getConfigContent(), Map.class);
                dto.setExtendedConfig(extendedConfig);
            } catch (JSONException e) {
                log.warn("解析配置内容失败，configId: {}", entity.getId(), e);
            }
        }
        
        // 转换规则
        if (rules != null) {
            List<FieldMappingConfigDTO.FieldMappingRuleDTO> ruleDTOs = rules.stream()
                    .map(this::convertRuleEntityToDTO)
                    .collect(Collectors.toList());
            dto.setFieldRules(ruleDTOs);
        }
        
        return dto;
    }

    /**
     * 转换规则DTO列表为实体列表
     */
    private List<FieldMappingRule> convertRuleDTOsToEntities(List<FieldMappingConfigDTO.FieldMappingRuleDTO> dtos, Long configId) {
        return dtos.stream().map(dto -> {
            FieldMappingRule entity = new FieldMappingRule();
            BeanUtils.copyProperties(dto, entity);
            entity.setConfigId(configId);
            
            // 处理验证规则
            if (dto.getValidationRules() != null) {
                entity.setValidationRules(JSON.toJSONString(dto.getValidationRules()));
            }
            
            // 设置默认值
            if (entity.getIsEnabled() == null) {
                entity.setIsEnabled(true);
            }
            if (entity.getSortOrder() == null) {
                entity.setSortOrder(0);
            }
            
            return entity;
        }).collect(Collectors.toList());
    }

    /**
     * 转换规则实体为DTO
     */
    private FieldMappingConfigDTO.FieldMappingRuleDTO convertRuleEntityToDTO(FieldMappingRule entity) {
        FieldMappingConfigDTO.FieldMappingRuleDTO dto = new FieldMappingConfigDTO.FieldMappingRuleDTO();
        BeanUtils.copyProperties(entity, dto);
        
        // 处理验证规则
        if (StringUtils.hasText(entity.getValidationRules())) {
            try {
                FieldMappingConfigDTO.ValidationRuleDTO validationRules = 
                        JSON.parseObject(entity.getValidationRules(), FieldMappingConfigDTO.ValidationRuleDTO.class);
                dto.setValidationRules(validationRules);
            } catch (JSONException e) {
                log.warn("解析验证规则失败，ruleId: {}", entity.getId(), e);
            }
        }
        
        return dto;
    }

    // ==================== 配置操作 ====================

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean publishConfig(Long id) throws BusinessException {
        try {
            FieldMappingConfig config = getById(id);
            if (config == null) {
                throw new BusinessException(ResultCode.DATA_NOT_FOUND, "配置不存在");
            }

            // 验证配置完整性
            List<FieldMappingRule> rules = ruleMapper.selectByConfigId(id);
            if (rules.isEmpty()) {
                throw new BusinessException(ResultCode.PARAM_ERROR, "配置没有字段规则，无法发布");
            }

            // 更新状态为已发布
            int result = configMapper.updateStatus(id, FieldMappingConfig.ConfigStatus.PUBLISHED);
            return result > 0;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("发布配置失败，ID: {}", id, e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "发布配置失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableConfig(Long id) throws BusinessException {
        try {
            FieldMappingConfig config = getById(id);
            if (config == null) {
                throw new BusinessException(ResultCode.DATA_NOT_FOUND, "配置不存在");
            }

            // 检查是否为默认配置
            if (Boolean.TRUE.equals(config.getIsDefault())) {
                throw new BusinessException(ResultCode.PARAM_ERROR, "默认配置不能停用");
            }

            // 更新状态为已停用
            int result = configMapper.updateStatus(id, FieldMappingConfig.ConfigStatus.DISABLED);
            return result > 0;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("停用配置失败，ID: {}", id, e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "停用配置失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableConfig(Long id) throws BusinessException {
        try {
            FieldMappingConfig config = getById(id);
            if (config == null) {
                throw new BusinessException(ResultCode.DATA_NOT_FOUND, "配置不存在");
            }

            // 更新启用状态
            config.setIsEnabled(true);
            FieldMappingConfig updatedConfig = update(config);
            return updatedConfig != null;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("启用配置失败，ID: {}", id, e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "启用配置失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean setDefaultConfig(Long id) throws BusinessException {
        try {
            FieldMappingConfig config = getById(id);
            if (config == null) {
                throw new BusinessException(ResultCode.DATA_NOT_FOUND, "配置不存在");
            }

            // 设置为默认配置（会自动清除其他默认配置）
            int result = configMapper.setDefaultConfig(id, config.getConfigType(), config.getModuleType());
            return result > 0;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("设置默认配置失败，ID: {}", id, e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "设置默认配置失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FieldMappingConfigDTO copyConfig(Long sourceId, String newConfigName) throws BusinessException {
        try {
            // 检查源配置是否存在
            FieldMappingConfig sourceConfig = getById(sourceId);
            if (sourceConfig == null) {
                throw new BusinessException(ResultCode.DATA_NOT_FOUND, "源配置不存在");
            }

            // 检查新配置名称是否重复
            int count = configMapper.countByConfigName(newConfigName, 0L);
            if (count > 0) {
                throw new BusinessException(ResultCode.DATA_ALREADY_EXISTS, "配置名称已存在");
            }

            // 复制配置
            FieldMappingConfig newConfig = new FieldMappingConfig();
            BeanUtils.copyProperties(sourceConfig, newConfig);
            newConfig.setId(null);
            newConfig.setConfigName(newConfigName);
            newConfig.setIsDefault(false);
            newConfig.setIsEnabled(false);
            newConfig.setStatus(FieldMappingConfig.ConfigStatus.DRAFT);
            newConfig.setCreatedTime(LocalDateTime.now());
            newConfig.setUpdatedTime(LocalDateTime.now());

            // 创建新配置
            FieldMappingConfig createdConfig = create(newConfig);

            // 复制规则
            ruleMapper.copyRulesToNewConfig(sourceId, createdConfig.getId());

            // 返回完整的配置信息
            return getConfigDetail(createdConfig.getId());

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("复制配置失败，sourceId: {}, newConfigName: {}", sourceId, newConfigName, e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "复制配置失败");
        }
    }

    // ==================== 字段映射应用 ====================

    @Override
    public Map<String, Object> applyFieldMapping(Long configId, Map<String, Object> sourceData) throws BusinessException {
        try {
            // 获取配置和规则
            FieldMappingConfig config = getById(configId);
            if (config == null) {
                throw new BusinessException(ResultCode.DATA_NOT_FOUND, "配置不存在");
            }

            List<FieldMappingRule> rules = ruleMapper.selectByConfigId(configId);
            if (rules.isEmpty()) {
                throw new BusinessException(ResultCode.PARAM_ERROR, "配置没有字段规则");
            }

            Map<String, Object> targetData = new HashMap<>();

            // 应用字段映射规则
            for (FieldMappingRule rule : rules) {
                Object sourceValue = sourceData.get(rule.getSourceField());
                Object targetValue = applyTransformation(rule, sourceValue, sourceData);

                if (targetValue != null) {
                    targetData.put(rule.getTargetField(), targetValue);
                } else if (rule.getDefaultValue() != null) {
                    targetData.put(rule.getTargetField(), rule.getDefaultValue());
                }
            }

            return targetData;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("应用字段映射失败，configId: {}", configId, e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "应用字段映射失败");
        }
    }

    @Override
    public List<Map<String, Object>> batchApplyFieldMapping(Long configId, List<Map<String, Object>> sourceDataList)
            throws BusinessException {
        try {
            return sourceDataList.stream()
                    .map(sourceData -> {
                        try {
                            return applyFieldMapping(configId, sourceData);
                        } catch (BusinessException e) {
                            log.error("批量应用字段映射失败，configId: {}", configId, e);
                            return new HashMap<String, Object>();
                        }
                    })
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("批量应用字段映射失败，configId: {}", configId, e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "批量应用字段映射失败");
        }
    }

    /**
     * 应用字段转换
     */
    private Object applyTransformation(FieldMappingRule rule, Object sourceValue, Map<String, Object> sourceData) {
        if (rule.getTransformType() == null || rule.getTransformType() == FieldMappingRule.TransformType.NONE) {
            return sourceValue;
        }

        switch (rule.getTransformType()) {
            case DIRECT:
                return sourceValue;

            case CONSTANT:
                return rule.getTransformExpression();

            case EXPRESSION:
                // 这里可以集成表达式引擎，如SpEL或其他
                return evaluateExpression(rule.getTransformExpression(), sourceData);

            case DICTIONARY:
                // 字典映射
                return applyDictionaryMapping(rule.getTransformExpression(), sourceValue);

            case FORMAT:
                // 格式化
                return formatValue(rule.getTransformExpression(), sourceValue);

            default:
                return sourceValue;
        }
    }

    /**
     * 计算表达式（简单实现）
     */
    private Object evaluateExpression(String expression, Map<String, Object> context) {
        // 这里可以集成SpEL或其他表达式引擎
        // 简单实现：支持基本的字段引用
        if (expression.startsWith("${") && expression.endsWith("}")) {
            String fieldName = expression.substring(2, expression.length() - 1);
            return context.get(fieldName);
        }
        return expression;
    }

    /**
     * 应用字典映射
     */
    private Object applyDictionaryMapping(String mappingJson, Object sourceValue) {
        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> mapping = JSON.parseObject(mappingJson, Map.class);
            return mapping.getOrDefault(String.valueOf(sourceValue), sourceValue);
        } catch (Exception e) {
            log.warn("字典映射失败，mappingJson: {}, sourceValue: {}", mappingJson, sourceValue, e);
            return sourceValue;
        }
    }

    /**
     * 格式化值
     */
    private Object formatValue(String format, Object value) {
        if (value == null) {
            return null;
        }

        try {
            return String.format(format, value);
        } catch (Exception e) {
            log.warn("格式化值失败，format: {}, value: {}", format, value, e);
            return value;
        }
    }

    // ==================== 数据验证 ====================

    @Override
    public ValidationResult validateData(Long configId, Map<String, Object> data) throws BusinessException {
        try {
            ValidationResult result = new ValidationResult();
            result.setValid(true);
            result.setErrors(new ArrayList<>());
            result.setValidatedData(new HashMap<>(data));

            // 获取验证规则
            List<FieldMappingRule> rules = ruleMapper.selectByConfigId(configId);

            for (FieldMappingRule rule : rules) {
                Object value = data.get(rule.getTargetField());

                // 必填验证
                if (Boolean.TRUE.equals(rule.getIsRequired()) && (value == null || "".equals(value))) {
                    result.getErrors().add(new ValidationError(rule.getTargetField(),
                            rule.getTargetFieldLabel() + "不能为空", value));
                    result.setValid(false);
                    continue;
                }

                // 类型验证
                if (value != null && !validateFieldType(value, rule.getFieldType())) {
                    result.getErrors().add(new ValidationError(rule.getTargetField(),
                            rule.getTargetFieldLabel() + "类型不正确", value));
                    result.setValid(false);
                    continue;
                }

                // 自定义验证规则
                if (StringUtils.hasText(rule.getValidationRules())) {
                    ValidationError error = validateCustomRules(rule, value);
                    if (error != null) {
                        result.getErrors().add(error);
                        result.setValid(false);
                    }
                }
            }

            return result;

        } catch (Exception e) {
            log.error("验证数据失败，configId: {}", configId, e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "验证数据失败");
        }
    }

    /**
     * 验证字段类型
     */
    private boolean validateFieldType(Object value, FieldMappingRule.FieldType fieldType) {
        if (value == null) {
            return true;
        }

        switch (fieldType) {
            case STRING:
                return value instanceof String;
            case INTEGER:
                return value instanceof Integer || (value instanceof String && isInteger((String) value));
            case DECIMAL:
                return value instanceof Number || (value instanceof String && isDecimal((String) value));
            case BOOLEAN:
                return value instanceof Boolean || "true".equalsIgnoreCase(String.valueOf(value)) ||
                       "false".equalsIgnoreCase(String.valueOf(value));
            case DATE:
            case DATETIME:
            case TIME:
                return value instanceof Date || value instanceof LocalDateTime || isDateString(String.valueOf(value));
            default:
                return true;
        }
    }

    /**
     * 验证自定义规则
     */
    private ValidationError validateCustomRules(FieldMappingRule rule, Object value) {
        try {
            if (!StringUtils.hasText(rule.getValidationRules())) {
                return null;
            }

            FieldMappingConfigDTO.ValidationRuleDTO validationRule =
                    JSON.parseObject(rule.getValidationRules(), FieldMappingConfigDTO.ValidationRuleDTO.class);

            String stringValue = String.valueOf(value);

            // 长度验证
            if (validationRule.getMinLength() != null && stringValue.length() < validationRule.getMinLength()) {
                return new ValidationError(rule.getTargetField(),
                        rule.getTargetFieldLabel() + "长度不能少于" + validationRule.getMinLength() + "个字符", value);
            }

            if (validationRule.getMaxLength() != null && stringValue.length() > validationRule.getMaxLength()) {
                return new ValidationError(rule.getTargetField(),
                        rule.getTargetFieldLabel() + "长度不能超过" + validationRule.getMaxLength() + "个字符", value);
            }

            // 数值范围验证
            if (value instanceof Number) {
                double numValue = ((Number) value).doubleValue();

                if (validationRule.getMinValue() != null && numValue < validationRule.getMinValue()) {
                    return new ValidationError(rule.getTargetField(),
                            rule.getTargetFieldLabel() + "不能小于" + validationRule.getMinValue(), value);
                }

                if (validationRule.getMaxValue() != null && numValue > validationRule.getMaxValue()) {
                    return new ValidationError(rule.getTargetField(),
                            rule.getTargetFieldLabel() + "不能大于" + validationRule.getMaxValue(), value);
                }
            }

            // 正则验证
            if (StringUtils.hasText(validationRule.getPattern()) &&
                !stringValue.matches(validationRule.getPattern())) {
                return new ValidationError(rule.getTargetField(),
                        validationRule.getErrorMessage() != null ? validationRule.getErrorMessage() :
                        rule.getTargetFieldLabel() + "格式不正确", value);
            }

            // 枚举值验证
            if (validationRule.getEnumValues() != null && !validationRule.getEnumValues().isEmpty() &&
                !validationRule.getEnumValues().contains(stringValue)) {
                return new ValidationError(rule.getTargetField(),
                        rule.getTargetFieldLabel() + "值不在允许范围内", value);
            }

            return null;

        } catch (Exception e) {
            log.warn("验证自定义规则失败，rule: {}, value: {}", rule.getId(), value, e);
            return null;
        }
    }

    // ==================== 配置导入导出 ====================

    @Override
    public String exportConfig(Long id) throws BusinessException {
        try {
            FieldMappingConfigDTO config = getConfigDetail(id);
            return JSON.toJSONString(config);
        } catch (Exception e) {
            log.error("导出配置失败，ID: {}", id, e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "导出配置失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FieldMappingConfigDTO importConfig(String configJson) throws BusinessException {
        try {
            FieldMappingConfigDTO configDTO = JSON.parseObject(configJson, FieldMappingConfigDTO.class);
            configDTO.setId(null); // 清除ID，作为新配置创建

            // 检查配置名称是否重复，如果重复则自动重命名
            String originalName = configDTO.getConfigName();
            String newName = originalName;
            int suffix = 1;

            while (configMapper.countByConfigName(newName, 0L) > 0) {
                newName = originalName + "_imported_" + suffix++;
            }

            configDTO.setConfigName(newName);
            configDTO.setIsDefault(false);
            configDTO.setStatus(FieldMappingConfig.ConfigStatus.DRAFT);

            return createConfig(configDTO);

        } catch (Exception e) {
            log.error("导入配置失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "导入配置失败");
        }
    }

    @Override
    public String batchExportConfigs(List<Long> ids) throws BusinessException {
        try {
            List<FieldMappingConfigDTO> configs = ids.stream()
                    .map(id -> {
                        try {
                            return getConfigDetail(id);
                        } catch (BusinessException e) {
                            log.warn("导出配置失败，ID: {}", id, e);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            return JSON.toJSONString(configs);

        } catch (Exception e) {
            log.error("批量导出配置失败，IDs: {}", ids, e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "批量导出配置失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResult batchImportConfigs(String configsJson) throws BusinessException {
        ImportResult result = new ImportResult();
        result.setErrorMessages(new ArrayList<>());

        try {
            List<FieldMappingConfigDTO> configs = JSON.parseArray(configsJson, FieldMappingConfigDTO.class);
            result.setTotalCount(configs.size());

            int successCount = 0;
            for (FieldMappingConfigDTO config : configs) {
                try {
                    importConfig(JSON.toJSONString(config));
                    successCount++;
                } catch (Exception e) {
                    result.getErrorMessages().add("导入配置失败：" + config.getConfigName() + " - " + e.getMessage());
                }
            }

            result.setSuccessCount(successCount);
            result.setFailedCount(configs.size() - successCount);

            return result;

        } catch (Exception e) {
            log.error("批量导入配置失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "批量导入配置失败");
        }
    }

    // ==================== 统计信息 ====================

    @Override
    public ConfigStatistics getConfigStatistics() throws BusinessException {
        try {
            FieldMappingConfigMapper.ConfigStatistics stats = configMapper.getConfigStatistics();

            ConfigStatistics result = new ConfigStatistics();
            result.setTotalCount(stats.getTotalCount());
            result.setEnabledCount(stats.getEnabledCount());
            result.setPublishedCount(stats.getPublishedCount());
            result.setDefaultCount(stats.getDefaultCount());

            return result;

        } catch (Exception e) {
            log.error("获取配置统计信息失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "获取配置统计信息失败");
        }
    }

    @Override
    public List<FieldTypeStatistics> getFieldTypeStatistics(Long configId) throws BusinessException {
        try {
            List<FieldMappingRuleMapper.FieldTypeStatistics> stats = ruleMapper.getFieldTypeStatistics(configId);

            return stats.stream().map(stat -> {
                FieldTypeStatistics result = new FieldTypeStatistics();
                result.setFieldType(stat.getFieldType());
                result.setCount(stat.getCount());
                return result;
            }).collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取字段类型统计失败，configId: {}", configId, e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "获取字段类型统计失败");
        }
    }

    // ==================== 辅助方法 ====================

    private boolean isInteger(String str) {
        try {
            Integer.parseInt(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    private boolean isDecimal(String str) {
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    private boolean isDateString(String str) {
        // 简单的日期格式验证
        return str.matches("\\d{4}-\\d{2}-\\d{2}.*");
    }
}
