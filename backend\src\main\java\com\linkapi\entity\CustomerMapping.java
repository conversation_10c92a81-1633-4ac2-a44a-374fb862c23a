package com.linkapi.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 客户映射表实体
 * 
 * <AUTHOR> API Team
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("customer_mapping")
public class CustomerMapping {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 外部系统客户编码（旺店通店铺）
     */
    @NotBlank(message = "外部系统客户编码不能为空")
    @Size(max = 100, message = "外部系统客户编码长度不能超过100个字符")
    @TableField("external_code")
    private String externalCode;

    /**
     * 外部系统客户名称
     */
    @Size(max = 200, message = "外部系统客户名称长度不能超过200个字符")
    @TableField("external_name")
    private String externalName;

    /**
     * 金蝶客户编码
     */
    @NotBlank(message = "金蝶客户编码不能为空")
    @Size(max = 100, message = "金蝶客户编码长度不能超过100个字符")
    @TableField("kingdee_code")
    private String kingdeeCode;

    /**
     * 金蝶客户名称
     */
    @Size(max = 200, message = "金蝶客户名称长度不能超过200个字符")
    @TableField("kingdee_name")
    private String kingdeeName;

    /**
     * 客户类型
     */
    @Size(max = 50, message = "客户类型长度不能超过50个字符")
    @TableField("customer_type")
    private String customerType;

    /**
     * 状态
     */
    @TableField("status")
    private MappingStatus status;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 创建人
     */
    @Size(max = 100, message = "创建人长度不能超过100个字符")
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 更新人
     */
    @Size(max = 100, message = "更新人长度不能超过100个字符")
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    /**
     * 逻辑删除标识
     */
    @TableLogic
    @TableField("deleted")
    private Boolean deleted;

    /**
     * 映射状态枚举
     */
    public enum MappingStatus {
        ACTIVE("有效"),
        INACTIVE("无效");

        private final String description;

        MappingStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 检查映射是否有效
     */
    public boolean isActive() {
        return MappingStatus.ACTIVE.equals(this.status);
    }

    /**
     * 激活映射
     */
    public void activate() {
        this.status = MappingStatus.ACTIVE;
    }

    /**
     * 停用映射
     */
    public void deactivate() {
        this.status = MappingStatus.INACTIVE;
    }
}