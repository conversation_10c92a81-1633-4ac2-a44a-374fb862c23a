import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { getToken } from '@/utils/auth'
import router from '@/router'
import NProgress from 'nprogress'

// 响应数据接口
interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
  timestamp?: number
}

// 请求配置接口
interface RequestConfig extends AxiosRequestConfig {
  showLoading?: boolean
  showError?: boolean
  showSuccess?: boolean
  loadingText?: string
  successMessage?: string
}

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
})

// 加载实例
let loadingInstance: any = null
let loadingCount = 0

// 显示加载
const showLoading = (text = '加载中...') => {
  if (loadingCount === 0) {
    loadingInstance = ElLoading.service({
      lock: true,
      text,
      background: 'rgba(0, 0, 0, 0.7)'
    })
  }
  loadingCount++
}

// 隐藏加载
const hideLoading = () => {
  loadingCount--
  if (loadingCount <= 0) {
    loadingCount = 0
    if (loadingInstance) {
      loadingInstance.close()
      loadingInstance = null
    }
  }
}

// 请求拦截器
service.interceptors.request.use(
  (config: RequestConfig) => {
    // 开始进度条
    NProgress.start()
    
    // 显示加载动画
    if (config.showLoading !== false) {
      showLoading(config.loadingText)
    }
    
    // 添加认证token
    const token = getToken()
    if (token) {
      config.headers = config.headers || {}
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加请求时间戳（防止缓存）
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }
    
    // 请求日志
    if (import.meta.env.DEV) {
      console.log('🚀 请求发送:', {
        url: config.url,
        method: config.method,
        params: config.params,
        data: config.data
      })
    }
    
    return config
  },
  (error: AxiosError) => {
    // 隐藏加载动画
    hideLoading()
    NProgress.done()
    
    console.error('请求拦截器错误:', error)
    ElMessage.error('请求配置错误')
    
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    // 隐藏加载动画
    hideLoading()
    NProgress.done()
    
    const config = response.config as RequestConfig
    const { code, message, data } = response.data
    
    // 响应日志
    if (import.meta.env.DEV) {
      console.log('📦 响应接收:', {
        url: response.config.url,
        status: response.status,
        data: response.data
      })
    }
    
    // 处理业务状态码
    if (code === 200) {
      // 成功响应
      if (config.showSuccess && config.successMessage) {
        ElMessage.success(config.successMessage)
      }
      return data
    } else if (code === 401) {
      // 未授权，清除token并跳转到登录页
      const userStore = useUserStore()
      userStore.resetToken()
      
      ElMessageBox.confirm(
        '登录状态已过期，您可以继续留在该页面，或者重新登录',
        '系统提示',
        {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        router.push(`/login?redirect=${router.currentRoute.value.fullPath}`)
      })
      
      return Promise.reject(new Error(message || '登录状态已过期'))
    } else if (code === 403) {
      // 无权限
      ElMessage.error(message || '无权限访问')
      return Promise.reject(new Error(message || '无权限访问'))
    } else {
      // 其他业务错误
      if (config.showError !== false) {
        ElMessage.error(message || '请求失败')
      }
      return Promise.reject(new Error(message || '请求失败'))
    }
  },
  (error: AxiosError) => {
    // 隐藏加载动画
    hideLoading()
    NProgress.done()
    
    const config = error.config as RequestConfig
    let message = '网络错误'
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          message = '请求参数错误'
          break
        case 401:
          message = '未授权，请重新登录'
          // 清除token
          const userStore = useUserStore()
          userStore.resetToken()
          router.push('/login')
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求地址不存在'
          break
        case 408:
          message = '请求超时'
          break
        case 500:
          message = '服务器内部错误'
          break
        case 501:
          message = '服务未实现'
          break
        case 502:
          message = '网关错误'
          break
        case 503:
          message = '服务不可用'
          break
        case 504:
          message = '网关超时'
          break
        case 505:
          message = 'HTTP版本不受支持'
          break
        default:
          message = `连接错误${status}`
      }
      
      // 尝试从响应数据中获取错误信息
      if (data && typeof data === 'object' && data.message) {
        message = data.message
      }
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      if (error.code === 'ECONNABORTED') {
        message = '请求超时'
      } else if (error.message.includes('Network Error')) {
        message = '网络连接失败'
      } else {
        message = '网络错误'
      }
    } else {
      // 请求配置错误
      message = error.message || '请求配置错误'
    }
    
    // 显示错误信息
    if (config?.showError !== false) {
      ElMessage.error(message)
    }
    
    // 错误日志
    console.error('请求错误:', {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      message: error.message,
      data: error.response?.data
    })
    
    return Promise.reject(error)
  }
)

// 请求方法封装
class Request {
  // GET请求
  get<T = any>(url: string, params?: any, config?: RequestConfig): Promise<T> {
    return service.get(url, { params, ...config })
  }
  
  // POST请求
  post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return service.post(url, data, config)
  }
  
  // PUT请求
  put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return service.put(url, data, config)
  }
  
  // DELETE请求
  delete<T = any>(url: string, config?: RequestConfig): Promise<T> {
    return service.delete(url, config)
  }
  
  // PATCH请求
  patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<T> {
    return service.patch(url, data, config)
  }
  
  // 上传文件
  upload<T = any>(url: string, file: File, config?: RequestConfig): Promise<T> {
    const formData = new FormData()
    formData.append('file', file)
    
    return service.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      ...config
    })
  }
  
  // 下载文件
  download(url: string, params?: any, filename?: string): Promise<void> {
    return service.get(url, {
      params,
      responseType: 'blob',
      showLoading: true,
      loadingText: '正在下载...'
    }).then((data: any) => {
      const blob = new Blob([data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    })
  }
}

// 创建请求实例
const request = new Request()

export default request
export { request, service, type RequestConfig, type ApiResponse }