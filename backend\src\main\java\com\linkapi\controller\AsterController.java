package com.linkapi.controller;

import com.linkapi.common.Result;
import com.linkapi.service.AsterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 星辰API控制器
 * 提供星辰API相关的接口
 * 
 * <AUTHOR> API Team
 */
@Slf4j
@RestController
@RequestMapping("/api/aster")
@RequiredArgsConstructor
public class AsterController {

    private final AsterService asterService;

    /**
     * 测试连接
     */
    @GetMapping("/test-connection")
    public Result<Map<String, Object>> testConnection() {
        log.info("测试星辰API连接");
        Map<String, Object> result = asterService.testConnection();
        
        if ((Boolean) result.get("success")) {
            return Result.success("连接测试成功", result);
        } else {
            return Result.error((String) result.get("message"));
        }
    }

    /**
     * 获取授权信息
     */
    @PostMapping("/authorize")
    public Result<Map<String, Object>> getAuthorizeInfo(@RequestParam String appKey) {
        log.info("获取授权信息，appKey: {}", appKey);
        Map<String, Object> result = asterService.getAuthorizeInfo(appKey);
        
        if ((Boolean) result.get("success")) {
            return Result.success("获取授权信息成功", result);
        } else {
            return Result.error((String) result.get("message"));
        }
    }

    /**
     * 获取访问令牌
     */
    @PostMapping("/token")
    public Result<Map<String, Object>> getAppToken(@RequestParam String appKey, 
                                                   @RequestParam String appSecret) {
        log.info("获取访问令牌，appKey: {}", appKey);
        Map<String, Object> result = asterService.getAppToken(appKey, appSecret);
        
        if ((Boolean) result.get("success")) {
            return Result.success("获取访问令牌成功", result);
        } else {
            return Result.error((String) result.get("message"));
        }
    }

    /**
     * 获取商品列表
     */
    @GetMapping("/materials")
    public Result<Map<String, Object>> getMaterialList(@RequestParam(required = false) String appToken) {
        log.info("获取商品列表");
        Map<String, Object> result = asterService.getMaterialList(appToken);
        
        if ((Boolean) result.get("success")) {
            return Result.success("获取商品列表成功", result);
        } else {
            return Result.error((String) result.get("message"));
        }
    }

    /**
     * 获取当前令牌
     */
    @GetMapping("/current-token")
    public Result<Map<String, Object>> getCurrentToken() {
        log.info("获取当前令牌");
        String currentToken = asterService.getCurrentAppToken();
        
        Map<String, Object> data = new HashMap<>();
        data.put("appToken", currentToken);
        
        return Result.success("获取当前令牌成功", data);
    }

    /**
     * 完整同步测试
     */
    @PostMapping("/sync-test")
    public Result<Map<String, Object>> syncTest(@RequestParam String appKey, 
                                                @RequestParam String appSecret) {
        log.info("开始完整同步测试，appKey: {}", appKey);
        
        Map<String, Object> finalResult = new HashMap<>();
        Map<String, Object> results = new HashMap<>();
        
        try {
            // 1. 获取授权信息
            log.info("步骤1: 获取授权信息");
            Map<String, Object> authorizeResult = asterService.getAuthorizeInfo(appKey);
            results.put("authorize", authorizeResult);
            
            if (!(Boolean) authorizeResult.get("success")) {
                finalResult.put("success", false);
                finalResult.put("step", "authorize");
                finalResult.put("message", "获取授权信息失败: " + authorizeResult.get("message"));
                finalResult.put("results", results);
                return Result.error("同步测试失败");
            }
            
            // 2. 获取访问令牌
            log.info("步骤2: 获取访问令牌");
            Map<String, Object> tokenResult = asterService.getAppToken(appKey, appSecret);
            results.put("token", tokenResult);
            
            if (!(Boolean) tokenResult.get("success")) {
                finalResult.put("success", false);
                finalResult.put("step", "token");
                finalResult.put("message", "获取访问令牌失败: " + tokenResult.get("message"));
                finalResult.put("results", results);
                return Result.error("同步测试失败");
            }
            
            // 3. 获取商品列表
            log.info("步骤3: 获取商品列表");
            Map<String, Object> materialsResult = asterService.getMaterialList(null);
            results.put("materials", materialsResult);
            
            if (!(Boolean) materialsResult.get("success")) {
                finalResult.put("success", false);
                finalResult.put("step", "materials");
                finalResult.put("message", "获取商品列表失败: " + materialsResult.get("message"));
                finalResult.put("results", results);
                return Result.error("同步测试失败");
            }
            
            // 所有步骤成功
            finalResult.put("success", true);
            finalResult.put("message", "完整同步测试成功");
            finalResult.put("results", results);
            
            log.info("完整同步测试成功");
            return Result.success("完整同步测试成功", finalResult);
            
        } catch (Exception e) {
            log.error("完整同步测试异常", e);
            finalResult.put("success", false);
            finalResult.put("message", "同步测试异常: " + e.getMessage());
            finalResult.put("results", results);
            finalResult.put("error", e.getMessage());
            return Result.error("同步测试异常");
        }
    }
}
