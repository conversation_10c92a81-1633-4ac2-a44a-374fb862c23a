<template>
  <div class="app-wrapper" :class="classObj">
    <!-- 侧边栏 -->
    <sidebar class="sidebar-container" />
    
    <!-- 主内容区域 -->
    <div class="main-container">
      <!-- 顶部导航栏 -->
      <navbar />
      
      <!-- 标签页导航 -->
      <tags-view v-if="needTagsView" />
      
      <!-- 页面内容 -->
      <app-main />
    </div>
    
    <!-- 设置面板 -->
    <settings v-if="showSettings" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useAppStore } from '@/stores/app'
import { useSettingsStore } from '@/stores/settings'
import Sidebar from './components/Sidebar/index.vue'
import Navbar from './components/Navbar/index.vue'
import AppMain from './components/AppMain.vue'
import TagsView from './components/TagsView/index.vue'
import Settings from './components/Settings/index.vue'

const appStore = useAppStore()
const settingsStore = useSettingsStore()

// 计算样式类
const classObj = computed(() => {
  return {
    hideSidebar: !appStore.sidebar.opened,
    openSidebar: appStore.sidebar.opened,
    withoutAnimation: appStore.sidebar.withoutAnimation,
    mobile: appStore.device === 'mobile'
  }
})

// 是否显示标签页
const needTagsView = computed(() => settingsStore.tagsView)

// 是否显示设置面板
const showSettings = computed(() => settingsStore.showSettings)
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.app-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
  
  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.sidebar-container {
  transition: width 0.28s;
  width: $sidebar-width !important;
  background-color: #304156;
  height: 100%;
  position: fixed;
  font-size: 0px;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 1001;
  overflow: hidden;
  
  // 重置 element-ui 的 css
  .horizontal-collapse-transition {
    transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
  }
  
  .scrollbar-wrapper {
    overflow-x: hidden !important;
  }
  
  .el-scrollbar__bar.is-vertical {
    right: 0px;
  }
  
  .el-scrollbar {
    height: 100%;
  }
  
  &.has-logo {
    .el-scrollbar {
      height: calc(100% - 50px);
    }
  }
  
  .is-horizontal {
    display: none;
  }
  
  a {
    display: inline-block;
    width: 100%;
    overflow: hidden;
  }
  
  .svg-icon {
    margin-right: 16px;
  }
  
  .sub-el-icon {
    margin-right: 12px;
    margin-left: -2px;
  }
}

.hideSidebar {
  .sidebar-container {
    width: 54px !important;
  }
  
  .main-container {
    margin-left: 54px;
  }
  
  .submenu-title-noDropdown {
    padding: 0 !important;
    position: relative;
    
    .el-tooltip {
      padding: 0 !important;
      
      .svg-icon {
        margin-left: 20px;
      }
      
      .sub-el-icon {
        margin-left: 19px;
      }
    }
  }
  
  .el-submenu {
    overflow: hidden;
    
    & > .el-submenu__title {
      padding: 0 !important;
      
      .svg-icon {
        margin-left: 20px;
      }
      
      .sub-el-icon {
        margin-left: 19px;
      }
      
      .el-submenu__icon-arrow {
        display: none;
      }
    }
  }
  
  .el-menu--collapse {
    .el-submenu {
      & > .el-submenu__title {
        & > span {
          height: 0;
          width: 0;
          overflow: hidden;
          visibility: hidden;
          display: inline-block;
        }
      }
    }
  }
}

.el-menu--collapse .el-menu .el-submenu {
  min-width: $sidebar-width !important;
}

// mobile responsive
.mobile {
  .main-container {
    margin-left: 0px;
  }
  
  .sidebar-container {
    transition: transform .28s;
    width: $sidebar-width !important;
  }
  
  &.hideSidebar {
    .sidebar-container {
      pointer-events: none;
      transition-duration: 0.3s;
      transform: translate3d(-$sidebar-width, 0, 0);
    }
  }
}

.withoutAnimation {
  .main-container,
  .sidebar-container {
    transition: none;
  }
}

.main-container {
  min-height: 100%;
  transition: margin-left .28s;
  margin-left: $sidebar-width;
  position: relative;
}
</style>