package com.linkapi.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.FileCopyUtils;

import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

/**
 * 数据库初始化器
 * 在应用启动时执行SQL脚本初始化数据库表结构和基础数据
 */
@Slf4j
@Component
public class DatabaseInitializer implements CommandLineRunner {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public void run(String... args) throws Exception {
        try {
            // 检查用户表是否存在
            String checkTableSql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'linkapi' AND table_name = 'users'";
            Integer count = jdbcTemplate.queryForObject(checkTableSql, Integer.class);
            
            if (count == null || count == 0) {
                log.info("用户认证表不存在，开始初始化数据库...");
                executeSqlScript("sql/user_auth.sql");
                log.info("数据库初始化完成");
            } else {
                log.info("用户认证表已存在，跳过数据库初始化");
            }
        } catch (Exception e) {
            log.error("数据库初始化失败", e);
            // 不抛出异常，避免应用启动失败
        }
    }

    /**
     * 执行SQL脚本
     */
    private void executeSqlScript(String scriptPath) throws Exception {
        ClassPathResource resource = new ClassPathResource(scriptPath);
        if (!resource.exists()) {
            log.warn("SQL脚本文件不存在: {}", scriptPath);
            return;
        }

        String sqlScript = FileCopyUtils.copyToString(
            new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8)
        );

        // 分割SQL语句（以分号分隔）
        String[] sqlStatements = sqlScript.split(";");
        
        for (String sql : sqlStatements) {
            sql = sql.trim();
            if (!sql.isEmpty() && !sql.startsWith("--")) {
                try {
                    jdbcTemplate.execute(sql);
                    log.debug("执行SQL: {}", sql.substring(0, Math.min(sql.length(), 50)) + "...");
                } catch (Exception e) {
                    log.warn("执行SQL失败: {}, 错误: {}", sql.substring(0, Math.min(sql.length(), 50)), e.getMessage());
                    // 继续执行其他SQL语句
                }
            }
        }
    }
}