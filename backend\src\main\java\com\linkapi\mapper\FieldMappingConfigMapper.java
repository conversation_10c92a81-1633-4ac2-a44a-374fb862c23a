package com.linkapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkapi.dto.FieldMappingConfigDTO;
import com.linkapi.entity.FieldMappingConfig;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 字段映射配置Mapper接口
 * 
 * <AUTHOR> API Team
 */
@Mapper
public interface FieldMappingConfigMapper extends BaseMapper<FieldMappingConfig> {

    /**
     * 根据配置类型和模块类型查询配置
     */
    @Select("SELECT * FROM field_mapping_config WHERE deleted = 0 AND config_type = #{configType} AND module_type = #{moduleType} AND is_enabled = 1 ORDER BY priority ASC, created_time DESC")
    List<FieldMappingConfig> selectByTypeAndModule(@Param("configType") FieldMappingConfig.ConfigType configType, 
                                                   @Param("moduleType") String moduleType);

    /**
     * 根据源系统和目标系统查询配置
     */
    @Select("SELECT * FROM field_mapping_config WHERE deleted = 0 AND source_system = #{sourceSystem} AND target_system = #{targetSystem} AND is_enabled = 1 ORDER BY priority ASC")
    List<FieldMappingConfig> selectBySourceAndTarget(@Param("sourceSystem") String sourceSystem, 
                                                     @Param("targetSystem") String targetSystem);

    /**
     * 查询默认配置
     */
    @Select("SELECT * FROM field_mapping_config WHERE deleted = 0 AND config_type = #{configType} AND module_type = #{moduleType} AND is_default = 1 AND is_enabled = 1 LIMIT 1")
    FieldMappingConfig selectDefaultConfig(@Param("configType") FieldMappingConfig.ConfigType configType, 
                                          @Param("moduleType") String moduleType);

    /**
     * 查询启用的配置列表
     */
    @Select("SELECT * FROM field_mapping_config WHERE deleted = 0 AND is_enabled = 1 ORDER BY priority ASC, created_time DESC")
    List<FieldMappingConfig> selectEnabledConfigs();

    /**
     * 根据配置名称查询（用于重名检查）
     */
    @Select("SELECT COUNT(*) FROM field_mapping_config WHERE deleted = 0 AND config_name = #{configName} AND id != #{excludeId}")
    int countByConfigName(@Param("configName") String configName, @Param("excludeId") Long excludeId);

    /**
     * 更新配置状态
     */
    @Update("UPDATE field_mapping_config SET status = #{status}, updated_time = NOW() WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") FieldMappingConfig.ConfigStatus status);

    /**
     * 批量更新启用状态
     */
    @Update("UPDATE field_mapping_config SET is_enabled = #{isEnabled}, updated_time = NOW() WHERE id IN (${ids})")
    int batchUpdateEnabled(@Param("ids") String ids, @Param("isEnabled") Boolean isEnabled);

    /**
     * 设置默认配置（先清除其他默认配置）
     */
    @Update({
        "<script>",
        "UPDATE field_mapping_config SET is_default = CASE ",
        "WHEN id = #{configId} THEN 1 ",
        "ELSE 0 ",
        "END, updated_time = NOW() ",
        "WHERE deleted = 0 AND config_type = #{configType} AND module_type = #{moduleType}",
        "</script>"
    })
    int setDefaultConfig(@Param("configId") Long configId, 
                        @Param("configType") FieldMappingConfig.ConfigType configType, 
                        @Param("moduleType") String moduleType);

    /**
     * 分页查询配置预览信息
     */
    @Select({
        "<script>",
        "SELECT c.id, c.config_name, c.config_type, c.module_type, c.source_system, c.target_system, ",
        "c.config_version, c.is_enabled, c.is_default, c.status, c.created_time, c.updated_time, ",
        "COUNT(r.id) as rule_count ",
        "FROM field_mapping_config c ",
        "LEFT JOIN field_mapping_rule r ON c.id = r.config_id AND r.deleted = 0 ",
        "WHERE c.deleted = 0 ",
        "<if test='query.configName != null and query.configName != \"\"'>",
        "AND c.config_name LIKE CONCAT('%', #{query.configName}, '%') ",
        "</if>",
        "<if test='query.configType != null'>",
        "AND c.config_type = #{query.configType} ",
        "</if>",
        "<if test='query.moduleType != null and query.moduleType != \"\"'>",
        "AND c.module_type = #{query.moduleType} ",
        "</if>",
        "<if test='query.sourceSystem != null and query.sourceSystem != \"\"'>",
        "AND c.source_system = #{query.sourceSystem} ",
        "</if>",
        "<if test='query.targetSystem != null and query.targetSystem != \"\"'>",
        "AND c.target_system = #{query.targetSystem} ",
        "</if>",
        "<if test='query.isEnabled != null'>",
        "AND c.is_enabled = #{query.isEnabled} ",
        "</if>",
        "<if test='query.status != null'>",
        "AND c.status = #{query.status} ",
        "</if>",
        "<if test='query.createdTimeStart != null'>",
        "AND c.created_time >= #{query.createdTimeStart} ",
        "</if>",
        "<if test='query.createdTimeEnd != null'>",
        "AND c.created_time <= #{query.createdTimeEnd} ",
        "</if>",
        "GROUP BY c.id ",
        "ORDER BY c.priority ASC, c.created_time DESC",
        "</script>"
    })
    IPage<FieldMappingConfigDTO.ConfigPreviewDTO> selectConfigPreviewPage(Page<?> page, 
                                                                          @Param("query") FieldMappingConfigDTO.ConfigQueryDTO query);

    /**
     * 查询配置的完整信息（包含规则）
     */
    @Select("SELECT * FROM field_mapping_config WHERE deleted = 0 AND id = #{id}")
    @Results({
        @Result(property = "id", column = "id"),
        @Result(property = "fieldRules", column = "id", 
                many = @Many(select = "com.linkapi.mapper.FieldMappingRuleMapper.selectByConfigId"))
    })
    FieldMappingConfig selectConfigWithRules(@Param("id") Long id);

    /**
     * 复制配置
     */
    @Insert({
        "INSERT INTO field_mapping_config (config_name, config_description, config_type, module_type, ",
        "source_system, target_system, config_content, config_version, is_enabled, is_default, ",
        "priority, status, created_by, updated_by, created_time, updated_time) ",
        "SELECT CONCAT(config_name, '_copy_', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s')), ",
        "config_description, config_type, module_type, source_system, target_system, ",
        "config_content, config_version, 0, 0, priority + 1, 'DRAFT', ",
        "#{createdBy}, #{createdBy}, NOW(), NOW() ",
        "FROM field_mapping_config WHERE id = #{sourceId}"
    })
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int copyConfig(@Param("sourceId") Long sourceId, @Param("createdBy") String createdBy);

    /**
     * 获取配置统计信息
     */
    @Select({
        "SELECT ",
        "COUNT(*) as total_count, ",
        "SUM(CASE WHEN is_enabled = 1 THEN 1 ELSE 0 END) as enabled_count, ",
        "SUM(CASE WHEN status = 'PUBLISHED' THEN 1 ELSE 0 END) as published_count, ",
        "SUM(CASE WHEN is_default = 1 THEN 1 ELSE 0 END) as default_count ",
        "FROM field_mapping_config WHERE deleted = 0"
    })
    ConfigStatistics getConfigStatistics();

    /**
     * 配置统计信息
     */
    class ConfigStatistics {
        private Long totalCount;
        private Long enabledCount;
        private Long publishedCount;
        private Long defaultCount;

        // Getters and Setters
        public Long getTotalCount() { return totalCount; }
        public void setTotalCount(Long totalCount) { this.totalCount = totalCount; }

        public Long getEnabledCount() { return enabledCount; }
        public void setEnabledCount(Long enabledCount) { this.enabledCount = enabledCount; }

        public Long getPublishedCount() { return publishedCount; }
        public void setPublishedCount(Long publishedCount) { this.publishedCount = publishedCount; }

        public Long getDefaultCount() { return defaultCount; }
        public void setDefaultCount(Long defaultCount) { this.defaultCount = defaultCount; }
    }
}
