/**
 * 错误处理Composable
 * 提供统一的错误处理和用户反馈机制
 */

import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import type { MessageOptions } from 'element-plus'

/**
 * 错误类型枚举
 */
export enum ErrorType {
  NETWORK = 'NETWORK',
  BUSINESS = 'BUSINESS',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  SYSTEM = 'SYSTEM',
  UNKNOWN = 'UNKNOWN'
}

/**
 * 错误级别枚举
 */
export enum ErrorLevel {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

/**
 * 错误信息接口
 */
export interface ErrorInfo {
  code?: number | string
  message: string
  details?: string
  type: ErrorType
  level: ErrorLevel
  timestamp: Date
  requestId?: string
  traceId?: string
  suggestions?: string[]
  retryable?: boolean
}

/**
 * 错误处理配置
 */
export interface ErrorHandlerConfig {
  showMessage?: boolean
  showNotification?: boolean
  autoRetry?: boolean
  maxRetries?: number
  retryDelay?: number
  logError?: boolean
}

/**
 * 默认错误处理配置
 */
const defaultConfig: ErrorHandlerConfig = {
  showMessage: true,
  showNotification: false,
  autoRetry: false,
  maxRetries: 3,
  retryDelay: 1000,
  logError: true
}

/**
 * 错误处理Composable
 */
export function useErrorHandler(config: Partial<ErrorHandlerConfig> = {}) {
  const finalConfig = { ...defaultConfig, ...config }
  
  // 错误状态
  const errorState = reactive({
    hasError: false,
    currentError: null as ErrorInfo | null,
    errorHistory: [] as ErrorInfo[],
    retryCount: 0
  })
  
  // 加载状态
  const loading = ref(false)

  /**
   * 解析错误信息
   */
  const parseError = (error: any): ErrorInfo => {
    const timestamp = new Date()
    
    // 如果已经是ErrorInfo格式
    if (error && typeof error === 'object' && error.type && error.level) {
      return { ...error, timestamp }
    }
    
    // HTTP错误
    if (error?.response) {
      const { status, data } = error.response
      
      return {
        code: status,
        message: data?.message || getHttpErrorMessage(status),
        details: data?.details,
        type: getErrorTypeByStatus(status),
        level: getErrorLevelByStatus(status),
        timestamp,
        requestId: data?.requestId,
        traceId: data?.traceId,
        suggestions: data?.suggestions,
        retryable: isRetryableStatus(status)
      }
    }
    
    // 网络错误
    if (error?.code === 'NETWORK_ERROR' || error?.message?.includes('Network Error')) {
      return {
        message: '网络连接失败，请检查网络设置',
        type: ErrorType.NETWORK,
        level: ErrorLevel.HIGH,
        timestamp,
        retryable: true,
        suggestions: ['检查网络连接', '稍后重试', '联系技术支持']
      }
    }
    
    // 超时错误
    if (error?.code === 'TIMEOUT' || error?.message?.includes('timeout')) {
      return {
        message: '请求超时，请稍后重试',
        type: ErrorType.NETWORK,
        level: ErrorLevel.MEDIUM,
        timestamp,
        retryable: true,
        suggestions: ['稍后重试', '检查网络连接']
      }
    }
    
    // JavaScript错误
    if (error instanceof Error) {
      return {
        message: error.message,
        details: error.stack,
        type: ErrorType.SYSTEM,
        level: ErrorLevel.HIGH,
        timestamp,
        retryable: false
      }
    }
    
    // 字符串错误
    if (typeof error === 'string') {
      return {
        message: error,
        type: ErrorType.UNKNOWN,
        level: ErrorLevel.MEDIUM,
        timestamp,
        retryable: false
      }
    }
    
    // 未知错误
    return {
      message: '发生了未知错误',
      details: JSON.stringify(error),
      type: ErrorType.UNKNOWN,
      level: ErrorLevel.MEDIUM,
      timestamp,
      retryable: false
    }
  }

  /**
   * 处理错误
   */
  const handleError = async (error: any, context?: string): Promise<void> => {
    const errorInfo = parseError(error)
    
    // 更新错误状态
    errorState.hasError = true
    errorState.currentError = errorInfo
    errorState.errorHistory.push(errorInfo)
    
    // 记录错误日志
    if (finalConfig.logError) {
      console.error(`[ErrorHandler] ${context || 'Unknown context'}:`, errorInfo)
    }
    
    // 显示错误消息
    if (finalConfig.showMessage) {
      showErrorMessage(errorInfo)
    }
    
    // 显示错误通知
    if (finalConfig.showNotification) {
      showErrorNotification(errorInfo)
    }
    
    // 自动重试
    if (finalConfig.autoRetry && errorInfo.retryable && errorState.retryCount < (finalConfig.maxRetries || 3)) {
      await retryOperation(errorInfo)
    }
  }

  /**
   * 显示错误消息
   */
  const showErrorMessage = (errorInfo: ErrorInfo) => {
    const options: Partial<MessageOptions> = {
      message: errorInfo.message,
      type: 'error',
      duration: getMessageDuration(errorInfo.level),
      showClose: true
    }
    
    ElMessage(options)
  }

  /**
   * 显示错误通知
   */
  const showErrorNotification = (errorInfo: ErrorInfo) => {
    ElNotification({
      title: '错误提示',
      message: errorInfo.message,
      type: 'error',
      duration: getNotificationDuration(errorInfo.level),
      position: 'top-right'
    })
  }

  /**
   * 显示错误详情对话框
   */
  const showErrorDetails = (errorInfo: ErrorInfo) => {
    const content = `
      <div>
        <p><strong>错误信息：</strong>${errorInfo.message}</p>
        ${errorInfo.details ? `<p><strong>详细信息：</strong>${errorInfo.details}</p>` : ''}
        ${errorInfo.code ? `<p><strong>错误代码：</strong>${errorInfo.code}</p>` : ''}
        ${errorInfo.requestId ? `<p><strong>请求ID：</strong>${errorInfo.requestId}</p>` : ''}
        <p><strong>发生时间：</strong>${errorInfo.timestamp.toLocaleString()}</p>
        ${errorInfo.suggestions ? `
          <p><strong>建议解决方案：</strong></p>
          <ul>
            ${errorInfo.suggestions.map(s => `<li>${s}</li>`).join('')}
          </ul>
        ` : ''}
      </div>
    `
    
    ElMessageBox({
      title: '错误详情',
      message: content,
      type: 'error',
      dangerouslyUseHTMLString: true,
      confirmButtonText: '确定'
    })
  }

  /**
   * 重试操作
   */
  const retryOperation = async (errorInfo: ErrorInfo): Promise<void> => {
    if (!errorInfo.retryable) return
    
    errorState.retryCount++
    
    // 延迟重试
    await new Promise(resolve => setTimeout(resolve, finalConfig.retryDelay || 1000))
    
    // 这里应该重新执行失败的操作
    // 具体实现需要在使用时传入重试函数
  }

  /**
   * 清除错误状态
   */
  const clearError = () => {
    errorState.hasError = false
    errorState.currentError = null
    errorState.retryCount = 0
  }

  /**
   * 清除错误历史
   */
  const clearErrorHistory = () => {
    errorState.errorHistory = []
  }

  /**
   * 包装异步操作
   */
  const wrapAsyncOperation = async <T>(
    operation: () => Promise<T>,
    context?: string,
    retryFn?: () => Promise<T>
  ): Promise<T | null> => {
    loading.value = true
    clearError()
    
    try {
      const result = await operation()
      return result
    } catch (error) {
      await handleError(error, context)
      
      // 如果提供了重试函数且错误可重试
      if (retryFn && errorState.currentError?.retryable) {
        try {
          const retryResult = await retryFn()
          clearError()
          return retryResult
        } catch (retryError) {
          await handleError(retryError, `${context} (重试)`)
        }
      }
      
      return null
    } finally {
      loading.value = false
    }
  }

  // 辅助函数
  const getHttpErrorMessage = (status: number): string => {
    const messages: Record<number, string> = {
      400: '请求参数错误',
      401: '未授权，请重新登录',
      403: '访问被拒绝',
      404: '请求的资源不存在',
      408: '请求超时',
      409: '请求冲突',
      422: '请求参数验证失败',
      429: '请求过于频繁，请稍后重试',
      500: '服务器内部错误',
      502: '网关错误',
      503: '服务暂不可用',
      504: '网关超时'
    }
    
    return messages[status] || `HTTP错误 ${status}`
  }

  const getErrorTypeByStatus = (status: number): ErrorType => {
    if (status === 401) return ErrorType.AUTHENTICATION
    if (status === 403) return ErrorType.AUTHORIZATION
    if (status >= 400 && status < 500) return ErrorType.BUSINESS
    if (status >= 500) return ErrorType.SYSTEM
    return ErrorType.UNKNOWN
  }

  const getErrorLevelByStatus = (status: number): ErrorLevel => {
    if (status === 401 || status === 403) return ErrorLevel.HIGH
    if (status >= 500) return ErrorLevel.CRITICAL
    if (status >= 400) return ErrorLevel.MEDIUM
    return ErrorLevel.LOW
  }

  const isRetryableStatus = (status: number): boolean => {
    return [408, 429, 500, 502, 503, 504].includes(status)
  }

  const getMessageDuration = (level: ErrorLevel): number => {
    const durations = {
      [ErrorLevel.LOW]: 3000,
      [ErrorLevel.MEDIUM]: 5000,
      [ErrorLevel.HIGH]: 8000,
      [ErrorLevel.CRITICAL]: 0 // 不自动关闭
    }
    return durations[level]
  }

  const getNotificationDuration = (level: ErrorLevel): number => {
    const durations = {
      [ErrorLevel.LOW]: 4000,
      [ErrorLevel.MEDIUM]: 6000,
      [ErrorLevel.HIGH]: 10000,
      [ErrorLevel.CRITICAL]: 0 // 不自动关闭
    }
    return durations[level]
  }

  return {
    // 状态
    errorState,
    loading,
    
    // 方法
    handleError,
    clearError,
    clearErrorHistory,
    showErrorDetails,
    wrapAsyncOperation,
    
    // 工具方法
    parseError
  }
}
