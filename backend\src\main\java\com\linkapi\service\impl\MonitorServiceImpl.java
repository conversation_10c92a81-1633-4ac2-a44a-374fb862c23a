package com.linkapi.service.impl;

import com.linkapi.service.MonitorService;
import org.springframework.stereotype.Service;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.OperatingSystemMXBean;
import java.lang.management.RuntimeMXBean;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 系统监控服务实现类
 */
@Service
public class MonitorServiceImpl implements MonitorService {

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public Map<String, Object> getSystemInfo() {
        Map<String, Object> systemInfo = new HashMap<>();
        
        try {
            // 获取操作系统信息
            OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
            RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            
            // CPU使用率 (模拟数据，因为标准MXBean不提供此方法)
            double cpuUsage = Math.random() * 30 + 20;
            
            // 内存信息
            long totalMemory = memoryBean.getHeapMemoryUsage().getMax();
            long usedMemory = memoryBean.getHeapMemoryUsage().getUsed();
            double memoryUsage = (double) usedMemory / totalMemory * 100;
            
            // 磁盘使用率 (简化)
            double diskUsage = Math.random() * 20 + 30;
            
            // 网络速度 (模拟)
            String networkSpeed = String.format("%.1f MB/s", Math.random() * 100 + 50);
            
            // 启动时间
            long startTime = runtimeBean.getStartTime();
            String startTimeStr = dateFormat.format(new Date(startTime));
            
            // 运行时长
            long uptime = runtimeBean.getUptime();
            String uptimeStr = formatUptime(uptime);
            
            systemInfo.put("cpuUsage", Math.round(cpuUsage));
            systemInfo.put("memoryUsage", Math.round(memoryUsage));
            systemInfo.put("diskUsage", Math.round(diskUsage));
            systemInfo.put("networkSpeed", networkSpeed);
            systemInfo.put("os", osBean.getName() + " " + osBean.getVersion());
            systemInfo.put("javaVersion", System.getProperty("java.version"));
            systemInfo.put("startTime", startTimeStr);
            systemInfo.put("uptime", uptimeStr);
            systemInfo.put("totalMemory", formatBytes(totalMemory));
            systemInfo.put("freeMemory", formatBytes(totalMemory - usedMemory));
            
        } catch (Exception e) {
            // 如果获取真实数据失败，返回模拟数据
            systemInfo.put("cpuUsage", 45);
            systemInfo.put("memoryUsage", 68);
            systemInfo.put("diskUsage", 32);
            systemInfo.put("networkSpeed", "125 MB/s");
            systemInfo.put("os", "Windows 10");
            systemInfo.put("javaVersion", System.getProperty("java.version"));
            systemInfo.put("startTime", LocalDateTime.now().minusHours(2).format(formatter));
            systemInfo.put("uptime", "2小时15分钟");
            systemInfo.put("totalMemory", "8.0 GB");
            systemInfo.put("freeMemory", "2.6 GB");
        }
        
        return systemInfo;
    }

    @Override
    public Map<String, Object> getApplicationInfo() {
        Map<String, Object> appInfo = new HashMap<>();
        
        try {
            // 获取线程信息
            int activeThreads = Thread.activeCount();
            
            // 模拟其他应用信息
            appInfo.put("name", "Link API System");
            appInfo.put("version", "1.0.0");
            appInfo.put("activeThreads", activeThreads);
            appInfo.put("dbConnections", (int)(Math.random() * 5) + 5);
            appInfo.put("cacheHitRate", (int)(Math.random() * 10) + 85);
            appInfo.put("todayDocuments", (int)(Math.random() * 500) + 1000);
            
        } catch (Exception e) {
            // 默认值
            appInfo.put("name", "Link API System");
            appInfo.put("version", "1.0.0");
            appInfo.put("activeThreads", 25);
            appInfo.put("dbConnections", 8);
            appInfo.put("cacheHitRate", 92);
            appInfo.put("todayDocuments", 1247);
        }
        
        return appInfo;
    }

    @Override
    public Object getServiceStatus() {
        List<Map<String, Object>> services = new ArrayList<>();
        
        // Web服务
        Map<String, Object> webService = new HashMap<>();
        webService.put("name", "Web服务");
        webService.put("status", "运行中");
        webService.put("port", "8080");
        webService.put("lastCheck", LocalDateTime.now().format(formatter));
        webService.put("responseTime", "15ms");
        services.add(webService);
        
        // API网关
        Map<String, Object> apiGateway = new HashMap<>();
        apiGateway.put("name", "API网关");
        apiGateway.put("status", "运行中");
        apiGateway.put("port", "8081");
        apiGateway.put("lastCheck", LocalDateTime.now().format(formatter));
        apiGateway.put("responseTime", "8ms");
        services.add(apiGateway);
        
        // Redis缓存
        Map<String, Object> redis = new HashMap<>();
        redis.put("name", "Redis缓存");
        redis.put("status", "运行中");
        redis.put("port", "6379");
        redis.put("lastCheck", LocalDateTime.now().format(formatter));
        redis.put("responseTime", "2ms");
        services.add(redis);
        
        // MySQL数据库
        Map<String, Object> mysql = new HashMap<>();
        mysql.put("name", "MySQL数据库");
        mysql.put("status", "运行中");
        mysql.put("port", "3306");
        mysql.put("lastCheck", LocalDateTime.now().format(formatter));
        mysql.put("responseTime", "12ms");
        services.add(mysql);
        
        return services;
    }

    @Override
    public Map<String, Object> checkService(String serviceName) {
        Map<String, Object> result = new HashMap<>();
        
        // 模拟服务检查
        result.put("serviceName", serviceName);
        result.put("status", "运行中");
        result.put("lastCheck", LocalDateTime.now().format(formatter));
        result.put("responseTime", (int)(Math.random() * 20) + 5 + "ms");
        result.put("message", "服务检查完成，运行正常");
        
        return result;
    }

    @Override
    public Object getRealtimeData(String type) {
        Map<String, Object> data = new HashMap<>();
        List<Map<String, Object>> timeSeriesData = new ArrayList<>();
        
        // 生成最近10分钟的模拟数据
        LocalDateTime now = LocalDateTime.now();
        for (int i = 9; i >= 0; i--) {
            Map<String, Object> point = new HashMap<>();
            LocalDateTime time = now.minusMinutes(i);
            point.put("time", time.format(DateTimeFormatter.ofPattern("HH:mm")));
            
            switch (type.toLowerCase()) {
                case "cpu":
                    point.put("value", (int)(Math.random() * 30) + 20);
                    break;
                case "memory":
                    point.put("value", (int)(Math.random() * 20) + 60);
                    break;
                case "network":
                    point.put("value", (int)(Math.random() * 50) + 50);
                    break;
                default:
                    point.put("value", (int)(Math.random() * 50) + 25);
            }
            
            timeSeriesData.add(point);
        }
        
        data.put("type", type);
        data.put("data", timeSeriesData);
        data.put("unit", type.equals("network") ? "MB/s" : "%");
        
        return data;
    }
    
    /**
     * 格式化运行时长
     */
    private String formatUptime(long uptime) {
        long seconds = uptime / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        if (days > 0) {
            return String.format("%d天%d小时%d分钟", days, hours % 24, minutes % 60);
        } else if (hours > 0) {
            return String.format("%d小时%d分钟", hours, minutes % 60);
        } else {
            return String.format("%d分钟", minutes);
        }
    }
    
    /**
     * 格式化字节数
     */
    private String formatBytes(long bytes) {
        if (bytes < 0) return "0 B";
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = bytes;
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.1f %s", size, units[unitIndex]);
    }
}