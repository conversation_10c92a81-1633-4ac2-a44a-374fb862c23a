package com.linkapi.service;

import com.kingdee.service.ApiException;
import com.kingdee.service.Configuration;
import com.kingdee.service.ApiClient;
import com.kingdee.service.data.api.AuthorizeApi;
import com.kingdee.service.data.api.AppTokenApi;
import com.kingdee.service.data.api.MaterialApi;
import com.kingdee.service.data.entity.*;
import com.kingdee.service.unit.SHAUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 星辰API服务类
 * 提供授权、令牌获取和业务接口调用功能
 */
@Slf4j
@Service
public class AsterService {
    
    @Value("${app.kingdee.client_id}")
    private String clientId;
    
    @Value("${app.kingdee.client_secret}")
    private String clientSecret;
    
    private String currentAppToken;
    
    /**
     * 获取授权信息
     * @param appKey 应用密钥
     * @return 授权结果
     */
    public Map<String, Object> getAuthorizeInfo(String appKey) {
        Map<String, Object> result = new HashMap<>();
        try {
            AuthorizeApi authorizeApi = new AuthorizeApi();
            List<AsterAuthorizeRes> asterAuthorizeResList = authorizeApi.asterAuthorize(appKey);
            
            result.put("success", true);
            result.put("data", asterAuthorizeResList);
            result.put("message", "获取授权信息成功");
            
            log.info("获取授权信息成功，appKey: {}", appKey);
            
        } catch (ApiException e) {
            log.error("获取授权信息失败，appKey: {}, 错误: {}", appKey, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "获取授权信息失败: " + e.getMessage());
            result.put("error", e.getResponseBody());
        }
        return result;
    }
    
    /**
     * 获取访问令牌
     * @param appKey 应用密钥
     * @param appSecret 应用秘钥
     * @return 令牌结果
     */
    public Map<String, Object> getAppToken(String appKey, String appSecret) {
        Map<String, Object> result = new HashMap<>();
        try {
            AppTokenApi appTokenApi = new AppTokenApi();
            
            // 生成应用签名
            String appSignature = SHAUtil.SHA256HMAC(appKey, appSecret);
            appSignature = Base64.getEncoder().encodeToString(appSignature.getBytes());
            
            AsterAppTokenRes asterAppTokenRes = appTokenApi.asterAppToken(appKey, appSignature, null);
            
            // 保存当前令牌
            if (asterAppTokenRes != null && asterAppTokenRes.getAppToken() != null) {
                this.currentAppToken = asterAppTokenRes.getAppToken();
            }
            
            result.put("success", true);
            result.put("data", asterAppTokenRes);
            result.put("message", "获取访问令牌成功");
            
            log.info("获取访问令牌成功，appKey: {}", appKey);
            
        } catch (ApiException e) {
            log.error("获取访问令牌失败，appKey: {}, 错误: {}", appKey, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "获取访问令牌失败: " + e.getMessage());
            result.put("error", e.getResponseBody());
        }
        return result;
    }
    
    /**
     * 获取商品列表
     * @param appToken 访问令牌（可选，如果不提供则使用当前保存的令牌）
     * @return 商品列表结果
     */
    public Map<String, Object> getMaterialList(String appToken) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 设置访问令牌
            String tokenToUse = appToken != null ? appToken : this.currentAppToken;
            if (tokenToUse == null) {
                result.put("success", false);
                result.put("message", "访问令牌为空，请先获取令牌");
                return result;
            }
            
            ApiClient defaultApiClient = Configuration.getDefaultApiClient();
            defaultApiClient.setAppToken(tokenToUse);
            
            MaterialApi materialApi = new MaterialApi();
            MaterialMaterialListReq materialMaterialListReq = new MaterialMaterialListReq();
            
            MaterialListReply materialListReply = materialApi.materialMaterialList(materialMaterialListReq);
            
            result.put("success", true);
            result.put("data", materialListReply);
            result.put("message", "获取商品列表成功");
            
            log.info("获取商品列表成功");
            
        } catch (ApiException e) {
            log.error("获取商品列表失败，错误: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "获取商品列表失败: " + e.getMessage());
            result.put("error", e.getResponseBody());
        }
        return result;
    }
    
    /**
     * 测试API连接
     * @return 连接测试结果
     */
    public Map<String, Object> testConnection() {
        Map<String, Object> result = new HashMap<>();
        try {
            // 测试基本配置
            if (clientId == null || clientId.equals("xxx")) {
                result.put("success", false);
                result.put("message", "客户端ID未配置或使用默认值");
                return result;
            }
            
            if (clientSecret == null || clientSecret.equals("xxx")) {
                result.put("success", false);
                result.put("message", "客户端密钥未配置或使用默认值");
                return result;
            }
            
            // 测试API客户端初始化
            ApiClient defaultApiClient = Configuration.getDefaultApiClient();
            if (defaultApiClient == null) {
                result.put("success", false);
                result.put("message", "API客户端初始化失败");
                return result;
            }
            
            result.put("success", true);
            result.put("message", "API连接配置正常");
            result.put("clientId", clientId.substring(0, Math.min(clientId.length(), 6)) + "***");
            
            log.info("API连接测试成功");
            
        } catch (Exception e) {
            log.error("API连接测试失败，错误: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "API连接测试失败: " + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取当前保存的访问令牌
     * @return 当前访问令牌
     */
    public String getCurrentAppToken() {
        return currentAppToken;
    }
    
    /**
     * 设置访问令牌
     * @param appToken 访问令牌
     */
    public void setCurrentAppToken(String appToken) {
        this.currentAppToken = appToken;
    }
}