package com.linkapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linkapi.entity.WarehouseMapping;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * 仓库映射Mapper接口
 * 
 * <AUTHOR> API Team
 */
@Mapper
public interface WarehouseMappingMapper extends BaseMapper<WarehouseMapping> {

    /**
     * 根据外部编码查询映射
     */
    @Select("SELECT * FROM warehouse_mapping WHERE deleted = 0 AND external_code = #{externalCode} AND status = 'ACTIVE'")
    WarehouseMapping selectByExternalCode(@Param("externalCode") String externalCode);

    /**
     * 根据金蝶编码查询映射
     */
    @Select("SELECT * FROM warehouse_mapping WHERE deleted = 0 AND kingdee_code = #{kingdeeCode} AND status = 'ACTIVE'")
    WarehouseMapping selectByKingdeeCode(@Param("kingdeeCode") String kingdeeCode);

    /**
     * 查询所有有效映射
     */
    @Select("SELECT * FROM warehouse_mapping WHERE deleted = 0 AND status = 'ACTIVE' ORDER BY created_time DESC")
    List<WarehouseMapping> selectActiveMapping();

    /**
     * 批量插入映射
     */
    int batchInsert(@Param("list") List<WarehouseMapping> mappings);

    /**
     * 统计映射数量
     */
    @Select("SELECT " +
            "COUNT(*) as total_count, " +
            "SUM(CASE WHEN status = 'ACTIVE' THEN 1 ELSE 0 END) as active_count, " +
            "SUM(CASE WHEN status = 'INACTIVE' THEN 1 ELSE 0 END) as inactive_count " +
            "FROM warehouse_mapping WHERE deleted = 0")
    Map<String, Object> selectMappingStatistics();

    /**
     * 查询未映射的仓库
     */
    @Select("SELECT DISTINCT d.warehouse_name " +
            "FROM documents d " +
            "LEFT JOIN warehouse_mapping wm ON d.warehouse_name = wm.external_code AND wm.deleted = 0 " +
            "WHERE d.deleted = 0 AND wm.id IS NULL " +
            "ORDER BY d.warehouse_name")
    List<Map<String, Object>> selectUnmappedWarehouses();

    /**
     * 根据关键词搜索映射
     */
    @Select("SELECT * FROM warehouse_mapping " +
            "WHERE deleted = 0 " +
            "AND (external_code LIKE CONCAT('%', #{keyword}, '%') " +
            "OR external_name LIKE CONCAT('%', #{keyword}, '%') " +
            "OR kingdee_code LIKE CONCAT('%', #{keyword}, '%') " +
            "OR kingdee_name LIKE CONCAT('%', #{keyword}, '%')) " +
            "ORDER BY created_time DESC")
    List<WarehouseMapping> searchMapping(@Param("keyword") String keyword);
}