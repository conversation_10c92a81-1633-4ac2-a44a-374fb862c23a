/**
 * 通用组件导出
 * 统一导出所有可复用的通用组件
 */

import DataTable from './DataTable.vue'
import BaseForm from './BaseForm.vue'
import BaseDialog from './BaseDialog.vue'

// 导出组件
export {
  DataTable,
  BaseForm,
  BaseDialog
}

// 默认导出
export default {
  DataTable,
  BaseForm,
  BaseDialog
}

// 组件安装函数（用于全局注册）
export const installCommonComponents = (app: any) => {
  app.component('DataTable', DataTable)
  app.component('BaseForm', BaseForm)
  app.component('BaseDialog', BaseDialog)
}
