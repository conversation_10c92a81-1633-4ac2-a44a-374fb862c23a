package com.linkapi;


import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Link API 应用启动类
 * 接口系统 - 旺店通到金蝶云星辰数据同步
 * 
 * <AUTHOR> API Team
 * @version 1.0.0
 */
@SpringBootApplication
@EnableTransactionManagement
@EnableAsync
@EnableScheduling
@MapperScan("com.linkapi.mapper")
public class LinkApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(LinkApiApplication.class, args);
        System.out.println("");
        System.out.println("  _     _       _      _    ____ ___ ");
        System.out.println(" | |   (_)_ __ | | __ / \\  |  _ \\_ _|");
        System.out.println(" | |   | | '_ \\| |/ // _ \\ | |_) | | ");
        System.out.println(" | |___| | | | |   </ ___ \\|  __/| | ");
        System.out.println(" |_____|_|_| |_|_|\\_\\/_/   \\_\\_|  |___|");
        System.out.println("");
        System.out.println(":: Link API Backend Started Successfully ::");
        System.out.println(":: Version: 1.0.0 ::");
        System.out.println(":: Port: 8080 ::");
        System.out.println("");
    }
}