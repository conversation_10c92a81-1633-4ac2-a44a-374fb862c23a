<template>
  <div class="system-monitor">
    <!-- 系统概览 -->
    <el-row :gutter="20" class="overview">
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon cpu">
              <el-icon><Cpu /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ systemInfo.cpuUsage }}%</div>
              <div class="metric-label">CPU使用率</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon memory">
              <el-icon><Monitor /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ systemInfo.memoryUsage }}%</div>
              <div class="metric-label">内存使用率</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon disk">
              <el-icon><FolderOpened /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ systemInfo.diskUsage }}%</div>
              <div class="metric-label">磁盘使用率</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="metric-card">
          <div class="metric-content">
            <div class="metric-icon network">
              <el-icon><Connection /></el-icon>
            </div>
            <div class="metric-info">
              <div class="metric-value">{{ systemInfo.networkSpeed }}</div>
              <div class="metric-label">网络速度</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细信息 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>系统信息</span>
          </template>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="操作系统">{{ systemInfo.os }}</el-descriptions-item>
            <el-descriptions-item label="Java版本">{{ systemInfo.javaVersion }}</el-descriptions-item>
            <el-descriptions-item label="启动时间">{{ systemInfo.startTime }}</el-descriptions-item>
            <el-descriptions-item label="运行时长">{{ systemInfo.uptime }}</el-descriptions-item>
            <el-descriptions-item label="总内存">{{ systemInfo.totalMemory }}</el-descriptions-item>
            <el-descriptions-item label="可用内存">{{ systemInfo.freeMemory }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>应用信息</span>
          </template>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="应用名称">{{ appInfo.name }}</el-descriptions-item>
            <el-descriptions-item label="应用版本">{{ appInfo.version }}</el-descriptions-item>
            <el-descriptions-item label="活跃线程数">{{ appInfo.activeThreads }}</el-descriptions-item>
            <el-descriptions-item label="数据库连接数">{{ appInfo.dbConnections }}</el-descriptions-item>
            <el-descriptions-item label="缓存命中率">{{ appInfo.cacheHitRate }}%</el-descriptions-item>
            <el-descriptions-item label="今日处理单据">{{ appInfo.todayDocuments }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
    </el-row>

    <!-- 实时监控图表 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <div class="chart-header">
              <span>实时监控</span>
              <el-button-group>
                <el-button :type="chartType === 'cpu' ? 'primary' : ''" @click="chartType = 'cpu'">CPU</el-button>
                <el-button :type="chartType === 'memory' ? 'primary' : ''" @click="chartType = 'memory'">内存</el-button>
                <el-button :type="chartType === 'network' ? 'primary' : ''" @click="chartType = 'network'">网络</el-button>
              </el-button-group>
            </div>
          </template>
          <div class="chart-container">
            <div class="chart-placeholder">
              {{ chartType.toUpperCase() }} 监控图表 (图表组件待集成)
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 服务状态 -->
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>服务状态</span>
          </template>
          <el-table :data="serviceStatus" border>
            <el-table-column prop="name" label="服务名称" />
            <el-table-column prop="status" label="状态">
              <template #default="{ row }">
                <el-tag :type="row.status === '运行中' ? 'success' : 'danger'">
                  {{ row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="port" label="端口" />
            <el-table-column prop="lastCheck" label="最后检查时间" />
            <el-table-column prop="responseTime" label="响应时间" />
            <el-table-column label="操作" width="150">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="checkService(row)">检查</el-button>
                <el-button 
                  :type="row.status === '运行中' ? 'danger' : 'success'" 
                  size="small" 
                  @click="toggleService(row)"
                >
                  {{ row.status === '运行中' ? '停止' : '启动' }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  getSystemInfo, 
  getApplicationInfo, 
  getServicesList, 
  checkServiceStatus, 
  getRealtimeData 
} from '@/api/monitor'

const chartType = ref('cpu')
let timer: NodeJS.Timeout | null = null
const loading = ref(false)

const systemInfo = reactive({
  cpuUsage: 0,
  memoryUsage: 0,
  diskUsage: 0,
  networkSpeed: '0 MB/s',
  os: '',
  javaVersion: '',
  startTime: '',
  uptime: '',
  totalMemory: '',
  freeMemory: ''
})

const appInfo = reactive({
  name: '',
  version: '',
  activeThreads: 0,
  dbConnections: 0,
  cacheHitRate: 0,
  todayDocuments: 0
})

const serviceStatus = ref([])

// 加载系统信息
const loadSystemInfo = async () => {
  try {
    const data = await getSystemInfo()
    Object.assign(systemInfo, data)
  } catch (error) {
    console.error('获取系统信息失败:', error)
    ElMessage.error('获取系统信息失败')
  }
}

// 加载应用信息
const loadApplicationInfo = async () => {
  try {
    const data = await getApplicationInfo()
    Object.assign(appInfo, data)
  } catch (error) {
    console.error('获取应用信息失败:', error)
    ElMessage.error('获取应用信息失败')
  }
}

// 加载服务状态
const loadServiceStatus = async () => {
  try {
    const data = await getServicesList()
    serviceStatus.value = data
  } catch (error) {
    console.error('获取服务状态失败:', error)
    ElMessage.error('获取服务状态失败')
  }
}

// 更新实时数据
const updateRealtimeData = async () => {
  try {
    // 获取CPU实时数据
    const cpuData = await getRealtimeData('cpu')
    if (cpuData && cpuData.value !== undefined) {
      systemInfo.cpuUsage = Math.round(cpuData.value)
    }
    
    // 获取内存实时数据
    const memoryData = await getRealtimeData('memory')
    if (memoryData && memoryData.value !== undefined) {
      systemInfo.memoryUsage = Math.round(memoryData.value)
    }
  } catch (error) {
    console.error('获取实时数据失败:', error)
  }
}

// 检查服务状态
const checkService = async (service: any) => {
  try {
    loading.value = true
    const result = await checkServiceStatus(service.name)
    service.status = result.status
    service.responseTime = result.responseTime
    service.lastCheck = new Date().toLocaleString()
    ElMessage.success(`检查服务 ${service.name} 完成`)
  } catch (error) {
    console.error('检查服务失败:', error)
    ElMessage.error(`检查服务 ${service.name} 失败`)
  } finally {
    loading.value = false
  }
}

// 切换服务状态（模拟操作）
const toggleService = (service: any) => {
  const action = service.status === '运行中' ? '停止' : '启动'
  service.status = service.status === '运行中' ? '已停止' : '运行中'
  service.lastCheck = new Date().toLocaleString()
  ElMessage.success(`${action}服务 ${service.name} 成功`)
}

// 初始化数据
const initData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadSystemInfo(),
      loadApplicationInfo(),
      loadServiceStatus()
    ])
  } catch (error) {
    console.error('初始化数据失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // 初始化数据
  initData()
  
  // 每5秒更新一次实时数据
  timer = setInterval(updateRealtimeData, 5000)
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>

<style scoped>
.system-monitor {
  padding: 20px;
}

.overview {
  margin-bottom: 20px;
}

.metric-card {
  height: 120px;
}

.metric-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 24px;
  color: white;
}

.metric-icon.cpu {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.metric-icon.memory {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.metric-icon.disk {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.metric-icon.network {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.metric-info {
  flex: 1;
}

.metric-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 14px;
  color: #909399;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  font-size: 18px;
  color: #909399;
  text-align: center;
}
</style>