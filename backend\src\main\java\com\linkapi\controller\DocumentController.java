package com.linkapi.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.linkapi.common.Result;
import com.linkapi.entity.Document;
import com.linkapi.service.DocumentService;
import com.linkapi.service.interfaces.DocumentServiceInterface;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文档控制器
 * 
 * <AUTHOR> API Team
 */
@Slf4j
@RestController
@RequestMapping("/document")
@RequiredArgsConstructor
public class DocumentController {

    private final DocumentService documentService;

    /**
     * 分页查询文档列表
     */
    @GetMapping("/list")
    public Result<Map<String, Object>> getDocumentList(
            @RequestParam(defaultValue = "1") int pageNum,
            @RequestParam(defaultValue = "10") int pageSize,
            @RequestParam(required = false) String documentType,
            @RequestParam(required = false) String status) {
        
        try {
            IPage<Document> page = documentService.getDocumentPage(pageNum, pageSize, documentType, status);
            
            Map<String, Object> data = new HashMap<>();
            data.put("list", page.getRecords());
            data.put("total", page.getTotal());
            data.put("pageNum", page.getCurrent());
            data.put("pageSize", page.getSize());
            data.put("pages", page.getPages());
            
            return Result.success("查询成功", data);
        } catch (Exception e) {
            log.error("查询文档列表失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID获取文档详情
     */
    @GetMapping("/{id}")
    public Result<Document> getDocumentById(@PathVariable Long id) {
        try {
            Document document = documentService.getDocumentById(id);
            if (document != null) {
                return Result.success("查询成功", document);
            } else {
                return Result.error("文档不存在");
            }
        } catch (Exception e) {
            log.error("查询文档详情失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据单据编号获取文档
     */
    @GetMapping("/no/{documentNo}")
    public Result<Document> getDocumentByNo(@PathVariable String documentNo) {
        try {
            Document document = documentService.getDocumentByNo(documentNo);
            if (document != null) {
                return Result.success("查询成功", document);
            } else {
                return Result.error("文档不存在");
            }
        } catch (Exception e) {
            log.error("根据单据编号查询文档失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 创建文档
     */
    @PostMapping
    public Result<Document> createDocument(@RequestBody Document document) {
        try {
            Document createdDocument = documentService.createDocument(document);
            return Result.success("创建成功", createdDocument);
        } catch (Exception e) {
            log.error("创建文档失败", e);
            return Result.error("创建失败：" + e.getMessage());
        }
    }

    /**
     * 更新文档
     */
    @PutMapping("/{id}")
    public Result<Document> updateDocument(@PathVariable Long id, @RequestBody Document document) {
        try {
            document.setId(id);
            Document updatedDocument = documentService.updateDocument(document);
            return Result.success("更新成功", updatedDocument);
        } catch (Exception e) {
            log.error("更新文档失败", e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除文档
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteDocument(@PathVariable Long id) {
        try {
            boolean success = documentService.deleteDocument(id);
            if (success) {
                return Result.success("删除成功", "success");
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            log.error("删除文档失败", e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 获取待处理文档列表
     */
    @GetMapping("/pending")
    public Result<List<Document>> getPendingDocuments() {
        try {
            List<Document> documents = documentService.getPendingDocuments();
            return Result.success("查询成功", documents);
        } catch (Exception e) {
            log.error("查询待处理文档失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 更新文档状态
     */
    @PutMapping("/{id}/status")
    public Result<String> updateDocumentStatus(
            @PathVariable Long id,
            @RequestParam String status,
            @RequestParam(required = false) String errorMessage) {
        try {
            Document.DocumentStatus documentStatus = Document.DocumentStatus.valueOf(status);
            boolean success = documentService.updateDocumentStatus(id, documentStatus, errorMessage);
            if (success) {
                return Result.success("状态更新成功", "success");
            } else {
                return Result.error("状态更新失败");
            }
        } catch (IllegalArgumentException e) {
            return Result.error("无效的状态值：" + status);
        } catch (Exception e) {
            log.error("更新文档状态失败", e);
            return Result.error("状态更新失败：" + e.getMessage());
        }
    }

    /**
     * 重试文档处理
     */
    @PostMapping("/{id}/retry")
    public Result<String> retryDocument(@PathVariable Long id) {
        try {
            boolean success = documentService.incrementRetryCount(id);
            if (success) {
                // 这里可以触发重新处理逻辑
                return Result.success("重试成功", "success");
            } else {
                return Result.error("重试失败");
            }
        } catch (Exception e) {
            log.error("重试文档处理失败", e);
            return Result.error("重试失败：" + e.getMessage());
        }
    }

    /**
     * 获取文档统计信息
     */
    @GetMapping("/statistics")
    public Result<Map<String, Object>> getDocumentStatistics() {
        try {
            DocumentServiceInterface.DocumentStatistics statistics = documentService.getDocumentStatistics();

            Map<String, Object> data = new HashMap<>();
            data.put("totalCount", statistics.getTotalCount());
            data.put("pendingCount", statistics.getPendingCount());
            data.put("successCount", statistics.getSuccessCount());
            data.put("failedCount", statistics.getFailedCount());
            data.put("processingCount", statistics.getProcessingCount());

            // 计算成功率
            double successRate = statistics.getTotalCount() > 0 ?
                (double) statistics.getSuccessCount() / statistics.getTotalCount() * 100 : 0.0;
            data.put("successRate", Math.round(successRate * 100.0) / 100.0);

            return Result.success("查询成功", data);
        } catch (Exception e) {
            log.error("查询文档统计信息失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
}
