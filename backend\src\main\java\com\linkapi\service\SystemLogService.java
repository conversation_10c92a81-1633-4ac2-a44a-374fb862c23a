package com.linkapi.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkapi.entity.SystemLog;
import com.linkapi.mapper.SystemLogMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统日志服务类
 * 
 * <AUTHOR> API Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SystemLogService {

    private final SystemLogMapper systemLogMapper;

    /**
     * 分页查询系统日志
     */
    public IPage<SystemLog> getSystemLogPage(int pageNum, int pageSize, String logType, String module, String operator) {
        Page<SystemLog> page = new Page<>(pageNum, pageSize);
        QueryWrapper<SystemLog> queryWrapper = new QueryWrapper<>();
        
        if (logType != null && !logType.trim().isEmpty()) {
            queryWrapper.eq("log_type", logType);
        }
        
        if (module != null && !module.trim().isEmpty()) {
            queryWrapper.like("module", module);
        }
        
        if (operator != null && !operator.trim().isEmpty()) {
            queryWrapper.like("operator", operator);
        }
        
        queryWrapper.orderByDesc("created_time");
        
        return systemLogMapper.selectPage(page, queryWrapper);
    }

    /**
     * 记录信息日志
     */
    @Transactional
    public void logInfo(String module, String operation, String operator) {
        logInfo(module, operation, operator, null, null, null);
    }

    /**
     * 记录信息日志（带参数）
     */
    @Transactional
    public void logInfo(String businessType, String operation, String operator, String requestData, String responseData, Long executionTime) {
        SystemLog systemLog = new SystemLog();
        systemLog.setLogType("INFO");
        systemLog.setBusinessType(businessType);
        systemLog.setOperation(operation);
        systemLog.setOperator(operator);
        systemLog.setRequestData(requestData);
        systemLog.setResponseData(responseData);
        systemLog.setExecutionTime(executionTime);
        systemLog.setCreatedTime(LocalDateTime.now());

        systemLogMapper.insert(systemLog);
    }

    /**
     * 记录警告日志
     */
    @Transactional
    public void logWarn(String businessType, String operation, String operator, String message) {
        SystemLog systemLog = new SystemLog();
        systemLog.setLogType("WARN");
        systemLog.setBusinessType(businessType);
        systemLog.setOperation(operation);
        systemLog.setOperator(operator);
        systemLog.setErrorMessage(message);
        systemLog.setCreatedTime(LocalDateTime.now());

        systemLogMapper.insert(systemLog);
    }

    /**
     * 记录错误日志
     */
    @Transactional
    public void logError(String module, String operation, String operator, String errorMessage) {
        logError(module, operation, operator, errorMessage, null, null);
    }

    /**
     * 记录错误日志（带详细信息）
     */
    @Transactional
    public void logError(String businessType, String operation, String operator, String errorMessage, String requestData, String responseData) {
        SystemLog systemLog = new SystemLog();
        systemLog.setLogType("ERROR");
        systemLog.setBusinessType(businessType);
        systemLog.setOperation(operation);
        systemLog.setOperator(operator);
        systemLog.setErrorMessage(errorMessage);
        systemLog.setRequestData(requestData);
        systemLog.setResponseData(responseData);
        systemLog.setCreatedTime(LocalDateTime.now());

        systemLogMapper.insert(systemLog);
    }

    /**
     * 记录调试日志
     */
    @Transactional
    public void logDebug(String businessType, String operation, String operator, String message) {
        SystemLog systemLog = new SystemLog();
        systemLog.setLogType("DEBUG");
        systemLog.setBusinessType(businessType);
        systemLog.setOperation(operation);
        systemLog.setOperator(operator);
        systemLog.setErrorMessage(message);
        systemLog.setCreatedTime(LocalDateTime.now());

        systemLogMapper.insert(systemLog);
    }

    /**
     * 记录API请求日志
     */
    @Transactional
    public void logApiRequest(String businessType, String requestUrl, String requestData,
                             String responseData, String operator, String ipAddress, String userAgent, Long executionTime) {
        SystemLog systemLog = new SystemLog();
        systemLog.setLogType("INFO");
        systemLog.setBusinessType(businessType);
        systemLog.setOperation("API请求");
        systemLog.setRequestData(requestData);
        systemLog.setResponseData(responseData);
        systemLog.setOperator(operator);
        systemLog.setIpAddress(ipAddress);
        systemLog.setUserAgent(userAgent);
        systemLog.setExecutionTime(executionTime);
        systemLog.setCreatedTime(LocalDateTime.now());

        systemLogMapper.insert(systemLog);
    }

    /**
     * 根据ID获取日志
     */
    public SystemLog getSystemLogById(Long id) {
        return systemLogMapper.selectById(id);
    }

    /**
     * 删除日志
     */
    @Transactional
    public boolean deleteSystemLog(Long id) {
        return systemLogMapper.deleteById(id) > 0;
    }

    /**
     * 批量删除日志
     */
    @Transactional
    public boolean deleteSystemLogs(List<Long> ids) {
        return systemLogMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 清理过期日志（保留指定天数）
     */
    @Transactional
    public int cleanExpiredLogs(int keepDays) {
        LocalDateTime expireTime = LocalDateTime.now().minusDays(keepDays);
        QueryWrapper<SystemLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.lt("created_time", expireTime);
        return systemLogMapper.delete(queryWrapper);
    }

    /**
     * 获取日志统计信息
     */
    public LogStatistics getLogStatistics() {
        // 总日志数
        Long totalCount = systemLogMapper.selectCount(new QueryWrapper<>());
        
        // 今日日志数
        QueryWrapper<SystemLog> todayWrapper = new QueryWrapper<>();
        todayWrapper.ge("created_time", LocalDateTime.now().toLocalDate().atStartOfDay());
        Long todayCount = systemLogMapper.selectCount(todayWrapper);
        
        // 错误日志数
        QueryWrapper<SystemLog> errorWrapper = new QueryWrapper<>();
        errorWrapper.eq("log_type", "ERROR");
        Long errorCount = systemLogMapper.selectCount(errorWrapper);
        
        // 警告日志数
        QueryWrapper<SystemLog> warnWrapper = new QueryWrapper<>();
        warnWrapper.eq("log_type", "WARN");
        Long warnCount = systemLogMapper.selectCount(warnWrapper);
        
        return new LogStatistics(totalCount, todayCount, errorCount, warnCount);
    }

    /**
     * 日志统计信息内部类
     */
    public static class LogStatistics {
        private final Long totalCount;
        private final Long todayCount;
        private final Long errorCount;
        private final Long warnCount;

        public LogStatistics(Long totalCount, Long todayCount, Long errorCount, Long warnCount) {
            this.totalCount = totalCount;
            this.todayCount = todayCount;
            this.errorCount = errorCount;
            this.warnCount = warnCount;
        }

        // Getters
        public Long getTotalCount() { return totalCount; }
        public Long getTodayCount() { return todayCount; }
        public Long getErrorCount() { return errorCount; }
        public Long getWarnCount() { return warnCount; }
    }
}
