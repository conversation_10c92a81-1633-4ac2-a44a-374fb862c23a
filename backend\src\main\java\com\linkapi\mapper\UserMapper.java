package com.linkapi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.linkapi.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据用户名查询用户（包含角色和权限信息）
     *
     * @param username 用户名
     * @return 用户信息
     */
    @Select("SELECT u.*, " +
            "GROUP_CONCAT(DISTINCT r.role_code) as role_codes, " +
            "GROUP_CONCAT(DISTINCT p.permission_code) as permission_codes " +
            "FROM users u " +
            "LEFT JOIN user_roles ur ON u.id = ur.user_id " +
            "LEFT JOIN roles r ON ur.role_id = r.id AND r.deleted = 0 AND r.status = 'ACTIVE' " +
            "LEFT JOIN role_permissions rp ON r.id = rp.role_id " +
            "LEFT JOIN permissions p ON rp.permission_id = p.id AND p.deleted = 0 AND p.status = 'ACTIVE' " +
            "WHERE u.username = #{username} AND u.deleted = 0 " +
            "GROUP BY u.id")
    User findByUsernameWithRoles(@Param("username") String username);

    /**
     * 根据用户ID查询用户角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    @Select("SELECT r.* FROM roles r " +
            "INNER JOIN user_roles ur ON r.id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND r.deleted = 0 AND r.status = 'ACTIVE'")
    List<com.linkapi.entity.Role> findRolesByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID查询用户权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    @Select("SELECT DISTINCT p.* FROM permissions p " +
            "INNER JOIN role_permissions rp ON p.id = rp.permission_id " +
            "INNER JOIN roles r ON rp.role_id = r.id " +
            "INNER JOIN user_roles ur ON r.id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND p.deleted = 0 AND p.status = 'ACTIVE' " +
            "AND r.deleted = 0 AND r.status = 'ACTIVE' " +
            "ORDER BY p.sort_order")
    List<com.linkapi.entity.Permission> findPermissionsByUserId(@Param("userId") Long userId);

    /**
     * 更新用户最后登录信息
     *
     * @param userId 用户ID
     * @param loginTime 登录时间
     * @param loginIp 登录IP
     * @return 更新行数
     */
    @Update("UPDATE users SET last_login_time = #{loginTime}, last_login_ip = #{loginIp}, " +
            "login_count = login_count + 1, updated_time = NOW() " +
            "WHERE id = #{userId}")
    int updateLastLoginInfo(@Param("userId") Long userId, 
                           @Param("loginTime") LocalDateTime loginTime, 
                           @Param("loginIp") String loginIp);

    /**
     * 根据邮箱查询用户
     *
     * @param email 邮箱
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE email = #{email} AND deleted = 0")
    User findByEmail(@Param("email") String email);

    /**
     * 根据手机号查询用户
     *
     * @param phone 手机号
     * @return 用户信息
     */
    @Select("SELECT * FROM users WHERE phone = #{phone} AND deleted = 0")
    User findByPhone(@Param("phone") String phone);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @param excludeId 排除的用户ID（用于编辑时检查）
     * @return 存在数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM users WHERE username = #{username} AND deleted = 0 " +
            "<if test='excludeId != null'>AND id != #{excludeId}</if>" +
            "</script>")
    int countByUsername(@Param("username") String username, @Param("excludeId") Long excludeId);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @param excludeId 排除的用户ID（用于编辑时检查）
     * @return 存在数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM users WHERE email = #{email} AND deleted = 0 " +
            "<if test='excludeId != null'>AND id != #{excludeId}</if>" +
            "</script>")
    int countByEmail(@Param("email") String email, @Param("excludeId") Long excludeId);
}