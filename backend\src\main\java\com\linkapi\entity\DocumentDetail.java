package com.linkapi.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 单据明细表实体
 * 
 * <AUTHOR> API Team
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("document_details")
public class DocumentDetail {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 单据主表ID
     */
    @NotNull(message = "单据ID不能为空")
    @TableField("document_id")
    private Long documentId;

    /**
     * 行号
     */
    @NotNull(message = "行号不能为空")
    @Min(value = 1, message = "行号必须大于0")
    @TableField("line_no")
    private Integer lineNo;

    /**
     * 商品编码
     */
    @NotBlank(message = "商品编码不能为空")
    @Size(max = 100, message = "商品编码长度不能超过100个字符")
    @TableField("product_code")
    private String productCode;

    /**
     * 商品名称
     */
    @Size(max = 500, message = "商品名称长度不能超过500个字符")
    @TableField("product_name")
    private String productName;

    /**
     * 规格型号
     */
    @Size(max = 200, message = "规格型号长度不能超过200个字符")
    @TableField("specification")
    private String specification;

    /**
     * 单位
     */
    @Size(max = 50, message = "单位长度不能超过50个字符")
    @TableField("unit")
    private String unit;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空")
    @DecimalMin(value = "-999999999.9999", message = "数量超出范围")
    @DecimalMax(value = "999999999.9999", message = "数量超出范围")
    @TableField("quantity")
    private BigDecimal quantity;

    /**
     * 单价
     */
    @NotNull(message = "单价不能为空")
    @DecimalMin(value = "0", message = "单价不能为负数")
    @TableField("unit_price")
    private BigDecimal unitPrice;

    /**
     * 金额
     */
    @NotNull(message = "金额不能为空")
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 批次号
     */
    @Size(max = 100, message = "批次号长度不能超过100个字符")
    @TableField("batch_no")
    private String batchNo;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 逻辑删除标识
     */
    @TableLogic
    @TableField("deleted")
    private Boolean deleted;

    /**
     * 计算金额（数量 × 单价）
     */
    public void calculateAmount() {
        if (quantity != null && unitPrice != null) {
            this.amount = quantity.multiply(unitPrice);
        } else {
            this.amount = BigDecimal.ZERO;
        }
    }

    /**
     * 验证金额计算是否正确
     */
    public boolean isAmountValid() {
        if (quantity == null || unitPrice == null || amount == null) {
            return false;
        }
        BigDecimal calculatedAmount = quantity.multiply(unitPrice);
        return calculatedAmount.compareTo(amount) == 0;
    }

    /**
     * 检查是否为退货明细（数量为负数）
     */
    public boolean isReturn() {
        return quantity != null && quantity.compareTo(BigDecimal.ZERO) < 0;
    }

    /**
     * 获取绝对数量
     */
    public BigDecimal getAbsoluteQuantity() {
        return quantity != null ? quantity.abs() : BigDecimal.ZERO;
    }

    /**
     * 获取绝对金额
     */
    public BigDecimal getAbsoluteAmount() {
        return amount != null ? amount.abs() : BigDecimal.ZERO;
    }
}