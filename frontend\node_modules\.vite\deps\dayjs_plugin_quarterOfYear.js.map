{"version": 3, "sources": ["../../dayjs/plugin/quarterOfYear.js"], "sourcesContent": ["!function(t,n){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=n():\"function\"==typeof define&&define.amd?define(n):(t=\"undefined\"!=typeof globalThis?globalThis:t||self).dayjs_plugin_quarterOfYear=n()}(this,(function(){\"use strict\";var t=\"month\",n=\"quarter\";return function(e,i){var r=i.prototype;r.quarter=function(t){return this.$utils().u(t)?Math.ceil((this.month()+1)/3):this.month(this.month()%3+3*(t-1))};var s=r.add;r.add=function(e,i){return e=Number(e),this.$utils().p(i)===n?this.add(3*e,t):s.bind(this)(e,i)};var u=r.startOf;r.startOf=function(e,i){var r=this.$utils(),s=!!r.u(i)||i;if(r.p(e)===n){var o=this.quarter()-1;return s?this.month(3*o).startOf(t).startOf(\"day\"):this.month(3*o+2).endOf(t).endOf(\"day\")}return u.bind(this)(e,i)}}}));"], "mappings": ";;;;;AAAA;AAAA;AAAA,KAAC,SAAS,GAAE,GAAE;AAAC,kBAAU,OAAO,WAAS,eAAa,OAAO,SAAO,OAAO,UAAQ,EAAE,IAAE,cAAY,OAAO,UAAQ,OAAO,MAAI,OAAO,CAAC,KAAG,IAAE,eAAa,OAAO,aAAW,aAAW,KAAG,MAAM,6BAA2B,EAAE;AAAA,IAAC,EAAE,SAAM,WAAU;AAAC;AAAa,UAAI,IAAE,SAAQ,IAAE;AAAU,aAAO,SAAS,GAAE,GAAE;AAAC,YAAI,IAAE,EAAE;AAAU,UAAE,UAAQ,SAASA,IAAE;AAAC,iBAAO,KAAK,OAAO,EAAE,EAAEA,EAAC,IAAE,KAAK,MAAM,KAAK,MAAM,IAAE,KAAG,CAAC,IAAE,KAAK,MAAM,KAAK,MAAM,IAAE,IAAE,KAAGA,KAAE,EAAE;AAAA,QAAC;AAAE,YAAI,IAAE,EAAE;AAAI,UAAE,MAAI,SAASC,IAAEC,IAAE;AAAC,iBAAOD,KAAE,OAAOA,EAAC,GAAE,KAAK,OAAO,EAAE,EAAEC,EAAC,MAAI,IAAE,KAAK,IAAI,IAAED,IAAE,CAAC,IAAE,EAAE,KAAK,IAAI,EAAEA,IAAEC,EAAC;AAAA,QAAC;AAAE,YAAI,IAAE,EAAE;AAAQ,UAAE,UAAQ,SAASD,IAAEC,IAAE;AAAC,cAAIC,KAAE,KAAK,OAAO,GAAEC,KAAE,CAAC,CAACD,GAAE,EAAED,EAAC,KAAGA;AAAE,cAAGC,GAAE,EAAEF,EAAC,MAAI,GAAE;AAAC,gBAAI,IAAE,KAAK,QAAQ,IAAE;AAAE,mBAAOG,KAAE,KAAK,MAAM,IAAE,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ,KAAK,IAAE,KAAK,MAAM,IAAE,IAAE,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM,KAAK;AAAA,UAAC;AAAC,iBAAO,EAAE,KAAK,IAAI,EAAEH,IAAEC,EAAC;AAAA,QAAC;AAAA,MAAC;AAAA,IAAC,CAAE;AAAA;AAAA;", "names": ["t", "e", "i", "r", "s"]}