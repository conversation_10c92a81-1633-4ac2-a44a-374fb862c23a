package com.linkapi.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 字段映射规则实体
 * 定义具体的字段映射规则和转换逻辑
 * 
 * <AUTHOR> API Team
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("field_mapping_rule")
public class FieldMappingRule {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 配置ID
     */
    @NotNull(message = "配置ID不能为空")
    @TableField("config_id")
    private Long configId;

    /**
     * 源字段名
     */
    @NotBlank(message = "源字段名不能为空")
    @Size(max = 100, message = "源字段名长度不能超过100个字符")
    @TableField("source_field")
    private String sourceField;

    /**
     * 源字段显示名
     */
    @Size(max = 200, message = "源字段显示名长度不能超过200个字符")
    @TableField("source_field_label")
    private String sourceFieldLabel;

    /**
     * 目标字段名
     */
    @NotBlank(message = "目标字段名不能为空")
    @Size(max = 100, message = "目标字段名长度不能超过100个字符")
    @TableField("target_field")
    private String targetField;

    /**
     * 目标字段显示名
     */
    @Size(max = 200, message = "目标字段显示名长度不能超过200个字符")
    @TableField("target_field_label")
    private String targetFieldLabel;

    /**
     * 字段类型
     */
    @NotNull(message = "字段类型不能为空")
    @TableField("field_type")
    private FieldType fieldType;

    /**
     * 是否必填
     */
    @TableField("is_required")
    private Boolean isRequired;

    /**
     * 默认值
     */
    @Size(max = 500, message = "默认值长度不能超过500个字符")
    @TableField("default_value")
    private String defaultValue;

    /**
     * 转换规则类型
     */
    @TableField("transform_type")
    private TransformType transformType;

    /**
     * 转换规则表达式
     */
    @TableField("transform_expression")
    private String transformExpression;

    /**
     * 验证规则
     */
    @TableField("validation_rules")
    private String validationRules;

    /**
     * 字段描述
     */
    @Size(max = 500, message = "字段描述长度不能超过500个字符")
    @TableField("field_description")
    private String fieldDescription;

    /**
     * 排序号
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 是否启用
     */
    @TableField("is_enabled")
    private Boolean isEnabled;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 逻辑删除标志
     */
    @TableLogic
    @TableField("deleted")
    private Boolean deleted;

    /**
     * 字段类型枚举
     */
    public enum FieldType {
        /**
         * 字符串
         */
        STRING("字符串"),
        
        /**
         * 整数
         */
        INTEGER("整数"),
        
        /**
         * 小数
         */
        DECIMAL("小数"),
        
        /**
         * 布尔值
         */
        BOOLEAN("布尔值"),
        
        /**
         * 日期
         */
        DATE("日期"),
        
        /**
         * 日期时间
         */
        DATETIME("日期时间"),
        
        /**
         * 时间
         */
        TIME("时间"),
        
        /**
         * 枚举
         */
        ENUM("枚举"),
        
        /**
         * 数组
         */
        ARRAY("数组"),
        
        /**
         * 对象
         */
        OBJECT("对象"),
        
        /**
         * JSON
         */
        JSON("JSON");

        private final String description;

        FieldType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 转换规则类型枚举
     */
    public enum TransformType {
        /**
         * 无转换
         */
        NONE("无转换"),
        
        /**
         * 直接映射
         */
        DIRECT("直接映射"),
        
        /**
         * 常量值
         */
        CONSTANT("常量值"),
        
        /**
         * 表达式计算
         */
        EXPRESSION("表达式计算"),
        
        /**
         * 字典映射
         */
        DICTIONARY("字典映射"),
        
        /**
         * 函数转换
         */
        FUNCTION("函数转换"),
        
        /**
         * 条件转换
         */
        CONDITIONAL("条件转换"),
        
        /**
         * 正则替换
         */
        REGEX_REPLACE("正则替换"),
        
        /**
         * 格式化
         */
        FORMAT("格式化"),
        
        /**
         * 自定义脚本
         */
        SCRIPT("自定义脚本");

        private final String description;

        TransformType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
