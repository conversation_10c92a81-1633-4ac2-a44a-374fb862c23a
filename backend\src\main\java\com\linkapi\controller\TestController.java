package com.linkapi.controller;

import com.linkapi.common.Result;
import com.linkapi.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.web.bind.annotation.*;
import org.springframework.jdbc.core.JdbcTemplate;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 * 
 * <AUTHOR> API Team
 */
@Slf4j
@RestController
@RequestMapping("/api/test")
public class TestController {
    
    @Autowired
    private UserService userService;
    
    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> health() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "UP");
        data.put("timestamp", LocalDateTime.now());
        data.put("message", "Link API 后端服务运行正常");
        
        return Result.success("服务正常", data);
    }

    /**
     * 获取系统信息
     */
    @GetMapping("/info")
    public Result<Map<String, Object>> info() {
        Map<String, Object> data = new HashMap<>();
        data.put("application", "Link API Backend");
        data.put("version", "1.0.0");
        data.put("java.version", System.getProperty("java.version"));
        data.put("spring.profiles.active", System.getProperty("spring.profiles.active", "default"));
        data.put("timestamp", LocalDateTime.now());
        
        return Result.success("系统信息", data);
    }
    
    /**
     * 测试密码编码和验证
     */
    @GetMapping("/password/{password}")
    public Result<Map<String, Object>> testPassword(@PathVariable String password) {
        String encoded = passwordEncoder.encode(password);
        boolean matches = passwordEncoder.matches(password, encoded);
        
        Map<String, Object> data = new HashMap<>();
        data.put("original", password);
        data.put("encoded", encoded);
        data.put("matches", matches);
        
        // Test with existing password from database
        boolean verifyResult = userService.verifyPassword(password, "$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXISwKhUOKmwQDRjtHh8tMuNST2");
        data.put("verifyWithDbHash", verifyResult);
        
        return Result.success("密码测试结果", data);
    }
    
    /**
     * 查看数据库中的用户信息
     */
    @GetMapping("/user/{username}")
    public Result<Map<String, Object>> getUserInfo(@PathVariable String username) {
        com.linkapi.entity.User user = userService.findByUsernameWithRoles(username);
        
        Map<String, Object> data = new HashMap<>();
        if (user != null) {
            data.put("id", user.getId());
            data.put("username", user.getUsername());
            data.put("password", user.getPassword());
            data.put("status", user.getStatus());
            data.put("realName", user.getRealName());
            data.put("email", user.getEmail());
        } else {
            data.put("message", "用户不存在");
        }
        
        return Result.success("用户信息", data);
    }
    
    /**
     * 验证密码哈希
     */
    @PostMapping("/verify-hash")
    public Result<Map<String, Object>> verifyHash(@RequestBody Map<String, String> request) {
        String password = request.get("password");
        String hash = request.get("hash");
        
        boolean matches = passwordEncoder.matches(password, hash);
        
        Map<String, Object> result = new HashMap<>();
        result.put("password", password);
        result.put("hash", hash);
        result.put("matches", matches);
        
        return Result.success("验证结果", result);
    }
    
    /**
     * 更新admin用户密码
     */
    @PostMapping("/update-admin-password")
    public Result<String> updateAdminPassword(@RequestBody Map<String, String> request) {
        try {
            String newPassword = request.get("password");
            String encodedPassword = passwordEncoder.encode(newPassword);
            
            // 直接更新数据库中admin用户的密码
            String sql = "UPDATE users SET password = ? WHERE username = 'admin'";
            int updated = jdbcTemplate.update(sql, encodedPassword);
            
            if (updated > 0) {
                return Result.success("密码更新成功", "新密码哈希: " + encodedPassword);
            } else {
                return Result.error("用户不存在或更新失败");
            }
        } catch (Exception e) {
            return Result.error("密码更新失败: " + e.getMessage());
        }
    }
}
