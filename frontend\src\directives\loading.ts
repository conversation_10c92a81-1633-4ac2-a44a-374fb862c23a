import type { App, Directive, DirectiveBinding } from 'vue'

/**
 * 加载指令
 * 用法：
 * v-loading="loading" - 显示/隐藏加载状态
 * v-loading="{ loading: true, text: '加载中...', background: 'rgba(0,0,0,0.8)' }" - 自定义加载样式
 */
const loading: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    setupLoading(el, binding)
  },
  updated(el: HTMLElement, binding: DirectiveBinding) {
    setupLoading(el, binding)
  },
  unmounted(el: HTMLElement) {
    removeLoading(el)
  }
}

function setupLoading(el: HTMLElement, binding: DirectiveBinding) {
  const { value } = binding
  
  let isLoading = false
  let text = '加载中...'
  let background = 'rgba(255, 255, 255, 0.9)'
  let spinner = 'default'
  
  if (typeof value === 'boolean') {
    isLoading = value
  } else if (typeof value === 'object' && value !== null) {
    isLoading = value.loading || false
    text = value.text || text
    background = value.background || background
    spinner = value.spinner || spinner
  }
  
  if (isLoading) {
    showLoading(el, { text, background, spinner })
  } else {
    hideLoading(el)
  }
}

function showLoading(el: HTMLElement, options: { text: string; background: string; spinner: string }) {
  // 如果已经有加载层，先移除
  hideLoading(el)
  
  // 设置元素为相对定位
  const originalPosition = getComputedStyle(el).position
  if (originalPosition === 'static') {
    el.style.position = 'relative'
    ;(el as any).__originalPosition__ = 'static'
  }
  
  // 创建加载层
  const loadingEl = document.createElement('div')
  loadingEl.className = 'v-loading-mask'
  loadingEl.style.cssText = `
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: ${options.background};
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    z-index: 9999;
    pointer-events: auto;
  `
  
  // 创建加载动画
  const spinnerEl = document.createElement('div')
  spinnerEl.className = 'v-loading-spinner'
  
  if (options.spinner === 'dots') {
    spinnerEl.innerHTML = `
      <div style="display: flex; gap: 4px;">
        <div class="loading-dot" style="width: 8px; height: 8px; border-radius: 50%; background: #409eff; animation: loading-dot 1.4s infinite ease-in-out;"></div>
        <div class="loading-dot" style="width: 8px; height: 8px; border-radius: 50%; background: #409eff; animation: loading-dot 1.4s infinite ease-in-out; animation-delay: 0.16s;"></div>
        <div class="loading-dot" style="width: 8px; height: 8px; border-radius: 50%; background: #409eff; animation: loading-dot 1.4s infinite ease-in-out; animation-delay: 0.32s;"></div>
      </div>
    `
  } else {
    spinnerEl.innerHTML = `
      <div style="
        width: 32px;
        height: 32px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #409eff;
        border-radius: 50%;
        animation: loading-spin 1s linear infinite;
      "></div>
    `
  }
  
  // 创建加载文本
  const textEl = document.createElement('div')
  textEl.className = 'v-loading-text'
  textEl.textContent = options.text
  textEl.style.cssText = `
    margin-top: 12px;
    font-size: 14px;
    color: #666;
    text-align: center;
  `
  
  loadingEl.appendChild(spinnerEl)
  loadingEl.appendChild(textEl)
  el.appendChild(loadingEl)
  
  // 添加CSS动画
  if (!document.getElementById('v-loading-styles')) {
    const style = document.createElement('style')
    style.id = 'v-loading-styles'
    style.textContent = `
      @keyframes loading-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      @keyframes loading-dot {
        0%, 80%, 100% {
          transform: scale(0);
          opacity: 0.5;
        }
        40% {
          transform: scale(1);
          opacity: 1;
        }
      }
    `
    document.head.appendChild(style)
  }
  
  ;(el as any).__loadingElement__ = loadingEl
}

function hideLoading(el: HTMLElement) {
  const loadingEl = (el as any).__loadingElement__
  if (loadingEl && loadingEl.parentNode) {
    loadingEl.parentNode.removeChild(loadingEl)
    delete (el as any).__loadingElement__
  }
  
  // 恢复原始定位
  const originalPosition = (el as any).__originalPosition__
  if (originalPosition) {
    el.style.position = originalPosition
    delete (el as any).__originalPosition__
  }
}

function removeLoading(el: HTMLElement) {
  hideLoading(el)
}

export function setupLoadingDirective(app: App) {
  app.directive('custom-loading', loading)
}