# 开发环境配置
spring:
  # 数据源配置 - 暂时禁用
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration

# 日志配置
logging:
  level:
    com.linkapi: DEBUG
    org.springframework.web: INFO
    org.springframework.security: DEBUG
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'

# MyBatis Plus配置 - 暂时禁用
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
