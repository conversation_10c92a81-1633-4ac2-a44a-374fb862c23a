package com.linkapi.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 单据主表实体
 * 
 * <AUTHOR> API Team
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("documents")
public class Document {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 单据编号（外部单号）
     */
    @NotBlank(message = "单据编号不能为空")
    @Size(max = 100, message = "单据编号长度不能超过100个字符")
    @TableField("document_no")
    private String documentNo;

    /**
     * 单据类型
     */
    @NotNull(message = "单据类型不能为空")
    @TableField("document_type")
    private DocumentType documentType;

    /**
     * 单据日期
     */
    @NotNull(message = "单据日期不能为空")
    @TableField("document_date")
    private LocalDateTime documentDate;

    /**
     * 客户编码
     */
    @Size(max = 100, message = "客户编码长度不能超过100个字符")
    @TableField("customer_code")
    private String customerCode;

    /**
     * 客户名称
     */
    @Size(max = 200, message = "客户名称长度不能超过200个字符")
    @TableField("customer_name")
    private String customerName;

    /**
     * 仓库编码
     */
    @Size(max = 100, message = "仓库编码长度不能超过100个字符")
    @TableField("warehouse_code")
    private String warehouseCode;

    /**
     * 仓库名称
     */
    @Size(max = 200, message = "仓库名称长度不能超过200个字符")
    @TableField("warehouse_name")
    private String warehouseName;

    /**
     * 单据总金额
     */
    @DecimalMin(value = "0", message = "单据总金额不能为负数")
    @TableField("total_amount")
    private BigDecimal totalAmount;

    /**
     * 单据总数量
     */
    @TableField("total_quantity")
    private BigDecimal totalQuantity;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 状态
     */
    @NotNull(message = "状态不能为空")
    @TableField("status")
    private DocumentStatus status;

    /**
     * 金蝶单据编号
     */
    @Size(max = 100, message = "金蝶单据编号长度不能超过100个字符")
    @TableField("kingdee_document_no")
    private String kingdeeDocumentNo;

    /**
     * 错误信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * 重试次数
     */
    @Min(value = 0, message = "重试次数不能为负数")
    @TableField("retry_count")
    private Integer retryCount;

    /**
     * 最后推送时间
     */
    @TableField("last_push_time")
    private LocalDateTime lastPushTime;

    /**
     * 来源文件
     */
    @Size(max = 500, message = "来源文件路径长度不能超过500个字符")
    @TableField("source_file")
    private String sourceFile;

    /**
     * 创建时间
     */
    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    @TableField(value = "updated_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    /**
     * 创建人
     */
    @Size(max = 100, message = "创建人长度不能超过100个字符")
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 更新人
     */
    @Size(max = 100, message = "更新人长度不能超过100个字符")
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    /**
     * 逻辑删除标识
     */
    @TableLogic
    @TableField("deleted")
    private Boolean deleted;

    /**
     * 单据明细列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<DocumentDetail> details;

    /**
     * 单据类型枚举
     */
    public enum DocumentType {
        SALE_OUT("销售出库"),
        SALE_RETURN("退货"),
        PURCHASE_IN("采购入库"),
        STOCK_TRANSFER("调拨");

        private final String description;

        DocumentType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 单据状态枚举
     */
    public enum DocumentStatus {
        PENDING("待校验"),
        VALIDATED("已校验"),
        PUSHING("推送中"),
        SUCCESS("成功"),
        FAILED("失败");

        private final String description;

        DocumentStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 计算单据总金额和总数量
     */
    public void calculateTotals() {
        if (details != null && !details.isEmpty()) {
            this.totalAmount = details.stream()
                    .map(DocumentDetail::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            this.totalQuantity = details.stream()
                    .map(DocumentDetail::getQuantity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        } else {
            this.totalAmount = BigDecimal.ZERO;
            this.totalQuantity = BigDecimal.ZERO;
        }
    }

    /**
     * 检查是否可以重试
     */
    public boolean canRetry(int maxRetryCount) {
        return this.retryCount != null && this.retryCount < maxRetryCount;
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount = (this.retryCount == null ? 0 : this.retryCount) + 1;
    }

    /**
     * 重置重试次数
     */
    public void resetRetryCount() {
        this.retryCount = 0;
    }
}