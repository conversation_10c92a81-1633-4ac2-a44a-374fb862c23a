import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { login, logout, getInfo } from '@/api/user'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { resetRouter } from '@/router'
import type { LoginForm, UserInfo } from '@/types/user'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(getToken())
  const name = ref('')
  const avatar = ref('')
  const introduction = ref('')
  const roles = ref<string[]>([])
  const permissions = ref<string[]>([])
  const email = ref('')
  const phone = ref('')
  const department = ref('')
  const position = ref('')
  const lastLoginTime = ref('')
  const loginCount = ref(0)
  
  // 计算属性
  const isLoggedIn = computed(() => !!token.value)
  const isAdmin = computed(() => roles.value.includes('admin'))
  const isOperator = computed(() => roles.value.includes('operator'))
  const hasRole = computed(() => (role: string) => roles.value.includes(role))
  const hasPermission = computed(() => (permission: string) => permissions.value.includes(permission))
  const hasAnyRole = computed(() => (roleList: string[]) => roleList.some(role => roles.value.includes(role)))
  const hasAnyPermission = computed(() => (permissionList: string[]) => permissionList.some(permission => permissions.value.includes(permission)))
  
  // 用户信息对象
  const userInfo = computed((): UserInfo => ({
    name: name.value,
    avatar: avatar.value,
    introduction: introduction.value,
    roles: roles.value,
    permissions: permissions.value,
    email: email.value,
    phone: phone.value,
    department: department.value,
    position: position.value,
    lastLoginTime: lastLoginTime.value,
    loginCount: loginCount.value
  }))
  
  // 动作
  const setToken = (newToken: string) => {
    token.value = newToken
    setToken(newToken)
  }
  
  const setUserInfo = (info: Partial<UserInfo>) => {
    if (info.name !== undefined) name.value = info.name
    if (info.avatar !== undefined) avatar.value = info.avatar
    if (info.introduction !== undefined) introduction.value = info.introduction
    if (info.roles !== undefined) roles.value = info.roles
    if (info.permissions !== undefined) permissions.value = info.permissions
    if (info.email !== undefined) email.value = info.email
    if (info.phone !== undefined) phone.value = info.phone
    if (info.department !== undefined) department.value = info.department
    if (info.position !== undefined) position.value = info.position
    if (info.lastLoginTime !== undefined) lastLoginTime.value = info.lastLoginTime
    if (info.loginCount !== undefined) loginCount.value = info.loginCount
  }
  
  // 登录
  const userLogin = async (loginForm: LoginForm): Promise<void> => {
    try {
      const { username, password, captcha, rememberMe } = loginForm
      
      const response = await login({
        username: username.trim(),
        password,
        captcha,
        rememberMe
      })
      
      const { token: newToken } = response.data
      
      if (!newToken) {
        throw new Error('登录失败：未获取到有效的访问令牌')
      }
      
      setToken(newToken)
      
      ElMessage.success('登录成功')
    } catch (error: any) {
      console.error('登录失败:', error)
      throw new Error(error.message || '登录失败，请检查用户名和密码')
    }
  }
  
  // 获取用户信息
  const getUserInfo = async (): Promise<UserInfo> => {
    try {
      if (!token.value) {
        throw new Error('未找到访问令牌')
      }
      
      const response = await getInfo()
      const data = response.data
      
      if (!data) {
        throw new Error('获取用户信息失败')
      }
      
      // 验证必要字段
      if (!data.roles || data.roles.length === 0) {
        throw new Error('用户角色信息不完整')
      }
      
      setUserInfo(data)
      
      return userInfo.value
    } catch (error: any) {
      console.error('获取用户信息失败:', error)
      throw new Error(error.message || '获取用户信息失败')
    }
  }
  
  // 登出
  const userLogout = async (): Promise<void> => {
    try {
      if (token.value) {
        await logout()
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      await resetToken()
      ElMessage.success('已安全退出')
    }
  }
  
  // 重置token
  const resetToken = async (): Promise<void> => {
    token.value = ''
    name.value = ''
    avatar.value = ''
    introduction.value = ''
    roles.value = []
    permissions.value = []
    email.value = ''
    phone.value = ''
    department.value = ''
    position.value = ''
    lastLoginTime.value = ''
    loginCount.value = 0
    
    removeToken()
    resetRouter()
  }
  
  // 更新用户信息
  const updateUserInfo = async (updateData: Partial<UserInfo>): Promise<void> => {
    try {
      // 这里应该调用更新用户信息的API
      // const response = await updateUserInfo(updateData)
      
      // 暂时直接更新本地状态
      setUserInfo(updateData)
      
      ElMessage.success('用户信息更新成功')
    } catch (error: any) {
      console.error('更新用户信息失败:', error)
      throw new Error(error.message || '更新用户信息失败')
    }
  }
  
  // 修改密码
  const changePassword = async (oldPassword: string, newPassword: string): Promise<void> => {
    try {
      // 这里应该调用修改密码的API
      // await changePassword({ oldPassword, newPassword })
      
      ElMessage.success('密码修改成功，请重新登录')
      
      // 修改密码后需要重新登录
      await resetToken()
    } catch (error: any) {
      console.error('修改密码失败:', error)
      throw new Error(error.message || '修改密码失败')
    }
  }
  
  // 刷新token
  const refreshToken = async (): Promise<string> => {
    try {
      // 这里应该调用刷新token的API
      // const response = await refreshToken()
      // const { token: newToken } = response.data
      
      // 暂时返回当前token
      const newToken = token.value
      
      if (newToken) {
        setToken(newToken)
      }
      
      return newToken
    } catch (error: any) {
      console.error('刷新token失败:', error)
      await resetToken()
      throw new Error(error.message || '刷新token失败')
    }
  }
  
  // 检查权限
  const checkPermission = (permission: string | string[]): boolean => {
    if (Array.isArray(permission)) {
      return permission.some(p => permissions.value.includes(p))
    }
    return permissions.value.includes(permission)
  }
  
  // 检查角色
  const checkRole = (role: string | string[]): boolean => {
    if (Array.isArray(role)) {
      return role.some(r => roles.value.includes(r))
    }
    return roles.value.includes(role)
  }
  
  return {
    // 状态
    token,
    name,
    avatar,
    introduction,
    roles,
    permissions,
    email,
    phone,
    department,
    position,
    lastLoginTime,
    loginCount,
    
    // 计算属性
    isLoggedIn,
    isAdmin,
    isOperator,
    hasRole,
    hasPermission,
    hasAnyRole,
    hasAnyPermission,
    userInfo,
    
    // 动作
    setToken,
    setUserInfo,
    userLogin,
    getUserInfo,
    userLogout,
    resetToken,
    updateUserInfo,
    changePassword,
    refreshToken,
    checkPermission,
    checkRole
  }
})