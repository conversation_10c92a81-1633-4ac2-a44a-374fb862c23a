package com.linkapi.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.linkapi.common.BusinessException;
import com.linkapi.common.ResultCode;
import com.linkapi.config.KingdeeConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 金蝶API服务
 * 
 * <AUTHOR> API Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class KingdeeApiService {

    private final KingdeeConfig kingdeeConfig;
    private WebClient webClient;
    private final Map<String, String> sessionCache = new ConcurrentHashMap<>();
    private volatile long lastLoginTime = 0;
    private static final long SESSION_TIMEOUT = 30 * 60 * 1000; // 30分钟

    // @PostConstruct
    public void init() {
        // 验证配置
        // kingdeeConfig.validate();
        
        // 创建WebClient
        this.webClient = WebClient.builder()
                .baseUrl(kingdeeConfig.getBaseUrl())
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE)
                .defaultHeader(HttpHeaders.USER_AGENT, "Link-API/1.0")
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(10 * 1024 * 1024)) // 10MB
                .build();
        
        log.info("金蝶API服务初始化完成，基础URL: {}", kingdeeConfig.getBaseUrl());
    }

    /**
     * 登录金蝶系统
     */
    public String login() {
        // 检查会话是否有效
        String sessionId = sessionCache.get("sessionId");
        if (sessionId != null && (System.currentTimeMillis() - lastLoginTime) < SESSION_TIMEOUT) {
            log.debug("使用缓存的会话ID: {}", sessionId);
            return sessionId;
        }
        
        log.info("开始登录金蝶系统");
        
        Map<String, Object> loginData = new HashMap<>();
        loginData.put("acctid", kingdeeConfig.getDbId());
        loginData.put("username", kingdeeConfig.getUsername());
        loginData.put("password", kingdeeConfig.getPassword());
        loginData.put("lcid", kingdeeConfig.getLcid());
        
        try {
            String response = webClient.post()
                    .uri(kingdeeConfig.getEndpoints().getLogin())
                    .bodyValue(JSON.toJSONString(loginData))
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofMillis(kingdeeConfig.getTimeout()))
                    .retryWhen(Retry.fixedDelay(kingdeeConfig.getRetryTimes(), Duration.ofSeconds(1)))
                    .block();
            
            JSONObject result = JSON.parseObject(response);
            
            if (result.getBooleanValue("LoginResultType")) {
                sessionId = result.getString("SessionId");
                sessionCache.put("sessionId", sessionId);
                lastLoginTime = System.currentTimeMillis();
                
                log.info("金蝶系统登录成功，会话ID: {}", sessionId);
                return sessionId;
            } else {
                String errorMsg = result.getString("Message");
                log.error("金蝶系统登录失败: {}", errorMsg);
                throw new BusinessException(ResultCode.KINGDEE_AUTH_ERROR, "金蝶系统登录失败: " + errorMsg);
            }
            
        } catch (WebClientResponseException e) {
            log.error("金蝶API调用失败: HTTP {}, {}", e.getStatusCode(), e.getResponseBodyAsString());
            throw new BusinessException(ResultCode.KINGDEE_NETWORK_ERROR, "金蝶API网络错误: " + e.getMessage());
        } catch (Exception e) {
            log.error("金蝶系统登录异常", e);
            throw new BusinessException(ResultCode.KINGDEE_API_ERROR, "金蝶系统登录异常: " + e.getMessage());
        }
    }

    /**
     * 保存单据到金蝶
     */
    public JSONObject saveDocument(String formId, JSONObject documentData) {
        String sessionId = login();
        
        Map<String, Object> requestData = new HashMap<>();
        requestData.put("formid", formId);
        requestData.put("data", documentData);
        
        log.info("开始保存单据到金蝶，表单ID: {}", formId);
        log.debug("单据数据: {}", JSON.toJSONString(documentData));
        
        try {
            String response = webClient.post()
                    .uri(kingdeeConfig.getEndpoints().getSave())
                    .header("SessionId", sessionId)
                    .bodyValue(JSON.toJSONString(requestData))
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofMillis(kingdeeConfig.getTimeout()))
                    .retryWhen(Retry.fixedDelay(kingdeeConfig.getRetryTimes(), Duration.ofSeconds(1)))
                    .block();
            
            JSONObject result = JSON.parseObject(response);
            
            if (result.getBooleanValue("Result")) {
                log.info("单据保存成功，金蝶单据ID: {}", result.getJSONObject("ResponseStatus").getString("Id"));
                return result;
            } else {
                JSONObject responseStatus = result.getJSONObject("ResponseStatus");
                String errorMsg = responseStatus != null ? responseStatus.getString("Errors") : "未知错误";
                log.error("单据保存失败: {}", errorMsg);
                throw new BusinessException(ResultCode.KINGDEE_BUSINESS_ERROR, "单据保存失败: " + errorMsg);
            }
            
        } catch (WebClientResponseException e) {
            log.error("金蝶API调用失败: HTTP {}, {}", e.getStatusCode(), e.getResponseBodyAsString());
            
            // 如果是401错误，清除会话缓存重试
            if (e.getStatusCode().value() == 401) {
                sessionCache.clear();
                lastLoginTime = 0;
                throw new BusinessException(ResultCode.KINGDEE_AUTH_ERROR, "金蝶认证失效，请重试");
            }
            
            throw new BusinessException(ResultCode.KINGDEE_NETWORK_ERROR, "金蝶API网络错误: " + e.getMessage());
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("保存单据到金蝶异常", e);
            throw new BusinessException(ResultCode.KINGDEE_API_ERROR, "保存单据到金蝶异常: " + e.getMessage());
        }
    }

    /**
     * 查询金蝶单据
     */
    public JSONObject queryDocument(String formId, String fieldKeys, String filterString) {
        String sessionId = login();
        
        Map<String, Object> requestData = new HashMap<>();
        requestData.put("formid", formId);
        requestData.put("fieldkeys", fieldKeys);
        requestData.put("filterstring", filterString);
        requestData.put("orderstring", "");
        requestData.put("toprowcount", 100);
        requestData.put("startrow", 0);
        
        log.info("开始查询金蝶单据，表单ID: {}, 过滤条件: {}", formId, filterString);
        
        try {
            String response = webClient.post()
                    .uri(kingdeeConfig.getEndpoints().getQuery())
                    .header("SessionId", sessionId)
                    .bodyValue(JSON.toJSONString(requestData))
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofMillis(kingdeeConfig.getTimeout()))
                    .block();
            
            JSONObject result = JSON.parseObject(response);
            
            if (result.getBooleanValue("Result")) {
                log.debug("查询金蝶单据成功");
                return result;
            } else {
                String errorMsg = result.getString("ResponseStatus");
                log.error("查询金蝶单据失败: {}", errorMsg);
                throw new BusinessException(ResultCode.KINGDEE_BUSINESS_ERROR, "查询金蝶单据失败: " + errorMsg);
            }
            
        } catch (WebClientResponseException e) {
            log.error("金蝶API调用失败: HTTP {}, {}", e.getStatusCode(), e.getResponseBodyAsString());
            
            if (e.getStatusCode().value() == 401) {
                sessionCache.clear();
                lastLoginTime = 0;
                throw new BusinessException(ResultCode.KINGDEE_AUTH_ERROR, "金蝶认证失效，请重试");
            }
            
            throw new BusinessException(ResultCode.KINGDEE_NETWORK_ERROR, "金蝶API网络错误: " + e.getMessage());
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询金蝶单据异常", e);
            throw new BusinessException(ResultCode.KINGDEE_API_ERROR, "查询金蝶单据异常: " + e.getMessage());
        }
    }

    /**
     * 删除金蝶单据
     */
    public JSONObject deleteDocument(String formId, String ids) {
        String sessionId = login();
        
        Map<String, Object> requestData = new HashMap<>();
        requestData.put("formid", formId);
        requestData.put("data", ids);
        
        log.info("开始删除金蝶单据，表单ID: {}, 单据ID: {}", formId, ids);
        
        try {
            String response = webClient.post()
                    .uri(kingdeeConfig.getEndpoints().getDelete())
                    .header("SessionId", sessionId)
                    .bodyValue(JSON.toJSONString(requestData))
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofMillis(kingdeeConfig.getTimeout()))
                    .block();
            
            JSONObject result = JSON.parseObject(response);
            
            if (result.getBooleanValue("Result")) {
                log.info("删除金蝶单据成功");
                return result;
            } else {
                String errorMsg = result.getString("ResponseStatus");
                log.error("删除金蝶单据失败: {}", errorMsg);
                throw new BusinessException(ResultCode.KINGDEE_BUSINESS_ERROR, "删除金蝶单据失败: " + errorMsg);
            }
            
        } catch (WebClientResponseException e) {
            log.error("金蝶API调用失败: HTTP {}, {}", e.getStatusCode(), e.getResponseBodyAsString());
            
            if (e.getStatusCode().value() == 401) {
                sessionCache.clear();
                lastLoginTime = 0;
                throw new BusinessException(ResultCode.KINGDEE_AUTH_ERROR, "金蝶认证失效，请重试");
            }
            
            throw new BusinessException(ResultCode.KINGDEE_NETWORK_ERROR, "金蝶API网络错误: " + e.getMessage());
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除金蝶单据异常", e);
            throw new BusinessException(ResultCode.KINGDEE_API_ERROR, "删除金蝶单据异常: " + e.getMessage());
        }
    }

    /**
     * 测试金蝶连接
     */
    public boolean testConnection() {
        try {
            login();
            return true;
        } catch (Exception e) {
            log.error("金蝶连接测试失败", e);
            return false;
        }
    }

    /**
     * 清除会话缓存
     */
    public void clearSession() {
        sessionCache.clear();
        lastLoginTime = 0;
        log.info("金蝶会话缓存已清除");
    }

    /**
     * 获取会话状态
     */
    public Map<String, Object> getSessionStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("hasSession", sessionCache.containsKey("sessionId"));
        status.put("lastLoginTime", lastLoginTime);
        status.put("sessionTimeout", SESSION_TIMEOUT);
        status.put("remainingTime", Math.max(0, SESSION_TIMEOUT - (System.currentTimeMillis() - lastLoginTime)));
        return status;
    }
}