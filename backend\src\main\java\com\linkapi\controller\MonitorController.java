package com.linkapi.controller;

import com.linkapi.common.Result;
import com.linkapi.service.MonitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 系统监控控制器
 * 提供系统性能监控、服务状态检查等功能
 */
@RestController
@RequestMapping("/api/monitor")
@CrossOrigin(origins = "*")
public class MonitorController {

    @Autowired
    private MonitorService monitorService;

    /**
     * 获取系统信息
     * @return 系统基本信息和性能指标
     */
    @GetMapping("/system")
    public Result<Map<String, Object>> getSystemInfo() {
        try {
            Map<String, Object> systemInfo = monitorService.getSystemInfo();
            return Result.success(systemInfo);
        } catch (Exception e) {
            return Result.error("获取系统信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取应用信息
     * @return 应用运行状态和统计信息
     */
    @GetMapping("/application")
    public Result<Map<String, Object>> getApplicationInfo() {
        try {
            Map<String, Object> appInfo = monitorService.getApplicationInfo();
            return Result.success(appInfo);
        } catch (Exception e) {
            return Result.error("获取应用信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取服务状态列表
     * @return 各个服务的运行状态
     */
    @GetMapping("/services")
    public Result<Object> getServiceStatus() {
        try {
            Object serviceStatus = monitorService.getServiceStatus();
            return Result.success(serviceStatus);
        } catch (Exception e) {
            return Result.error("获取服务状态失败: " + e.getMessage());
        }
    }

    /**
     * 检查指定服务状态
     * @param serviceName 服务名称
     * @return 服务检查结果
     */
    @PostMapping("/services/{serviceName}/check")
    public Result<Map<String, Object>> checkService(@PathVariable String serviceName) {
        try {
            Map<String, Object> result = monitorService.checkService(serviceName);
            return Result.success(result);
        } catch (Exception e) {
            return Result.error("检查服务失败: " + e.getMessage());
        }
    }

    /**
     * 获取实时性能数据
     * @param type 数据类型 (cpu, memory, network)
     * @return 实时性能数据
     */
    @GetMapping("/realtime/{type}")
    public Result<Object> getRealtimeData(@PathVariable String type) {
        try {
            Object data = monitorService.getRealtimeData(type);
            return Result.success(data);
        } catch (Exception e) {
            return Result.error("获取实时数据失败: " + e.getMessage());
        }
    }
}