package com.linkapi.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.linkapi.entity.CustomerMapping;
import com.linkapi.entity.ProductMapping;
import com.linkapi.entity.WarehouseMapping;
import com.linkapi.mapper.CustomerMappingMapper;
import com.linkapi.mapper.ProductMappingMapper;
import com.linkapi.mapper.WarehouseMappingMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 映射服务类
 * 
 * <AUTHOR> API Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MappingService {

    private final ProductMappingMapper productMappingMapper;
    private final CustomerMappingMapper customerMappingMapper;
    private final WarehouseMappingMapper warehouseMappingMapper;

    // ==================== 产品映射 ====================

    /**
     * 分页查询产品映射
     */
    public IPage<ProductMapping> getProductMappingPage(int pageNum, int pageSize, String sourceCode, String targetCode) {
        Page<ProductMapping> page = new Page<>(pageNum, pageSize);
        QueryWrapper<ProductMapping> queryWrapper = new QueryWrapper<>();
        
        if (sourceCode != null && !sourceCode.trim().isEmpty()) {
            queryWrapper.like("external_code", sourceCode);
        }

        if (targetCode != null && !targetCode.trim().isEmpty()) {
            queryWrapper.like("kingdee_code", targetCode);
        }
        
        queryWrapper.orderByDesc("created_time");
        
        return productMappingMapper.selectPage(page, queryWrapper);
    }

    /**
     * 根据源编码查找产品映射
     */
    public ProductMapping getProductMappingBySourceCode(String sourceCode) {
        QueryWrapper<ProductMapping> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("external_code", sourceCode)
                   .eq("status", ProductMapping.MappingStatus.ACTIVE);
        return productMappingMapper.selectOne(queryWrapper);
    }

    /**
     * 创建产品映射
     */
    @Transactional
    public ProductMapping createProductMapping(ProductMapping mapping) {
        mapping.setCreatedTime(LocalDateTime.now());
        mapping.setUpdatedTime(LocalDateTime.now());
        mapping.setStatus(ProductMapping.MappingStatus.ACTIVE);
        productMappingMapper.insert(mapping);
        return mapping;
    }

    /**
     * 更新产品映射
     */
    @Transactional
    public ProductMapping updateProductMapping(ProductMapping mapping) {
        mapping.setUpdatedTime(LocalDateTime.now());
        productMappingMapper.updateById(mapping);
        return mapping;
    }

    /**
     * 删除产品映射
     */
    @Transactional
    public boolean deleteProductMapping(Long id) {
        return productMappingMapper.deleteById(id) > 0;
    }

    // ==================== 客户映射 ====================

    /**
     * 分页查询客户映射
     */
    public IPage<CustomerMapping> getCustomerMappingPage(int pageNum, int pageSize, String sourceCode, String targetCode) {
        Page<CustomerMapping> page = new Page<>(pageNum, pageSize);
        QueryWrapper<CustomerMapping> queryWrapper = new QueryWrapper<>();
        
        if (sourceCode != null && !sourceCode.trim().isEmpty()) {
            queryWrapper.like("source_code", sourceCode);
        }
        
        if (targetCode != null && !targetCode.trim().isEmpty()) {
            queryWrapper.like("target_code", targetCode);
        }
        
        queryWrapper.orderByDesc("created_time");
        
        return customerMappingMapper.selectPage(page, queryWrapper);
    }

    /**
     * 根据源编码查找客户映射
     */
    public CustomerMapping getCustomerMappingBySourceCode(String sourceCode, String sourceSystem, String targetSystem) {
        QueryWrapper<CustomerMapping> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("source_code", sourceCode)
                   .eq("source_system", sourceSystem)
                   .eq("target_system", targetSystem)
                   .eq("status", 1);
        return customerMappingMapper.selectOne(queryWrapper);
    }

    /**
     * 创建客户映射
     */
    @Transactional
    public CustomerMapping createCustomerMapping(CustomerMapping mapping) {
        mapping.setCreatedTime(LocalDateTime.now());
        mapping.setUpdatedTime(LocalDateTime.now());
        mapping.setStatus(CustomerMapping.MappingStatus.ACTIVE);
        customerMappingMapper.insert(mapping);
        return mapping;
    }

    /**
     * 更新客户映射
     */
    @Transactional
    public CustomerMapping updateCustomerMapping(CustomerMapping mapping) {
        mapping.setUpdatedTime(LocalDateTime.now());
        customerMappingMapper.updateById(mapping);
        return mapping;
    }

    /**
     * 删除客户映射
     */
    @Transactional
    public boolean deleteCustomerMapping(Long id) {
        return customerMappingMapper.deleteById(id) > 0;
    }

    // ==================== 仓库映射 ====================

    /**
     * 分页查询仓库映射
     */
    public IPage<WarehouseMapping> getWarehouseMappingPage(int pageNum, int pageSize, String sourceCode, String targetCode) {
        Page<WarehouseMapping> page = new Page<>(pageNum, pageSize);
        QueryWrapper<WarehouseMapping> queryWrapper = new QueryWrapper<>();
        
        if (sourceCode != null && !sourceCode.trim().isEmpty()) {
            queryWrapper.like("source_code", sourceCode);
        }
        
        if (targetCode != null && !targetCode.trim().isEmpty()) {
            queryWrapper.like("target_code", targetCode);
        }
        
        queryWrapper.orderByDesc("created_time");
        
        return warehouseMappingMapper.selectPage(page, queryWrapper);
    }

    /**
     * 根据源编码查找仓库映射
     */
    public WarehouseMapping getWarehouseMappingBySourceCode(String sourceCode, String sourceSystem, String targetSystem) {
        QueryWrapper<WarehouseMapping> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("source_code", sourceCode)
                   .eq("source_system", sourceSystem)
                   .eq("target_system", targetSystem)
                   .eq("status", 1);
        return warehouseMappingMapper.selectOne(queryWrapper);
    }

    /**
     * 创建仓库映射
     */
    @Transactional
    public WarehouseMapping createWarehouseMapping(WarehouseMapping mapping) {
        mapping.setCreatedTime(LocalDateTime.now());
        mapping.setUpdatedTime(LocalDateTime.now());
        mapping.setStatus(WarehouseMapping.MappingStatus.ACTIVE);
        warehouseMappingMapper.insert(mapping);
        return mapping;
    }

    /**
     * 更新仓库映射
     */
    @Transactional
    public WarehouseMapping updateWarehouseMapping(WarehouseMapping mapping) {
        mapping.setUpdatedTime(LocalDateTime.now());
        warehouseMappingMapper.updateById(mapping);
        return mapping;
    }

    /**
     * 删除仓库映射
     */
    @Transactional
    public boolean deleteWarehouseMapping(Long id) {
        return warehouseMappingMapper.deleteById(id) > 0;
    }

    // ==================== 通用方法 ====================

    /**
     * 获取映射统计信息
     */
    public MappingStatistics getMappingStatistics() {
        Long productCount = productMappingMapper.selectCount(new QueryWrapper<ProductMapping>().eq("status", 1));
        Long customerCount = customerMappingMapper.selectCount(new QueryWrapper<CustomerMapping>().eq("status", 1));
        Long warehouseCount = warehouseMappingMapper.selectCount(new QueryWrapper<WarehouseMapping>().eq("status", 1));
        
        return new MappingStatistics(productCount, customerCount, warehouseCount);
    }

    /**
     * 映射统计信息内部类
     */
    public static class MappingStatistics {
        private final Long productCount;
        private final Long customerCount;
        private final Long warehouseCount;

        public MappingStatistics(Long productCount, Long customerCount, Long warehouseCount) {
            this.productCount = productCount;
            this.customerCount = customerCount;
            this.warehouseCount = warehouseCount;
        }

        // Getters
        public Long getProductCount() { return productCount; }
        public Long getCustomerCount() { return customerCount; }
        public Long getWarehouseCount() { return warehouseCount; }
    }
}
